plugins {
    id "com.android.application"
    id "com.google.gms.google-services"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.cocoai.dev"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.cocoai.dev"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    // 签名配置
    signingConfigs {
        debug {
            // 使用Flutter默认调试签名
        }
        release {
            // 签名文件路径
            storeFile file('./amor.jks')
            // 签名密码
            storePassword 'Aa335577'
            // 别名
            keyAlias 'amor'
            // 别名密码
            keyPassword 'Aa335577'
            v1SigningEnabled true
            v2SigningEnabled true
        }

    }
    // 打包
    buildTypes {
        debug {
            minifyEnabled false
            zipAlignEnabled true
            shrinkResources false
            signingConfig signingConfigs.debug
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }
        release {
            // 是否zip对齐
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources true
            //混淆开关
            minifyEnabled true
            proguardFiles 'proguard-rules.pro'
            signingConfig signingConfigs.release
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.facebook.android:facebook-login:latest.release'
    implementation 'com.facebook.android:facebook-android-sdk:15.0.1'
    //adjust  添加 Google Play 服务
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    //adjust Google Play 推荐 API 
    implementation 'com.android.installreferrer:installreferrer:2.2'
}
