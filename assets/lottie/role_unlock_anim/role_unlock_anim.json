{"v": "5.9.4", "fr": 25, "ip": 0, "op": 33, "w": 750, "h": 1624, "nm": "合成 1", "ddd": 0, "assets": [{"id": "image_0", "w": 173, "h": 136, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 108, "h": 125, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 173, "h": 215, "u": "images/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 513, "h": 513, "u": "images/", "p": "img_3.png", "e": 0}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "Group 1000004871.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [5]}, {"t": 24, "s": [-5]}], "ix": 10, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.06;\n    frequency = 2;\n    decay = 6;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [375, 900, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [86.5, 68, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [153, 153, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 16, "op": 35, "st": -10, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Vector 31 (<PERSON><PERSON>).png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 27, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [375, 767, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 26, "s": [375, 722, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [54, 62.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [153, 153, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 16, "op": 35, "st": -10, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "Group 1000004872.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [100]}, {"t": 16, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [375, 1086, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 11, "s": [375, 862, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [86.5, 107.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [50, 50, 100]}, {"t": 11, "s": [153, 153, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 38, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Group 352.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 27, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [375, 1080, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 11, "s": [375, 856, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256.5, 256.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [50, 50, 100]}, {"t": 11, "s": [153, 153, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 38, "st": 0, "bm": 0}], "markers": [], "metadata": {"filename": "无标题项目.aep", "customProps": {"自定义属性 1": 1}}}