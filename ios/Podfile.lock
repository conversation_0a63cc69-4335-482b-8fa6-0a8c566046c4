PODS:
  - Adjust (5.0.1):
    - Adjust/Adjust (= 5.0.1)
  - Adjust/Adjust (5.0.1):
    - AdjustSignature (~> 3.18)
  - adjust_sdk (5.0.2):
    - Adjust (= 5.0.1)
    - Flutter
  - AdjustSignature (3.20.2)
  - advertising_id (0.0.1):
    - Flutter
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - device_uuid (0.0.1):
    - Flutter
    - XYUUID
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - FBAEMKit (17.1.0):
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit (17.1.0):
    - FBAEMKit (= 17.1.0)
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit_Basics (17.1.0)
  - FBSDKLoginKit (17.1.0):
    - FBSDKCoreKit (= 17.1.0)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Analytics (11.2.0):
    - Firebase/Core
  - Firebase/Auth (11.2.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.2.0)
  - Firebase/Core (11.2.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.2.0)
  - Firebase/CoreOnly (11.2.0):
    - FirebaseCore (= 11.2.0)
  - Firebase/Messaging (11.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.2.0)
  - firebase_analytics (11.3.3):
    - Firebase/Analytics (= 11.2.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.3.1):
    - Firebase/Auth (= 11.2.0)
    - firebase_core
    - Flutter
  - firebase_core (3.6.0):
    - Firebase/CoreOnly (= 11.2.0)
    - Flutter
  - firebase_messaging (15.1.3):
    - Firebase/Messaging (= 11.2.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.2.0):
    - FirebaseAnalytics/AdIdSupport (= 11.2.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.3.0)
  - FirebaseAuth (11.2.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (~> 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.3.0)
  - FirebaseCore (11.2.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.3.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.3.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.1.1):
    - FBSDKLoginKit (~> 17.1.0)
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_keychain (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.2.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.2.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.2.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities (8.0.2):
    - GoogleUtilities/AppDelegateSwizzler (= 8.0.2)
    - GoogleUtilities/Environment (= 8.0.2)
    - GoogleUtilities/Logger (= 8.0.2)
    - GoogleUtilities/MethodSwizzler (= 8.0.2)
    - GoogleUtilities/Network (= 8.0.2)
    - "GoogleUtilities/NSData+zlib (= 8.0.2)"
    - GoogleUtilities/Privacy (= 8.0.2)
    - GoogleUtilities/Reachability (= 8.0.2)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.0.2)
    - GoogleUtilities/UserDefaults (= 8.0.2)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.0.2):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - in_app_review (0.2.0):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (100.0.0)
  - record_darwin (1.0.0):
    - Flutter
    - FlutterMacOS
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - XYUUID (1.0.0)

DEPENDENCIES:
  - adjust_sdk (from `.symlinks/plugins/adjust_sdk/ios`)
  - advertising_id (from `.symlinks/plugins/advertising_id/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - device_uuid (from `.symlinks/plugins/device_uuid/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Firebase/Messaging
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_keychain (from `.symlinks/plugins/flutter_keychain/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - GoogleUtilities
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Adjust
    - AdjustSignature
    - AppAuth
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - RecaptchaInterop
    - SDWebImage
    - SwiftyGif
    - TOCropViewController
    - XYUUID

EXTERNAL SOURCES:
  adjust_sdk:
    :path: ".symlinks/plugins/adjust_sdk/ios"
  advertising_id:
    :path: ".symlinks/plugins/advertising_id/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  device_uuid:
    :path: ".symlinks/plugins/device_uuid/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_keychain:
    :path: ".symlinks/plugins/flutter_keychain/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Adjust: f27daaa40d0df5550b89dd428cd8545d812e1f6d
  adjust_sdk: 229b9e59a81371ad3248475ad6daa6cb7e4c8349
  AdjustSignature: f42d8c64cda8361ff5b5a31e4536d82076be4306
  advertising_id: ea8a9b898196a0673bad1b0fe6c50541d9295d02
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  audio_session: 088d2483ebd1dc43f51d253d4a1c517d9a2e7207
  connectivity_plus: 4c41c08fc6d7c91f63bc7aec70ffe3730b04f563
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  device_uuid: 1673ecd99e9ae7a1508120f378158c85ecd708a6
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  FBAEMKit: cb719c53575a3be86ea873279f30d6a2c4e15881
  FBSDKCoreKit: ecdb980a24633ccb012700299ceb16d0235e14d2
  FBSDKCoreKit_Basics: 045101c4a9ef10c845347424d73a29aae02c3e43
  FBSDKLoginKit: 69eb59b2f839aba635616df6e422acd0ca88030a
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Firebase: 98e6bf5278170668a7983e12971a66b2cd57fc8c
  firebase_analytics: fbc57838bdb94eef1e0ff504f127d974ff2981ad
  firebase_auth: 0c77e299a8f2d1c74d1b1f6b78b3d4d802c19f47
  firebase_core: 2bedc3136ec7c7b8561c6123ed0239387b53f2af
  firebase_messaging: 15d114e1a41fc31e4fbabcd48d765a19eec94a38
  FirebaseAnalytics: c36efd5710c60c17558650fa58c2066eca7e9265
  FirebaseAppCheckInterop: 7789a8adfb09e905ce02a76540b94b059029ea81
  FirebaseAuth: 2a198b8cdbbbd457f08d74df7040feb0a0e7777a
  FirebaseAuthInterop: c453b7ba7c49b88b2f519bb8d2e29edf7ada4a2a
  FirebaseCore: a282032ae9295c795714ded2ec9c522fc237f8da
  FirebaseCoreExtension: 30bb063476ef66cd46925243d64ad8b2c8ac3264
  FirebaseCoreInternal: ac26d09a70c730e497936430af4e60fb0c68ec4e
  FirebaseInstallations: 58cf94dabf1e2bb2fa87725a9be5c2249171cda0
  FirebaseMessaging: c9ec7b90c399c7a6100297e9d16f8a27fc7f7152
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 48be46091bd1a5cc5a66055f1228a9d99a28bfaa
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_keychain: 01aabf894ffe8b01adfda1d9df21c210c1b4b452
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_native_splash: edf599c81f74d093a4daf8e17bd7a018854bc778
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleAppMeasurement: 76d4f8b36b03bd8381fa9a7fe2cc7f99c0a2e93a
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: 37d40f62177c101ff4c164906d259ea2c3aa70cf
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: 8c3b0b3eb1b0f04efbff401c3de6266d4258d433
  in_app_review: 318597b3a06c22bb46dc454d56828c85f444f99d
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  record_darwin: df0a677188e5fed18472550298e675f19ddaffbe
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite_darwin: a553b1fd6fe66f53bbb0fe5b4f5bab93f08d7a13
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4
  XYUUID: 9a2483143d14fc5204b619042ca65073df9fbd88

PODFILE CHECKSUM: 08605acf17a21a95f144fc1959214bb540b8ed08

COCOAPODS: 1.14.3
