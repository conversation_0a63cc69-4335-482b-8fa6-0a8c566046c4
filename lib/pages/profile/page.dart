import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:amor_app/pages/profile/widgets/profile_menu.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'widgets/profile_header.dart';
import 'widgets/profile_recharge.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with RouteAware {
  String pushNextRoute = '';
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 路由订阅
    AppRouteObserver().routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

// 当Pop到该页面时：
  @override
  void didPopNext() {
    super.didPopNext();
    if (TabsController.to.page == 3) {
      UserService.to.getUserInfo();
    }
  }

  @override
  void dispose() {
    /// 取消路由订阅
    AppRouteObserver().routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.mainBg,
      body: GetBuilder<ProfilePageController>(
        builder: (controller) => Column(
          children: [
            ProfileHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ProfileRechargeWidget(),
                    // 20.verticalSpaceFromWidth,
                    ProfileMenu(),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
