import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'sub_pages/purchase/page.dart';

class ProfilePageController extends GetxController {
  static ProfilePageController get to => Get.find();
  bool showGemsTip = false;

  showRecharge() async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    Analytics().logEvent(Analytics.clickHot, screen: Analytics.pageMine);
    Analytics().logEvent(
      Analytics.view,
      sourceEvent: Analytics.clickHot,
      sourceChar: null,
      sourceScreen: Analytics.pageMine,
      screen: Analytics.pageHot,
    );
    PurchaseSheet.show(
        buyType: UserService.to.isVip ? 1 : 0,
        page: ReportUtil.profile,
        source: Analytics.devsucvipcremevip);
  }

  //展示宝石提示弹窗
  showGemsDialog() async {
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (showGemsTip == true) {
        Get.back();
      }
    });
    showGemsTip = true;
    await Get.dialog(ProfileDialog.gemsTipWidget(),
        barrierColor: Colors.transparent, barrierDismissible: false);
    showGemsTip = false;
  }

  //订阅成功后展示动画
  showSubscribeSuccess() async {
    if (AppService.sp.get(spSubscriptionAnimation) == true) {
      AppService.sp.set(spSubscriptionAnimation, false);
      GemsAnimation.show();
    }
  }

  //切换自动翻译
  autoTraSwitch() async {
    if (UserService.to.isVip || UserService.to.isAutoTranslation == true) {
      bool result = await ProfileApis.profileUpdate(params: {
        'autoTranslate': !UserService.to.isAutoTranslation,
        'targetLanguage': (Get.deviceLocale ?? const Locale('en', 'US')).toString(),
        'sourceLanguage': 'en_US'
      });
      if (result == true) {
        UserService.to.userinfo.value.autoTranslate = !UserService.to.isAutoTranslation;
        UserService.to.userinfo.refresh();
      }
    } else {
      showRecharge();
    }
  }
}
