// import 'package:amor_app/common/utils/utils.dart';
// import 'package:applovin_max/applovin_max.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class TestAdPage extends GetView<TestAdPageController> {
  const TestAdPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("AppLovin MAX Demo"),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              // AppLovinMAX.showMediationDebugger();
            },
            child: const Text("Debugger"),
          ),
          ElevatedButton(
            onPressed: () {
              // ApplovinUtil.loadInterstitialAd(ApplovinUtil.interstitialAdUnitIdClone);
            },
            child: const Text("Interstitial"),
          ),
          ElevatedButton(
            onPressed: () {
              // ApplovinUtil.loadRewardedAd(ApplovinUtil.rewardedAdUnitIdTask1);
            },
            child: const Text("Rewarded"),
          ),
          ElevatedButton(
            onPressed: () async {
              // bool adIsReady = await ApplovinUtil.appOpenAdIsReady();
              // if (adIsReady) {
              //   ApplovinUtil.showAppOpenAd();
              //   return;
              // }
            },
            child: const Text("app open"),
          ),
        ],
      ),
    );
  }
}
