import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final packageModel =
        PackageInfoModel.fromJson(Get.find<SPService>().get(spAppInfo) as Map<String, dynamic>);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        leading: const CustomBackButton(),
        title: Text('About us'.tr),
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Image.asset(
              Assets.assetsImagesInnerTopBg,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: CommonUtil.appBarHeight() + CommonUtil.statusBarHeight(context) + 50.w,
            left: 0,
            right: 0,
            bottom: 0,
            child: Column(
              children: [
                Image.asset(
                  Assets.assetsImagesAboutusLogo,
                  width: 100.w,
                ),
                16.verticalSpaceFromWidth,
                Text(
                  'Amor AI 2025',
                  style:
                      TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w600),
                ),
                30.verticalSpaceFromWidth,
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                  child: Column(
                    children: [
                      itemWidget(
                        onTap: () {},
                        title: 'Current Version'.tr,
                        icon: Assets.assetsImagesAboutusVersionIcon,
                        subTitle: "v ${packageModel.version ?? ''}",
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 46.w, right: 16.w),
                        child: Divider(
                          height: 0.5,
                          color: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                      itemWidget(
                        onTap: () {
                          Get.toNamed(Routes.webview, parameters: {
                            'url': AppService.configModel.userAgreement ?? (defaultUserAgreement)
                          });
                        },
                        title: 'User Terms & Conditions'.tr,
                        icon: Assets.assetsImagesAboutusTermsIcon,
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 46.w, right: 16.w),
                        child: Divider(
                          height: 0.5,
                          color: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                      itemWidget(
                        onTap: () {
                          Get.toNamed(Routes.webview, parameters: {
                            'url': AppService.configModel.privacyPolicy ?? defaultPrivacyPolicy
                          });
                        },
                        title: 'Privacy Policy'.tr,
                        icon: Assets.assetsImagesAboutusPrivacyIcon,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  itemWidget(
      {required VoidCallback onTap,
      required String title,
      required String icon,
      String? subTitle}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        height: 58.w,
        child: Row(
          children: [
            Image.asset(icon, width: 20.w),
            10.horizontalSpace,
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            const Spacer(),
            subTitle != null
                ? Text(
                    subTitle,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                  )
                : Icon(
                    Icons.arrow_forward_ios,
                    size: 15.w,
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
          ],
        ),
      ),
    );
  }
}
