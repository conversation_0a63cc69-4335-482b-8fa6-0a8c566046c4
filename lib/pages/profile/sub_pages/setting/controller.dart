import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/login/page.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class SettingPageController extends GetxController {
  var notificationValue = false.obs;
  var hipticsValue = false.obs;

  @override
  void onInit() async {
    super.onInit();
    hipticsValue.value = (Get.find<SPService>().get(spHipticsValue) ?? true) as bool;
    notificationValue.value = await PermissionHelper.getPermissionStatus(Permission.notification,
        showToast: false, request: false);
  }

  hipticsValueChange() {
    hipticsValue.toggle();
    Get.find<SPService>().set(spHipticsValue, hipticsValue.value);
    ReportUtil.reportEvents(page: ReportUtil.setting, action: ReportUtil.hiptics);
    if (hipticsValue.value == true) {
      HapticFeedback.selectionClick();
    }
  }

  notificationValueChange() async {
    if (notificationValue.value == false) {
      //请求通知权限
      bool result =
          await PermissionHelper.getPermissionStatus(Permission.notification, showToast: false);
      if (result == false) {
        openAppSettings();
      } else {
        notificationValue.value = true;
      }
    } else {
      notificationValue.value = false;
    }
  }

  logout() async {
    ProfileDialog.logoutDialog();
    ReportUtil.reportEvents(page: ReportUtil.setting, action: ReportUtil.logout);
  }

  //切换账号、设备登录
  showUserDialog() async {
    bool? result = await ProfileDialog.userDialog(
      title: 'Switch Account'.tr,
      content: 'Switch to a different account?'.tr,
      confirmTitle: 'No'.tr,
      cancelTitle: 'Yes'.tr,
    );
    if (result == false) {
      ReportUtil.reportEvents(page: ReportUtil.profile, action: ReportUtil.actionSwitch);
      Login.showLogin();
    }
  }
}
