import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/setting/controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingPage extends GetView<SettingPageController> {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Settings'.tr),
        leading: const CustomBackButton(),
      ),
      body: Column(
        children: [
          22.verticalSpaceFromWidth,
          bgContainer(
            child: userItemWidget(
              onTap: () {
                Get.toNamed(Routes.aboutus);
              },
              title: 'About us'.tr,
            ),
          ),
          20.verticalSpaceFromWidth,
          bgContainer(
            child: Column(
              children: [
                Obx(
                  () => switchItemWidget(
                    onChanged: (p0) {
                      controller.notificationValueChange();
                    },
                    title: 'Notification'.tr,
                    switchValue: controller.notificationValue.value,
                    subTitle: 'Allow Amors to send you messages'.trArgs(['Amors']),
                  ),
                ),
                Divider(height: 0.5, color: Colors.white.withValues(alpha: 0.2)),
                Obx(
                  () => switchItemWidget(
                      onChanged: (p0) {
                        controller.hipticsValueChange();
                      },
                      title: 'Hiptics'.tr,
                      switchValue: controller.hipticsValue.value),
                ),
              ],
            ),
          ),
          20.verticalSpaceFromWidth,
          bgContainer(
            child: Column(
              children: [
                userItemWidget(
                  onTap: () {
                    controller.logout();
                  },
                  title: 'Log Out'.tr,
                ),
                Divider(height: 0.5, color: Colors.white.withValues(alpha: 0.2)),
                userItemWidget(
                  onTap: () {
                    Get.toNamed(Routes.deleteAccount);
                  },
                  title: 'Delete Account'.tr,
                ),
                Divider(height: 0.5, color: Colors.white.withValues(alpha: 0.2)),
                userItemWidget(
                  onTap: () {
                    controller.showUserDialog();
                  },
                  title: 'Switch Accounts'.tr,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget bgContainer({required Widget child, double? height}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.white.withValues(alpha: 0.05),
      ),
      child: child,
    );
  }

  Widget switchItemWidget(
      {required Function(bool) onChanged,
      required String title,
      required bool switchValue,
      String? subTitle}) {
    return SizedBox(
      height: 68.w,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: TextStyle(fontSize: 16.sp, color: Colors.white),
                ),
                if (subTitle != null)
                  Text(
                    subTitle,
                    style: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                  ),
              ],
            ),
          ),
          CupertinoSwitch(
            value: switchValue,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  userItemWidget({
    required VoidCallback onTap,
    required String title,
  }) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        height: 60.w,
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 15.w,
              color: Colors.white.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }
}
