import 'package:amor_app/common/apis/login_api/login_api.dart';
import 'package:amor_app/common/models/login_models/register_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'widgets/image.dart';
import 'widgets/tag.dart';

class PreferencesPageController extends GetxController {
  Duration animatedDuration = const Duration(milliseconds: 200);
  List<RegisterModel> questionList = [];
  var stepIndex = 0.obs;
  var pageList = [].obs;
  var selectedOptions = [].obs;
  //标签切换
  var tagSwitchType = [].obs;
  @override
  void onReady() {
    super.onReady();
    getRegisterInfo();
  }

  //获取注册填写的信息
  getRegisterInfo() async {
    questionList = await LoginApi.registerInfoGet();
    pageList.addAll(questionList.map((e) {
      List optionIds = [];
      for (OptionRespsModel element in e.optionResps ?? []) {
        optionIds.addAllIf(element.selected == true, [element.optionId]);
      }
      selectedOptions.add({'optionIds': optionIds, 'questionId': e.questionId});
      if (e.questionType == 'tag') {
        return PreferencesTagWidget(model: e, pageIndex: questionList.indexOf(e));
      }
      if (e.questionType == 'image') {
        return PreferencesImageWidget(pageIndex: questionList.indexOf(e), model: e);
      }
      return Container();
    }).toList());
  }

  //提交问题
  questionCommit() async {
    selectedOptions.removeWhere((element) => (element['optionIds'] as List).isEmpty);
    Map<String, dynamic>? result = await LoginApi.questionCommit(questionItemReqs: selectedOptions);
    if (result == null) {
      return;
    }
    await Get.find<SPService>().remove(spExposureModel);
    if (Get.isRegistered<AmorsPageController>() == true) {
      AmorsPageController.to.selectedCategory(0);
    }
    Get.back();
  }

  //选中和取消选中
  setSelectedOptions({required int optionId, required bool single}) {
    HapticFeedback.mediumImpact();
    List optionIds = selectedOptions.elementAt(stepIndex.value)['optionIds'];
    //单选
    if (single) {
      optionIds.clear();
      if (stepIndex.value < pageList.length - 1) {
        Future.delayed(animatedDuration, () {
          stepNext();
        });
      }
    }
    if (optionIds.contains(optionId)) {
      optionIds.remove(optionId);
    } else {
      optionIds.add(optionId);
    }
    selectedOptions.elementAt(stepIndex.value)['optionIds'] = optionIds;
    selectedOptions.refresh();
  }

  //清除选中的nsfw、bdsm标签
  clearSpecialTags({required RegisterModel tagWidgetMdel}) {
    List optionIds = selectedOptions.elementAt(stepIndex.value)['optionIds'];
    for (OptionRespsModel element in tagWidgetMdel.optionResps ?? []) {
      if (element.subContent != null && optionIds.contains(element.optionId)) {
        if ((element.subContent == 'nsfw' && tagSwitchType.contains(1) == false) ||
            (element.subContent == 'bdsm' && tagSwitchType.contains(2) == false)) {
          optionIds.remove(element.optionId);
        }
      }
    }
    selectedOptions.elementAt(stepIndex.value)['optionIds'] = optionIds;
    selectedOptions.refresh();
  }

  //下一步
  stepNext() async {
    ++stepIndex.value;
  }
}
