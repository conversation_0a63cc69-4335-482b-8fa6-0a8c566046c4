import 'package:amor_app/common/models/login_models/register_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/preferences/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PreferencesTagWidget extends GetView<PreferencesPageController> {
  final RegisterModel model;
  final int pageIndex;
  const PreferencesTagWidget({super.key, required this.model, required this.pageIndex});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnimatedOpacity(
        opacity: controller.stepIndex.value == pageIndex ? 1 : 0,
        duration: controller.animatedDuration,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: REdgeInsets.only(left: 10),
                child: CustomBackButton(
                    back: () => pageIndex == 0 ? Get.back() : --controller.stepIndex.value),
              ),
            ),
            20.verticalSpaceFromWidth,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w),
              child: Text(
                'Q ${pageIndex + 1}/${controller.questionList.length}: ${model.title}',
                style: TextStyle(fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            24.verticalSpaceFromWidth,
            if (AppService.audit == false)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 25.w),
                child: Obx(
                  () => TagSwitchWidget(
                    selected: controller.tagSwitchType.isEmpty ? [] : controller.tagSwitchType,
                    selectedCallBack: (index) {
                      if (controller.tagSwitchType.contains(index)) {
                        controller.tagSwitchType.remove(index);
                        //取消nsfw、bdsm时清除选中的相关tag
                        controller.clearSpecialTags(tagWidgetMdel: model);
                      } else {
                        controller.tagSwitchType.add(index);
                      }
                    },
                  ),
                ),
              ),
            14.verticalSpaceFromWidth,
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 25.w),
                child: tagWidget(),
              ),
            ),
            AnimatedOpacity(
              opacity:
                  (controller.selectedOptions.elementAt(pageIndex)['optionIds'] as List).isNotEmpty
                      ? 1
                      : 0.5,
              duration: controller.animatedDuration,
              child: Center(
                child: InkWell(
                  onTap: () {
                    if ((controller.selectedOptions.elementAt(pageIndex)['optionIds'] as List)
                        .isNotEmpty) {
                      if (pageIndex == controller.pageList.length - 1) {
                        controller.questionCommit();
                      } else {
                        controller.stepNext();
                      }
                    }
                  },
                  child: Container(
                    width: 234.w,
                    height: 45.w,
                    margin: EdgeInsets.only(top: 10.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(45.w / 2),
                      gradient: LinearGradient(
                          colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')]),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      pageIndex == controller.pageList.length - 1 ? 'Start To Explore'.tr : 'GO'.tr,
                      style: TextStyle(
                          fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 55.h + CommonUtil.bottomBarHeight(),
            ),
          ],
        ),
      ),
    );
  }

  Widget tagWidget() {
    return Obx(
      () => Wrap(
        children: List.generate((model.optionResps ?? []).length, (index) {
          OptionRespsModel answerModel = model.optionResps!.elementAt(index);
          bool hidden = false;
          if ((answerModel.subContent == 'nsfw' && controller.tagSwitchType.contains(1) == false) ||
              (answerModel.subContent == 'bdsm' && controller.tagSwitchType.contains(2) == false)) {
            hidden = true;
          }
          bool selected = (controller.selectedOptions.elementAt(pageIndex)['optionIds'] as List)
              .contains(answerModel.optionId);
          return hidden
              ? const SizedBox(width: 0)
              : InkWell(
                  onTap: () {
                    controller.setSelectedOptions(
                        optionId: answerModel.optionId ?? 0, single: false);
                  },
                  child: Container(
                    height: 38.w,
                    constraints: BoxConstraints(minWidth: 50.w),
                    margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.w),
                    child: Stack(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          margin: EdgeInsets.only(top: 6.w),
                          height: 32.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(36.w / 2),
                            border: Border.all(
                                color: selected
                                    ? AppColor.colorsUtil('#F0BE72')
                                    : Colors.white.withValues(alpha: 0.4),
                                strokeAlign: BorderSide.strokeAlignOutside),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                answerModel.content ?? '',
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                    color: selected == true
                                        ? AppColor.colorsUtil('F0BE72')
                                        : Colors.white.withValues(alpha: 0.4)),
                              )
                            ],
                          ),
                        ),
                        if ((answerModel.subContent == 'nsfw' &&
                                controller.tagSwitchType.contains(1) == true) ||
                            (answerModel.subContent == 'bdsm' &&
                                controller.tagSwitchType.contains(2) == true))
                          Positioned(
                            top: 0,
                            left: 2.w,
                            child: Image.asset(
                              answerModel.subContent == 'nsfw'
                                  ? Assets.assetsImagesTagNsfw
                                  : Assets.assetsImagesTagBdsm,
                              width: 48.w,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
        }),
      ),
    );
  }
}
