import 'package:amor_app/common/models/login_models/register_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/preferences/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PreferencesImageWidget extends StatefulWidget {
  final RegisterModel model;
  final int pageIndex;
  const PreferencesImageWidget({
    super.key,
    required this.model,
    required this.pageIndex,
  });

  @override
  State<PreferencesImageWidget> createState() => _PreferencesImageWidgettState();
}

class _PreferencesImageWidgettState extends State<PreferencesImageWidget> {
  PreferencesPageController controller = Get.find<PreferencesPageController>();

  @override
  Widget build(BuildContext context) {
    List optionIds = controller.selectedOptions.elementAt(widget.pageIndex)['optionIds'];
    return Obx(
      () => AnimatedOpacity(
        opacity: controller.stepIndex.value == widget.pageIndex ? 1 : 0,
        duration: controller.animatedDuration,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: REdgeInsets.only(left: 10),
                child: CustomBackButton(
                    back: () => widget.pageIndex == 0 ? Get.back() : --controller.stepIndex.value),
              ),
            ),
            20.verticalSpaceFromWidth,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w),
              child: Text(
                'Q ${widget.pageIndex + 1}/${controller.questionList.length}: ${widget.model.title}',
                style: TextStyle(fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            30.verticalSpaceFromWidth,
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 20.w, top: 10),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 38.w,
                    crossAxisSpacing: 25.w,
                    mainAxisExtent: (1.sw - 75.w) / 2),
                itemCount: widget.model.optionResps?.length,
                itemBuilder: (BuildContext context, int index) {
                  return InkWell(
                    onTap: () {
                      setState(() {
                        controller.setSelectedOptions(
                            optionId: widget.model.optionResps![index].optionId ?? 0,
                            single: widget.model.selectType == 1);
                      });
                    },
                    child: Column(
                      children: [
                        Container(
                          width: (1.sw - 75.w) / 2,
                          height: (1.sw - 75.w) / 2,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.r),
                              border: optionIds.contains(widget.model.optionResps?[index].optionId)
                                  ? Border.all(
                                      width: 3.w,
                                      color: AppColor.colorsUtil('#F0BE72'),
                                      strokeAlign: BorderSide.strokeAlignOutside,
                                    )
                                  : null),
                          // clipBehavior: Clip.antiAlias,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20.r),
                            child: CachedNetworkImage(
                              imageUrl: widget.model.optionResps?[index].image ?? '',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            //多选或最后一步才有按钮
            if (widget.model.selectType == 2 || widget.pageIndex == controller.pageList.length - 1)
              AnimatedOpacity(
                opacity:
                    (controller.selectedOptions.elementAt(widget.pageIndex)['optionIds'] as List)
                            .isNotEmpty
                        ? 1
                        : 0.5,
                duration: controller.animatedDuration,
                child: Center(
                  child: InkWell(
                    onTap: () {
                      if ((controller.selectedOptions.elementAt(widget.pageIndex)['optionIds']
                              as List)
                          .isNotEmpty) {
                        if (widget.pageIndex == controller.pageList.length - 1) {
                          controller.questionCommit();
                        } else {
                          controller.stepNext();
                        }
                      }
                    },
                    child: Container(
                      width: 234.w,
                      height: 45.w,
                      margin: EdgeInsets.only(top: 10.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(45.w / 2),
                        gradient: LinearGradient(colors: [
                          AppColor.colorsUtil('#FFDCA4'),
                          AppColor.colorsUtil('#C8984A')
                        ]),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        widget.pageIndex == controller.pageList.length - 1
                            ? 'Start To Explore'.tr
                            : 'GO'.tr,
                        style: TextStyle(
                            fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                      ),
                    ),
                  ),
                ),
              ),
            SizedBox(
              height: 55.h + CommonUtil.bottomBarHeight(),
            ),
          ],
        ),
      ),
    );
  }
}
