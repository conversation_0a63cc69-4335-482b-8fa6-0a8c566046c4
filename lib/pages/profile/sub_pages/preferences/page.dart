import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class PreferencesPage extends GetView<PreferencesPageController> {
  const PreferencesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.colorsUtil('#0B0813'),
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Positioned(
            top: 10.h + CommonUtil.statusBarHeight(context),
            right: 12.w,
            child: DefaultTextStyle(
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 12.sp,
                  color: AppColor.colorsUtil('#FFDCA4')),
              child: ShaderMask(
                shaderCallback: (rect) {
                  return LinearGradient(
                    colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
                  ).createShader(rect);
                },
                child: Text(
                  "register page title".tr,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          //进度条背景
          Positioned(
            top: 30.h + CommonUtil.statusBarHeight(context),
            left: 0,
            right: 0,
            child: Container(
              height: 4.w,
              color: Colors.white.withValues(alpha: 0.2),
            ),
          ),
          //进度条
          Obx(() {
            return controller.pageList.isEmpty
                ? Container()
                : AnimatedPositioned(
                    duration: controller.animatedDuration,
                    top: 30.h + CommonUtil.statusBarHeight(context),
                    left: 0,
                    width:
                        (1.sw) * ((controller.stepIndex.value + 1) / (controller.pageList.length)),
                    child: Container(
                      height: 4.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.w / 2),
                            bottomRight: Radius.circular(4.w / 2)),
                        gradient: LinearGradient(
                          colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
                        ),
                      ),
                    ),
                  );
          }),
          Positioned(
            top: 45.h + CommonUtil.statusBarHeight(context),
            left: 0,
            right: 0,
            bottom: 0,
            child: Obx(
              () => IndexedStack(
                  index: controller.stepIndex.value,
                  children: controller.pageList.map((element) => element as Widget).toList()),
            ),
          ),
        ],
      ),
    );
  }
}
