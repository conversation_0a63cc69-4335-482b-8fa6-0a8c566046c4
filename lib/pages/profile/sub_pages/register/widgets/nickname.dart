import 'dart:io';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/assets.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class RegisterNicknameWidget extends GetView<RegisterPageController> {
  const RegisterNicknameWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnimatedOpacity(
        opacity: controller.stepIndex.value == 1 ? 1 : 0,
        duration: controller.animatedDuration,
        child: Column(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: REdgeInsets.only(left: 10),
                child: CustomBackButton(back: () => --controller.stepIndex.value),
              ),
            ),
            10.verticalSpaceFromWidth,
            Text(
              '${'Who are you to Amor<PERSON>?'.trArgs(['Amors'])}💞',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            70.verticalSpaceFromWidth,
            Container(
              height: 50.w,
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white.withValues(alpha: 0.1),
              ),
              child: Row(
                children: [
                  16.horizontalSpace,
                  Expanded(
                    child: CustomTextField.textField(
                      hintText: 'Nickname (Try roll the dice)'.tr,
                      textCtl: controller.nicknameTextCtl,
                      // text: controller.profileData!['nickname'],
                      onChanged: (value) => controller.nickName.value = value,
                    ),
                  ),
                  16.horizontalSpace,
                  ImageBtn(
                      iconSting: Assets.assetsImagesRegisterNicknameRandom,
                      onPressed: () {
                        controller.getProfileData(type: 'nickname');
                      },
                      width: 50.w,
                      height: double.infinity),
                ],
              ),
            ),
            70.verticalSpaceFromWidth,
            InkWell(
              onTap: () {
                controller.selectAvatar();
              },
              child: Container(
                width: 162.w,
                height: 162.w,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.r)),
                child: Obx(
                  () => Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20.r),
                        child: controller.selectedAvatar.value.isEmpty
                            ? Image.asset(
                                Assets.assetsImagesProfileDefAvatar,
                                fit: BoxFit.cover,
                              )
                            : (controller.avatarType == 'url')
                                ? CachedNetworkImage(
                                    imageUrl: controller.selectedAvatar.value,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                    progressIndicatorBuilder: (context, url, progress) => Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 4,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                            AppColor.colorsUtil('#F0BE72')),
                                      ),
                                    ),
                                  )
                                : Image.file(
                                    File(controller.selectedAvatar.value),
                                    width: double.infinity,
                                    fit: BoxFit.fill,
                                  ),
                      ),
                      Positioned(
                        right: 10.w,
                        bottom: 10.w,
                        width: 32.w,
                        height: 32.w,
                        child: Image.asset(
                          Assets.assetsImagesRegisterAvatorEdit,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            10.verticalSpaceFromWidth,
            InkWell(
              onTap: () {
                controller.getProfileData(type: 'avatar');
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 10.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      Assets.assetsImagesRandomNickname,
                      width: 20.w,
                    ),
                    4.horizontalSpace,
                    Text(
                      'AI Surprise'.tr,
                      style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColor.colorsUtil('#F0BE72'),
                          fontWeight: FontWeight.w700),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            Obx(
              () => Opacity(
                opacity: controller.nickName.value.isNotEmpty ? 1 : 0.5,
                child: GestureDetector(
                  onTap: () {
                    if (controller.nickName.value.isNotEmpty) {
                      controller.inituser();
                    }
                  },
                  behavior: HitTestBehavior.translucent,
                  child: Container(
                    width: 234.w,
                    height: 45.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(45.w / 2),
                      gradient: LinearGradient(
                          colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')]),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      'GO'.tr,
                      style: TextStyle(
                          fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                  ),
                ),
              ),
            ),
            10.verticalSpaceFromWidth,
            Text(
              'you can always change this in Profile'.tr,
              style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.85),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic),
            ),
            30.verticalSpaceFromWidth,
          ],
        ),
      ),
    );
  }
}
