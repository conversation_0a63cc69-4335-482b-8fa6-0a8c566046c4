import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterFeedbackWidget extends StatelessWidget {
  const RegisterFeedbackWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColor.mainBg,
      child: Column(
        children: [
          SafeArea(child: 80.verticalSpaceFromWidth),
          Text(
            'Help Us Grow'.tr,
            style: TextStyle(
                fontSize: 24.sp, fontWeight: FontWeight.w700, color: AppColor.primaryText),
          ),
          18.verticalSpaceFromWidth,
          Text(
            'feedback tip'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 16.sp, fontWeight: FontWeight.w400, color: AppColor.primaryText),
          ),
          20.verticalSpaceFromWidth,
          Image.asset(
            Assets.assetsImagesMarkIcon,
            width: 180.w,
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    // controller.done();
                    Get.back();
                  },
                  child: Container(
                    width: 127.w,
                    height: 45.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(45.w / 2),
                      border: Border.all(color: AppColor.colorsUtil('#F0BE72')),
                    ),
                    child: Text(
                      '💔${'Not now'.tr}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: AppColor.colorsUtil('#F0BE72'),
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                GradientColorBtn(
                  height: 45.w,
                  width: 190.w,
                  text: '🙋${'Sure Let me help'.tr}',
                  textStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                  ),
                  onTap: () {
                    AppService.appReview(inApp: true);
                    Future.delayed(const Duration(milliseconds: 2000), () {
                      if (Get.isRegistered<SessionPageController>() &&
                          SessionPageController.to.state.isShowAppScore == true) {
                        Get.back();
                      }
                    });
                  },
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              Get.back();
              Get.find<SPService>().set(spRegisterMark, true);
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 14.w),
              child: CommonUtil.gradientText(
                text: 'Never show this again'.tr,
                colors: [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69A51')],
                textStyle: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
          SafeArea(
            top: false,
            child: 10.verticalSpaceFromWidth,
          ),
        ],
      ),
    );
  }
}
