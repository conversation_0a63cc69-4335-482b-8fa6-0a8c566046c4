import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class RegisterBaseInfoWidget extends GetView<RegisterPageController> {
  const RegisterBaseInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Row(
        //   children: [
        // Obx(
        //   () => AnimatedOpacity(
        //     opacity: controller.selectedSex.value > 1 ? 0 : 1,
        //     duration: controller.animatedDuration,
        //     child: CustomBackButton(back: () {
        //       controller.selectedSex.value = 2;
        //       controller.nickName.value = '';
        //       controller.nicknameTextCtl.text = '';
        //       controller.avatarType = '';
        //       controller.selectedAvatar.value = '';
        //     }),
        //   ),
        // ),
        //   ],
        // ),
        54.verticalSpace<PERSON>rom<PERSON>idth,
        Center(
          child: Text(
            'Your pronoun:'.tr,
            style: TextStyle(fontSize: 26.sp, color: Colors.white, fontWeight: FontWeight.w600),
          ),
        ),
        Expanded(
          child: SizedBox(
            width: 1.sw,
            child: Obx(
              () => Stack(
                alignment: Alignment.center,
                children: [
                  AnimatedPositioned(
                    top: 50.w,
                    duration: controller.animatedDuration,
                    child: sexListWidget(
                      title: 'She'.tr,
                      onTap: () {
                        controller.selectSex(0);
                      },
                      selected: controller.selectedSex.value == 0,
                    ),
                  ),
                  AnimatedPositioned(
                    top: 130.w,
                    duration: controller.animatedDuration,
                    child: sexListWidget(
                      title: 'He'.tr,
                      onTap: () {
                        controller.selectSex(1);
                      },
                      selected: controller.selectedSex.value == 1,
                    ),
                  ),
                  AnimatedPositioned(
                    top: 210.w,
                    duration: controller.animatedDuration,
                    child: sexListWidget(
                        title: 'They'.tr,
                        onTap: () {
                          controller.selectSex(-1);
                        },
                        selected: controller.selectedSex.value == -1),
                  ),
                  if (AppService.audit == true)
                    Positioned(
                      top: 400.w,
                      child: GradientColorBtn(
                        height: 45.w,
                        width: 234.w,
                        text: 'Skip'.tr,
                        onTap: () {
                          RegisterPageController.to.selectSex(1);
                        },
                      ),
                    ),

                  /*
                  Positioned(
                    top: 80.w,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Obx(
                      () => AnimatedCrossFade(
                        firstChild: basicsWidget(),
                        secondChild: Container(),
                        crossFadeState: controller.selectedSex.value > 1
                            ? CrossFadeState.showSecond
                            : CrossFadeState.showFirst,
                        duration: controller.animatedDuration,
                      ),
                    ),
                  ),
                  */
                ],
              ),
            ),
            // ),
          ),
        ),
      ],
    );
  }

  Widget sexListWidget(
      {required String title, required bool selected, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: AnimatedOpacity(
        opacity: 1,
        duration: controller.animatedDuration,
        child: Container(
          width: 234.w,
          height: 50.w,
          alignment: Alignment.center,
          // duration: controller.animatedDuration,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.w / 2),
            border: Border.all(color: selected ? AppColor.colorsUtil('#F0BE72') : Colors.white),
          ),
          child: Text(
            title,
            style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w500,
                color: selected ? AppColor.colorsUtil('#F0BE72') : Colors.white),
          ),
        ),
      ),
    );
  }
}
