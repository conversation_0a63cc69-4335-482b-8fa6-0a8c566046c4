import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/login_models/register_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'widgets/base_info.dart';
import 'widgets/register_widgets.dart';
import 'widgets/tag.dart';

class RegisterPageController extends GetxController {
  static RegisterPageController get to => Get.find();
  var animated = 0.obs;
  bool animatedEnd = false;
  var stepIndex = 0.obs;
  //选择的性别下标
  var selectedSex = 2.obs;
  //标签切换
  var tagSwitchType = [].obs;
  Duration animatedDuration = const Duration(milliseconds: 100);
  List<RegisterModel> questionList = [];
  var pageList = [].obs;
  var selectedOptions = [].obs;

  late TextEditingController nicknameTextCtl;
  var nickName = ''.obs;
  //头像文件地址/url
  var selectedAvatar = ''.obs;
  //头像类型
  String avatarType = '';
  //缓存的头像和昵称
  List cacheAvatarNicknameHe = [];
  List cacheAvatarNicknameShe = [];
  List cacheAvatarNicknameThey = [];

  @override
  void onInit() {
    super.onInit();
    nicknameTextCtl = TextEditingController();
    pageList.assignAll([const RegisterBaseInfoWidget(), const RegisterNicknameWidget()]);
    if (AppService.audit == false) {
      getRegisterInfo();
    }
    for (var element in CacheSaleService.avatarNickname) {
      if (element['gender'] == 0) {
        cacheAvatarNicknameShe.add(element);
      }
      if (element['gender'] == 1) {
        cacheAvatarNicknameHe.add(element);
      }
      if (element['gender'] == -1) {
        cacheAvatarNicknameThey.add(element);
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
    Future.delayed(const Duration(milliseconds: 1000), () {
      animated.value = 1;
    });
    Analytics().logEvent(Analytics.view, screen: Analytics.pagePronounpage);
  }

  //获取注册填写的信息
  getRegisterInfo() async {
    questionList = await LoginApi.registerInfoGet();
    pageList.insertAll(
        2,
        questionList.map((e) {
          selectedOptions.add({'optionIds': [], 'questionId': e.questionId});
          if (e.questionType == 'tag') {
            return RegisterTagWidget(model: e, pageIndex: questionList.indexOf(e) + 2);
          }
          if (e.questionType == 'image') {
            return RegisterImageWidget(pageIndex: questionList.indexOf(e) + 2, model: e);
          }
          return Container();
        }).toList());
    ReportUtil.reportViews(page: ReportUtil.kyc, action: ReportUtil.view);
  }

  //选择性别
  selectSex(int index) {
    if (selectedSex.value != index) {
      selectedSex.value = index;
      Map element = {};
      if (index == 0 && cacheAvatarNicknameShe.isNotEmpty) {
        element = cacheAvatarNicknameShe.first;
        cacheAvatarNicknameShe.removeAt(0);
      }
      if (index == 1 && cacheAvatarNicknameHe.isNotEmpty) {
        element = cacheAvatarNicknameHe.first;
        cacheAvatarNicknameHe.removeAt(0);
      }
      if (index == -1 && cacheAvatarNicknameThey.isNotEmpty) {
        element = cacheAvatarNicknameThey.first;
        cacheAvatarNicknameThey.removeAt(0);
      }
      if (element['nickname'] != null && element['avatar'] != null) {
        nicknameTextCtl.text = element['nickname'];
        nickName.value = nicknameTextCtl.text;
        avatarType = 'url';
        selectedAvatar.value = element['avatar'];
      } else {
        nicknameTextCtl.text = '';
        nickName.value = nicknameTextCtl.text;
        avatarType = '';
        selectedAvatar.value = '';
      }
    }
    ++stepIndex.value;
  }

  //获取随机头像、昵称
  getProfileData({required String type}) async {
    HapticFeedback.mediumImpact();
    Map<String, dynamic>? result = {};
    if (selectedSex.value == 0 && cacheAvatarNicknameShe.isNotEmpty) {
      result = cacheAvatarNicknameShe.first;
      cacheAvatarNicknameShe.removeAt(0);
    } else if (selectedSex.value == 1 && cacheAvatarNicknameHe.isNotEmpty) {
      result = cacheAvatarNicknameHe.first;
      cacheAvatarNicknameHe.removeAt(0);
    } else if (selectedSex.value == -1 && cacheAvatarNicknameThey.isNotEmpty) {
      result = cacheAvatarNicknameThey.first;
      cacheAvatarNicknameThey.removeAt(0);
    } else {
      result = await LoginApi.genNickname(gender: selectedSex.value);
    }
    if (result != null) {
      if (type != 'avatar') {
        nicknameTextCtl.text = result['nickname'];
        nickName.value = nicknameTextCtl.text;
      }
      if (type != 'nickname') {
        avatarType = 'url';
        selectedAvatar.value = result['avatar'];
      }
    }
  }

  //选择头像
  selectAvatar() async {
    String? path = await SelectMediaUtil.selectImage();
    if (path != null) {
      avatarType = 'file';
      selectedAvatar.value = path;
    }
  }

  //上传头像
  Future<String?> uploadFile() async {
    Loading.show();
    String? url = await UploadOss.upload(
        path: selectedAvatar.value, format: 'png', servicePath: ossServicePathAvatar);
    if (url == null) {
      Loading.toast('Avatar upload failed');
      return null;
    }
    return url;
    /*
    Loading.show();
    //图片鉴黄
    bool imageDetection =
        await CommonApis.imageCheck(url: url, contentCheckSource: 'profile', loding: true);
    if (imageDetection == true) {
      return url;
    }
    return null;
    */
  }

  //提交基础信息
  inituser() async {
    String avatar = '';
    if (avatarType == 'file') {
      String? result = await uploadFile();
      if (result != null) {
        avatar = result;
      }
    } else {
      avatar = selectedAvatar.value;
    }
    bool result = await LoginApi.initUser(
        gender: selectedSex.value, avatar: avatar, nickname: nickName.value);
    if (result == false) {
      return;
    }
    if (questionList.isEmpty) {
      //没有其他问题 直接提交
      questionCommit();
      return;
    }
    ++stepIndex.value;
  }

  //提交问题
  questionCommit() async {
    Analytics().logEvent(Analytics.clickStart2explore, screen: Analytics.pageKyc);
    selectedOptions.removeWhere((element) => (element['optionIds'] as List).isEmpty);
    Map<String, dynamic>? result = await LoginApi.questionCommit(questionItemReqs: selectedOptions);
    if (result == null) {
      return;
    }
    //提交成功后设置register为false
    Map<String, dynamic> userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>;
    userLoginInfo['register'] = false;
    Get.find<SPService>().set(spLoginInfo, userLoginInfo);
    done();
  }

  //选中和取消选中
  setSelectedOptions({required int optionId, required bool single}) {
    HapticFeedback.mediumImpact();
    List optionIds = selectedOptions.elementAt(stepIndex.value - 2)['optionIds'];
    //单选
    if (single) {
      optionIds.clear();
      if (stepIndex.value < pageList.length - 3) {
        Future.delayed(animatedDuration, () {
          stepNext();
        });
      }
    }
    if (optionIds.contains(optionId)) {
      optionIds.remove(optionId);
    } else {
      optionIds.add(optionId);
    }
    selectedOptions.elementAt(stepIndex.value - 2)['optionIds'] = optionIds;
    selectedOptions.refresh();
  }

  //清除选中的nsfw、bdsm标签
  clearSpecialTags({required RegisterModel tagWidgetMdel}) {
    List optionIds = selectedOptions.elementAt(stepIndex.value - 2)['optionIds'];
    for (OptionRespsModel element in tagWidgetMdel.optionResps ?? []) {
      if (element.subContent != null && optionIds.contains(element.optionId)) {
        if ((element.subContent == 'nsfw' && tagSwitchType.contains(1) == false) ||
            (element.subContent == 'bdsm' && tagSwitchType.contains(2) == false)) {
          optionIds.remove(element.optionId);
        }
      }
    }
    selectedOptions.elementAt(stepIndex.value - 2)['optionIds'] = optionIds;
    selectedOptions.refresh();
  }

  //下一步
  stepNext() async {
    ++stepIndex.value;
  }

  //完成
  done() async {
    // Get.offAndToNamed(Routes.tabs);
    await Get.find<SPService>().remove(spExposureModel);
    if (Get.isRegistered<AmorsPageController>() == true) {
      AmorsPageController.to.selectedCategory(0);
    }
    Get.back();
    // Routes.initController();
    ReportUtil.reportViews(page: ReportUtil.kyc, action: ReportUtil.finish);
    ReportUtil.reportViews(page: ReportUtil.kyc, action: ReportUtil.quit);
  }

  @override
  void onClose() {
    super.onClose();
    nicknameTextCtl.dispose();
  }
}
