import 'package:amor_app/common/utils/utils.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class RegisterPage extends GetView<RegisterPageController> {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.colorsUtil('#0B0813'),
      resizeToAvoidBottomInset: false,
      body: WillPopScope(
        // canPop: false,
        onWillPop: () {
          return Future.value(false);
        },
        child: SizedBox(
          width: 1.sw,
          height: 1.sh,
          child: Stack(
            children: [
              Obx(
                () => AnimatedPositioned(
                  top: (controller.animated.value == 0 ? 300.h : 10.h) +
                      CommonUtil.statusBarHeight(context),
                  left: controller.animated.value == 0 ? 0 : 153.w,
                  right: 0,
                  duration: controller.animatedDuration,
                  child: AnimatedDefaultTextStyle(
                    style: TextStyle(
                        fontWeight:
                            controller.animated.value == 0 ? FontWeight.w600 : FontWeight.w500,
                        fontSize: controller.animated.value == 0 ? 15.sp : 12.sp,
                        color: controller.animated.value == 0
                            ? Colors.white
                            : AppColor.colorsUtil('#FFDCA4')),
                    duration: controller.animatedDuration,
                    child: ShaderMask(
                      shaderCallback: (rect) {
                        return LinearGradient(
                          colors: controller.animated.value == 0
                              ? [Colors.white, Colors.white]
                              : [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
                        ).createShader(rect);
                      },
                      child: EasyRichText(
                        "register page title".trArgs(['AI Amors']),
                        textAlign: TextAlign.center,
                        patternList: [
                          EasyRichTextPattern(
                            targetString: 'AI Amors',
                            style: TextStyle(
                                fontSize: controller.animated.value == 0 ? 36.sp : 12.sp,
                                fontWeight: FontWeight.w700),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              //进度条背景
              Positioned(
                top: 30.h + CommonUtil.statusBarHeight(context),
                left: 0,
                right: 0,
                child: Container(
                  height: 4.w,
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
              //进度条
              Obx(() {
                return controller.pageList.length > 1
                    ? AnimatedPositioned(
                        duration: controller.animatedDuration,
                        top: 30.h + CommonUtil.statusBarHeight(context),
                        left: 0,
                        width: (1.sw) *
                            (controller.stepIndex.value / (controller.pageList.length - 1)),
                        child: Container(
                          height: 4.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(4.w / 2),
                                bottomRight: Radius.circular(4.w / 2)),
                            gradient: LinearGradient(
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A')
                              ],
                            ),
                          ),
                        ),
                      )
                    : Container();
              }),

              Positioned(
                top: 45.h + CommonUtil.statusBarHeight(context),
                left: 0,
                right: 0,
                bottom: 0,
                child: Obx(
                  () => AnimatedOpacity(
                    opacity: controller.animated.value == 0 ? 0 : 1,
                    duration: controller.animatedDuration,
                    child: IndexedStack(
                        index: controller.stepIndex.value,
                        children: controller.pageList.map((element) => element as Widget).toList()),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
