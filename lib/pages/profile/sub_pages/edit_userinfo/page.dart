import 'dart:io';

import 'package:amor_app/common/models/profile_model/profile_bind_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'controller.dart';

class EditUserinfoPage extends GetView<EditUserinfoPageController> {
  const EditUserinfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit information'.tr),
        leading: const CustomBackButton(),
        actions: [
          Center(
            child: InkWell(
              onTap: () {
                controller.updateUserinfo();
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  "Save".tr,
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.colorsUtil("#F0BE72"),
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          )
        ],
      ),
      body: GetBuilder<EditUserinfoPageController>(
        builder: (controller) => SingleChildScrollView(
          padding: REdgeInsets.only(bottom: CommonUtil.bottomBarHeight()),
          child: Column(
            children: [
              userHeader(),
              bgContainer(
                child: Column(
                  children: [
                    itemWidget(
                      onTap: () {},
                      title: 'Nickname'.tr,
                      hintText: '20 characters'.tr,
                    ),
                    dividerWidget(),
                    itemWidget(
                      onTap: () {
                        _showGenderPicker();
                      },
                      title: 'Pronoun'.tr,
                      subTitle: (controller.model!.gender == 0
                          ? 'She'.tr
                          : controller.model!.gender == 1
                              ? 'He'.tr
                              : 'They'.tr),
                      showArrow: true,
                      link: true,
                    ),
                    dividerWidget(),
                    itemWidget(
                      onTap: () {},
                      title: 'Uid',
                      subTitle: controller.model!.shareCode ?? '',
                    ),
                  ],
                ),
              ),
              20.verticalSpaceFromWidth,
              bgContainer(
                child: Column(
                  children: [
                    /*
                    itemWidget(
                      onTap: () {
                        controller.link('Google');
                      },
                      title: 'Google',
                      subTitle:
                          _getModel('Google', controller.bindList)?.bindAccount,
                      link: _getModel('Google', controller.bindList)?.hasBind ==
                          0,
                    ),
                    dividerWidget(),
                    */
                    if (GetPlatform.isIOS)
                      itemWidget(
                        onTap: () {
                          controller.link('Apple ID');
                        },
                        title: 'Apple ID',
                        subTitle: _getModel('Apple ID', controller.bindList)?.bindAccount,
                        link: _getModel('Apple ID', controller.bindList)?.hasBind == 0,
                      ),
                    if (GetPlatform.isIOS) dividerWidget(),
                    /*
                    itemWidget(
                      onTap: () {
                        controller.link('Phone');
                      },
                      title: 'Phone',
                      subTitle: _getModel('Phone', controller.bindList)?.bindAccount,
                      link: _getModel('Phone', controller.bindList)?.hasBind == 0,
                    ),
                    dividerWidget(),
                    */
                    itemWidget(
                      onTap: () {
                        controller.link('Email');
                      },
                      title: 'Email',
                      subTitle: _getModel('Email', controller.bindList)?.bindAccount,
                      link: _getModel('Email', controller.bindList)?.hasBind == 0,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget userHeader() {
    return Container(
      padding: REdgeInsets.symmetric(vertical: 30),
      alignment: Alignment.center,
      child: Stack(
        children: [
          ClipOval(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _showAvatarPicker(controller),
              child: controller.filePath.isEmpty
                  ? CachedNetworkImage(
                      fit: BoxFit.cover,
                      memCacheWidth: Get.width.toInt(),
                      width: 120.w,
                      height: 120.w,
                      imageUrl: controller.model!.avatarUrl ?? AppService.configModel.avatar ?? '',
                      placeholder: (context, url) {
                        return Image.asset(
                          Assets.assetsImagesProfileDefAvatar,
                          fit: BoxFit.cover,
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Image.asset(
                          Assets.assetsImagesProfileDefAvatar,
                          fit: BoxFit.cover,
                        );
                      },
                    )
                  : Image.file(
                      File(
                        controller.filePath,
                      ),
                      width: 120.w,
                      height: 120.w,
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          Positioned(
            bottom: 5,
            right: 5,
            child: Image.asset(
              Assets.assetsImagesEditUserinfoCamare,
              width: 24.w,
            ),
          )
        ],
      ),
    );
  }

  Widget bgContainer({required Widget child, double? height}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.white.withValues(alpha: 0.05),
      ),
      child: child,
    );
  }

  Widget itemWidget({
    required VoidCallback onTap,
    required String title,
    String? subTitle,
    bool showArrow = false,
    String? hintText,
    bool link = false,
  }) {
    return InkWell(
      onTap: link == true ? onTap : null,
      child: SizedBox(
        height: 60.w,
        child: Row(
          mainAxisAlignment: showArrow ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 16.sp, color: Colors.white),
            ),
            if (hintText != null)
              Expanded(
                child: CustomTextField.textField(
                  textCtl: controller.textCtl,
                  hintText: hintText,
                  maxLength: 20,
                  textAlign: TextAlign.end,
                ),
              ),
            if (showArrow) const Spacer(),
            if (subTitle != null)
              Text(
                subTitle,
                style: TextStyle(fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.5)),
              ),
            if (link == true && subTitle == null)
              Text(
                'Link'.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColor.colorsUtil('#F0BE72'),
                  //下划线
                  decoration: TextDecoration.underline,
                  decorationColor: AppColor.colorsUtil('#F0BE72'),
                ),
              ),
            if (showArrow)
              Padding(
                padding: EdgeInsets.only(left: 6.w),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 15.w,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget dividerWidget() {
    return Divider(height: 0.5, color: Colors.white.withValues(alpha: 0.2));
  }

  BindInfoModel? _getModel(String type, List<BindInfoModel> list) {
    String bindType = '';
    switch (type) {
      case 'Facebook':
        bindType = 'META';
        break;
      case 'Google':
        bindType = 'GOOGLE';
        break;
      case 'Apple ID':
        bindType = 'APPLE';
        break;
      case 'Phone':
        bindType = 'PHONE';
        break;
      case 'Email':
        bindType = 'EMAIL';
        break;
      default:
    }
    for (var element in list) {
      if (element.bindType == bindType) {
        return element;
      }
    }
    return null;
  }

  _showAvatarPicker(EditUserinfoPageController ctl) async {
    List<CustomBottomSheetMenuModel> menus = [
      CustomBottomSheetMenuModel(menuTitle: 'Take photo'),
      CustomBottomSheetMenuModel(
        menuTitle: 'Choose from Album',
      ),
    ];
    var result = await CustomBottomSheet.show(menus: menus);
    if (result != null) {
      if (result == 0) {
        ctl.selectPhoto(ImageSource.camera);
      }
      if (result == 1) {
        ctl.selectPhoto(ImageSource.gallery);
      }
    }
  }

  _showGenderPicker() async {
    List<CustomBottomSheetMenuModel> menus = [
      CustomBottomSheetMenuModel(menuTitle: 'She'.tr),
      CustomBottomSheetMenuModel(menuTitle: 'He'.tr),
      CustomBottomSheetMenuModel(menuTitle: 'They'.tr),
    ];
    var result = await CustomBottomSheet.show(menus: menus);
    if (result != null) {
      if (result == 1) {
        //选择男
        controller.model!.gender = 1;
      }
      if (result == 0) {
        //选择女
        controller.model!.gender = 0;
      }
      if (result == 2) {
        controller.model!.gender = -1;
      }
      controller.update();
    }
  }
}
