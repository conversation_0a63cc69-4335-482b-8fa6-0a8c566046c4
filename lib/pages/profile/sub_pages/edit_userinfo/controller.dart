import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/profile_bind_model.dart';
import 'package:amor_app/common/models/profile_model/user_info_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/login/third_party_login.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
// ignore: library_prefixes
import 'package:firebase_auth/firebase_auth.dart' as fbAuth;
// import 'package:firebase_auth/firebase_auth.dart' as fbAuth;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class EditUserinfoPageController extends GetxController {
  static EditUserinfoPageController get to => Get.find();
  late TextEditingController textCtl;
  var btnEnable = true.obs;
  UserInfo? model;
  String filePath = '';
  String avatarUrl = '';
  int gender = 0;
  List<BindInfoModel> bindList = [];
  //是否正在上传头像
  bool uploadingFile = false;
  //是否已点击提交
  bool onTapCommit = false;
  @override
  void onInit() {
    super.onInit();
    textCtl = TextEditingController();
    getUserInfo();
  }

  //获取用户/绑定信息
  getUserInfo() async {
    model = UserService.to.userinfo.value;
    textCtl.text = model!.nickName ?? '';
    gender = model!.gender ?? 1;
    bindList = await ProfileApis.accountBindGet();
    update();
    showBindDialog();
    ReportUtil.reportViews(page: ReportUtil.account, action: ReportUtil.view);
  }

  //展示提示绑定的弹窗
  showBindDialog() async {
    //已绑定过第三方的用户就不再是TempUser
    if ((UserService.to.isTempUser == true)) {
      int curDate = CommonUtil.currentTimeMillis();
      int date = (Get.find<SPService>().get(spDeviceUserBindTip) ?? 0) as int;
      bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
      if (isSameDay) {
        return;
      }
      Get.find<SPService>().set(spDeviceUserBindTip, curDate);
      ProfileDialog.accountWarning();
    }
  }

  selectPhoto(ImageSource source) async {
    if ((source == ImageSource.gallery &&
            await PermissionHelper.getPermissionStatus(
                    GetPlatform.isAndroid ? Permission.storage : Permission.photos) ==
                false) ||
        source == ImageSource.camera &&
            await PermissionHelper.getPermissionStatus(Permission.camera) == false) {
      Loading.toast('Please go to settings to enable permissions');
      return;
    }

    String? path = await SelectMediaUtil.selectImage(source: source);
    if (path != null) {
      filePath = path;
      avatarUrl = '';
      update();
      uploadFile();
    }
  }

  //上传
  uploadFile() async {
    uploadingFile = true;
    String? url = await UploadOss.upload(
        path: filePath, format: 'png', callBackFilePath: true, servicePath: ossServicePathAvatar);
    if (url == null) {
      uploadingFile = false;
      Loading.toast('Avatar upload failed');
      onTapCommit = false;
      return;
    }
    List list = url.split('callBackFilePath');
    if (list.length != 2) {
      uploadingFile = false;
      onTapCommit = false;
      Loading.toast('Avatar upload failed');
      return;
    }
    //图片鉴黄
    bool imageDetection = await CommonApis.imageCheck(
        url: list.elementAt(1), contentCheckSource: 'profile', loding: false);
    if (list.elementAt(0) != filePath || Get.isRegistered<EditUserinfoPageController>() == false) {
      uploadingFile = false;
      onTapCommit = false;
      return;
    }
    uploadingFile = false;
    if (imageDetection == true) {
      avatarUrl = list.elementAt(1);
      //上传过程中点击提交按钮 等待上传完成后继续提交
      if (onTapCommit) {
        updateUserinfo();
      }
    }

    //清除指定图片缓存
    // CachedNetworkImage.evictFromCache("${AppService.configModel.ossUrl}$avatarUrl");
  }

  //修改用户信息
  updateUserinfo() async {
    Get.focusScope?.unfocus();
    onTapCommit = true;
    if (textCtl.text.isEmpty) {
      Loading.toast('Nickname cannot be empty');
      return;
    }
    if (uploadingFile == true) {
      Loading.show();
      return;
    }
    Loading.dismiss();
    if (avatarUrl.isEmpty && gender == model?.gender && textCtl.text == model?.nickName) {
      //没有修改
      Get.back();
      return;
    }
    if (textCtl.text.length > 20) {
      Loading.toast('Nickname too long');
      return;
    }
    bool result = await ProfileApis.profileUpdate(params: {
      'avatarUrl': avatarUrl.isNotEmpty ? avatarUrl : model!.avatarUrl,
      'gender': model?.gender,
      'nickname': textCtl.text,
    });
    //修改了性别需要刷新首页
    if (gender != model?.gender) {
      await Get.find<SPService>().remove(spExposureModel);
      if (Get.isRegistered<AmorsPageController>() == true) {
        AmorsPageController.to.selectedCategory(0);
      }
    }
    onTapCommit = false;
    if (result) {
      Loading.toast('Success');
      Future.delayed(const Duration(milliseconds: 200), () => Get.back());
    }
  }

  link(String type) {
    if (type == 'Facebook') {
      facebookAuth();
      return;
    }
    if (type == 'Google') {
      googleAuth();
      return;
    }
    if (type == 'Apple ID') {
      appleAuth();
      return;
    }

    Get.toNamed(Routes.accountLogin, arguments: type);
  }

  //facebook Auth
  facebookAuth() async {
    Loading.show();
    fbAuth.User? user = await ThirdPartyLogin.facebookLogIn();
    if (user != null) {
      String? idToken = await user.getIdToken(true);
      Loading.dismiss();
      serviceVerify(
        thirdType: "META",
        thirdToken: idToken,
      );
    } else {
      Loading.toast('verification failed');
    }
  }

  //google Auth
  Future googleAuth() async {
    Loading.show();
    fbAuth.User? user = await ThirdPartyLogin.googleLogin();
    if (user != null) {
      String? idToken = await user.getIdToken(true);
      serviceVerify(
        thirdType: "GOOGLE",
        thirdToken: idToken,
      );
    } else {
      Loading.toast('verification failed');
    }
  }

  //apple Auth
  Future appleAuth() async {
    String? identityToken = await ThirdPartyLogin.appleLogIn();
    if (identityToken != null) {
      serviceVerify(
        thirdType: "APPLE",
        thirdToken: identityToken,
      );
    } else {
      Loading.toast('verification failed');
    }
  }

  //服务器绑定
  serviceVerify({String? thirdToken, required String thirdType}) async {
    if (thirdToken == null) {
      Loading.toast('link error');
      return;
    }
    bool result = await ProfileApis.bindOauth(thirdToken: thirdToken, thirdType: thirdType);
    if (result == true) {
      //绑定成功后设置登录的用户类型
      Map<String, dynamic> userLoginInfo =
          Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>;
      userLoginInfo['loginType'] = '';
      Get.find<SPService>().set(spLoginInfo, userLoginInfo);
      UserService.to.userLoginType = '';
      getUserInfo();
    }
  }

  @override
  void onClose() {
    super.onClose();
    textCtl.dispose();
    ReportUtil.reportViews(page: ReportUtil.account, action: ReportUtil.quit);
  }
}
