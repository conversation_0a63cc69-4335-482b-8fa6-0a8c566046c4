import 'dart:convert';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/apis/profile_apis/profile_apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'page.dart';
import 'state.dart';
import 'widgets/discount_dialog.dart';
import 'widgets/subscribe_success_dialog.dart';

class PurchasePageController extends GetxController with GetSingleTickerProviderStateMixin {
  static PurchasePageController get to => Get.find();
  final int buyType;
  final String? source;
  PurchasePageController({this.buyType = 0, this.source});
  late TabController tabController;
  // late ScrollController vipCardScrollController;
  // late ScrollController vipItemScrollController;

  final state = PurchasePageControllerState();
  @override
  void onInit() {
    super.onInit();
    state.openTime = CommonUtil.currentTimeMillis();
    tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    state.tabbarIndex.value = 0;
    tabController.addListener(
      () async {
        if (tabController.animation!.value == tabController.index) {}
        state.tabbarIndex.value = tabController.index;
      },
    );

    //上一次优惠倒计时剩余时间
    int lastOfferTime = (Get.find<SPService>().get(spPuchaseOfferCountdown) ?? 0) as int;
    //距离上一次打开购买页的时间
    int lastOpenTime = CommonUtil.timeDifference(
        state.openTime, (Get.find<SPService>().get(spPuchaseCloseTime) ?? 0) as int,
        unit: 'seconds');
    //再次打开购买时 优惠倒计时已经结束
    if (lastOpenTime > lastOfferTime || lastOpenTime == 0) {
      state.offerCountDown = 1800;
    } else {
      state.offerCountDown = lastOfferTime - lastOpenTime;
    }
    checkCache();
  }

  checkCache() async {
    bool isAvailable = await InAppPurchaseUtil().initStoreInfo();
    if (isAvailable == false) {
      Loading.toast('Temporarily unable to make a purchase');
      return;
    }
    if (CacheSaleService.storeSsvipConfigList
            .isNotEmpty /* &&
        CacheSaleService.storeGemsConfigList.isNotEmpty*/
        ) {
      state.vipConfigmodel = CacheSaleService.vipConfigmodel;
      // state.storeVipConfigList = CacheSaleService.storeVipConfigList;
      state.storeSvipConfigList = CacheSaleService.storeSsvipConfigList;
      state.gemsConfigmodel = CacheSaleService.gemsConfigmodel;
      state.storeGemsConfigList = CacheSaleService.storeGemsConfigList;
      state.priceUnit = CacheSaleService.priceUnit;
      state.hasOffer = CacheSaleService.hasOffer;
      state.premiumBilledIndex = CacheSaleService.premiumBilledIndex;
      update(['purchase_vip']);
      showDiscountDialog();
      update(['purchase_gems']);
    } else if (state.cacheCount < 1 && CacheSaleService.startRequest == false) {
      state.cacheCount++;
      Loading.show();
      Future.delayed(const Duration(seconds: 5), () => Loading.dismiss());
      CacheSaleService.cacheRequest();
      return;
    }
    verifyInAppPurchase();
  }

  //选择item
  selectGemsPurchaseItem({required int index}) {
    AppPurchaseModel model = state.storeGemsConfigList.elementAt(index);
    List<AppPurchaseModel> list = [];
    for (var element in state.storeGemsConfigList) {
      element.selected = false;
      if (element.serviceProductDetails?.goodsNo == model.serviceProductDetails?.goodsNo) {
        element.selected = true;
      }
      list.add(element);
    }
    state.storeGemsConfigList.assignAll(list);
    update(['purchase_gems']);
    ReportUtil.reportPurchase(
        page: ReportUtil.gem,
        action: ReportUtil.select,
        value: '${model.offerPrice ?? model.productDetails?.price}');
    AdjustUtil.adjustEvent(event: AdjustUtil.select);
  }

//获取支付参数
  getPayParam() async {
    if (state.tabbarIndex.value == 0 && UserService.to.isVip) {
      return;
    }
    if ((state.gemsConfigList.isEmpty && state.tabbarIndex.value == 1)) {
      return;
    }
    AppPurchaseModel? model;
    if (state.tabbarIndex.value == 0) {
      model = state.storeSvipConfigList[state.premiumBilledIndex];
      ReportUtil.reportPurchase(page: ReportUtil.hot, action: ReportUtil.subscribe);
      Analytics().logEvent(
        UserService.to.userinfo.value.vipType == 'vip'
            ? Analytics.clickUpgrade
            : Analytics.clickSubscribe,
        itemPrice: model.offerPrice ?? model.productDetails?.rawPrice ?? 0,
        currency: state.priceUnit,
        sourceEvent: Analytics().hotSourceEvent,
        sourceChar: Get.isRegistered<SessionPageController>()
            ? SessionPageController.to.state.modelId.toString()
            : null,
        sourceScreen: Analytics().hotSourceScreen,
        screen: Analytics.pageHot,
      );
      if (model.productDetails != null) {
        InAppPurchaseUtil().fetchSubscribe(
          productDetails: model.offerProductDetails != null
              ? model.offerProductDetails!
              : model.productDetails!,
          offer: model.offerPrice != null,
        );
        return;
      }
    } else {
      model = state.storeGemsConfigList.firstWhereOrNull((element) => element.selected == true);
      ReportUtil.reportPurchase(page: ReportUtil.gem, action: ReportUtil.get);
      if (model != null && model.productDetails != null) {
        InAppPurchaseUtil().fetchPurchase(
          productDetails: model.offerProductDetails != null
              ? model.offerProductDetails!
              : model.productDetails!,
        );
        return;
      }
    }

    Loading.toast('Temporarily unable to make a purchase');
  }

//验证内购
  verifyInAppPurchase() async {
    String productId = '';
    List list = (AppService.sp.get(spInAppPurchaseOrder) as List?) ?? [];
    if (list.isEmpty) {
      return;
    }
    List verifyList = List.from(list);
    int buyType = 2;
    Loading.show();
    for (int i = 0; i < list.length; i++) {
      Map<String, dynamic>? params = jsonDecode(list.elementAt(i));
      if (params == null || params['serviceParam'] == null) {
        continue;
      }
      Map<String, dynamic> serviceParam = params['serviceParam'];
      serviceParam['source'] = PurchaseSheet.modelIdSource;
      Map? result = await ProfileApis.inAppPurchaseVerify(params: serviceParam);
      if (result != null) {
        if (result['price'] != null) {
          AdjustUtil.adjustRevenuEnent(value: result['price'], currencyCode: 'USD');
        }
        buyType = serviceParam['type'];
        productId = params['serviceParam']['goodsNo'] ?? '';
        try {
          verifyList.replaceRange(i, i + 1, ['verify']);
        } catch (e) {
          debugPrint('Error');
        }
      }
    }
    verifyList.removeWhere((element) {
      return element == 'verify';
    });
    Get.find<SPService>().set(spInAppPurchaseOrder, verifyList);
    String viptype = UserService.to.userinfo.value.vipType.toString();
    await UserService.to.getUserInfo();
    Loading.dismiss();
    if (Get.isRegistered<PurchasePageController>() == true) {
      //VIP订阅
      if (buyType == 0) {
        // vip购买
        if (viptype != UserService.to.userinfo.value.vipType) {
          // 订阅成功后进入个人中心展示动画
          AppService.sp.set(spSubscriptionAnimation, true);
          //展示弹窗
          showSubscribeSuccessDialog(vipUpgrade: false);
          Analytics().paySuccessEvent(source: source, type: 0, productId: productId);
        }
      } else {
        //宝石购买
        Future.delayed(const Duration(milliseconds: 500), () {
          if (verifyList.isEmpty) {
            Get.back();
          }
        });
        Analytics().paySuccessEvent(source: source, type: 1, productId: productId);
      }
    }
    //刷新聊天页余额、背景、图片消息
    if (Get.isRegistered<SessionPageController>()) {
      SessionPageController.to.getSessionConfig(updatePage: true);
    }
    //购买成功后刷新缓存的商品数据
    // CacheSaleService.cacheRequest();
  }

//restore
  restore() {
    Loading.show();
    InAppPurchaseUtil().checkRestorePurchases();
    ReportUtil.reportPurchase(page: ReportUtil.hot, action: ReportUtil.restore);
  }

  //优惠弹窗
  showDiscountDialog() async {
    int curDate = CommonUtil.currentTimeMillis();
    int date = (Get.find<SPService>().get(spPuchaseOfferDialog) ?? 0) as int;
    bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
    bool isAudit = AppService.audit;
    if (isSameDay == true || UserService.to.isVip == true || isAudit == true) {
      return;
    }
    Get.find<SPService>().set(spPuchaseOfferDialog, curDate);
    if (state.hasOffer == true) {
      state.priceNumAnimation.value = 1;
      ReportUtil.reportPurchase(page: ReportUtil.hot, action: ReportUtil.coupon);
      await Get.dialog(const DiscountDialogWidget(),
          barrierColor: Colors.black.withValues(alpha: 0.8));
      state.priceNumAnimation.value = 2;
    }
  }

  //订阅成功弹窗
  showSubscribeSuccessDialog({required bool vipUpgrade}) async {
    bool showdialog = true;
    Future.delayed(const Duration(seconds: 5), () {
      if (showdialog == true) {
        Get.back();
      }
    });
    await Get.dialog(
      SubscribeSuccessDialog(
        vipUpgrade: vipUpgrade,
      ),
      barrierColor: Colors.black.withValues(alpha: 0.8),
    );
    showdialog = false;
    Future.delayed(const Duration(milliseconds: 500), () {
      if (Get.isRegistered<PurchasePageController>() == true) {
        Get.back();
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    //在购买页停留的时间
    int viewTime =
        CommonUtil.timeDifference(state.openTime, CommonUtil.currentTimeMillis(), unit: 'seconds');
    // 倒计时剩余时间
    int countdown = state.offerCountDown - viewTime;
    if (countdown < 0) {
      countdown = 0;
    }
    Get.find<SPService>().set(spPuchaseOfferCountdown, countdown);
    tabController.dispose();
  }
}
