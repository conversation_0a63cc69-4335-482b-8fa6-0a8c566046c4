import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class SubscribeSuccessDialog extends StatelessWidget {
  final bool vipUpgrade;
  const SubscribeSuccessDialog({super.key, required this.vipUpgrade});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          90.verticalSpaceFromWidth,
          Text(
            'Thanks for Subscribing!'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
                fontStyle: FontStyle.italic,
                color: CommonUtil.colorsUtil(
                    UserService.to.userinfo.value.vipType == 'vip' ? '#C7ABF4' : '#F0BE72')),
          ),
          36.verticalSpace<PERSON><PERSON><PERSON>idth,
          Text(
            "You've unlocked".tr,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w600),
          ),
          6.verticalSpaceFromWidth,
          Image.asset(
            UserService.to.userinfo.value.vipType == 'vip'
                ? Assets.assetsImagesSaleDialogLite
                : Assets.assetsImagesSaleDialogPrem,
            height: 30.w,
          ),
          16.verticalSpaceFromWidth,
          Container(
            width: 260.w,
            height: 260.w,
            color: Colors.transparent,
            child: Lottie.asset(
              'assets/lottie/subscribe_success/subscribe_success.json',
              animate: true,
              repeat: false,
            ),
          ),
          20.verticalSpaceFromWidth,
          TextButton(
            onPressed: () {
              Get.until((route) => route.settings.name == Routes.tabs);
              TabsController.to.handleNavBarTap(2);
            },
            child: Text(
              'Go roleplay with Juicy characters'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  fontStyle: FontStyle.italic),
            ),
          )
        ],
      ),
    );
  }
}
