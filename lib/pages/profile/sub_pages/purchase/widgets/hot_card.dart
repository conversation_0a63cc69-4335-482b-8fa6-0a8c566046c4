import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HotCardWidget extends StatelessWidget {
  const HotCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 214.w,
      width: 1.sw - 32.w,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: 190.w,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(AmorTraService.ar
                        ? Assets.assetsImagesVipCardBgAr
                        : Assets.assetsImagesVipCardBg),
                    fit: BoxFit.fill),
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: AmorTraService.ar ? 8.w : null,
            right: AmorTraService.ar ? null : 8.w,
            child: Image.asset(
              Assets.assetsImagesVipCardIcon,
              width: 90.w,
              fit: BoxFit.contain,
            ),
          ),
          Positioned(
            top: 30.w,
            left: 16.w,
            right: 16.w,
            bottom: 16.w,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  12.verticalSpaceFromWidth,
                  Image.asset(
                    Assets.assetsImagesVipCardTitle,
                    height: 20.w,
                  ),
                  8.verticalSpaceFromWidth,
                  CommonUtil.gradientText(
                    text: 'Platinum Membership'.tr,
                    colors: [
                      CommonUtil.colorsUtil('#6C88F1'),
                      CommonUtil.colorsUtil('#495CA5'),
                    ],
                    textStyle: TextStyle(fontSize: 19.sp, fontStyle: FontStyle.italic, height: 1.2),
                  ),
                  8.verticalSpaceFromWidth,
                  desWidget(text: 'vip card des 1'.tr),
                  2.verticalSpaceFromWidth,
                  desWidget(text: 'vip card des 2'.tr),
                  2.verticalSpaceFromWidth,
                  desWidget(text: 'vip card des 3'.tr),
                  2.verticalSpaceFromWidth,
                  desWidget(text: 'vip card des 4'.tr),
                  2.verticalSpaceFromWidth,
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget desWidget({required String text}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(6.r),
          bottomLeft: Radius.circular(6.r),
          bottomRight: Radius.circular(6.r),
        ),
        color: Colors.black.withValues(alpha: 0.1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            Assets.assetsImagesVipCardDes,
            width: 5.w,
          ),
          4.horizontalSpace,
          Container(
            constraints: BoxConstraints(maxWidth: 280.w),
            child: Text(
              text,
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
