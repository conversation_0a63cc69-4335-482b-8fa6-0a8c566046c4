import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:slide_countdown/slide_countdown.dart';
import 'package:intl/intl.dart';

class PurchaseGemsWidget extends GetView<PurchasePageController> {
  const PurchaseGemsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: CommonUtil.appBarHeight() + CommonUtil.statusBarHeight(context),
        ),
        const Spacer(),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Image.asset(
                    Assets.assetsImagesSessionGems,
                    width: 22.w,
                  ),
                  3.horizontalSpace,
                  Text(
                    'Amor Gems',
                    style: TextStyle(
                        fontSize: 20.sp,
                        color: AppColor.colorsUtil('#F0BE72'),
                        fontWeight: FontWeight.w900,
                        fontStyle: FontStyle.italic),
                  ),
                ],
              ),
              6.verticalSpaceFromWidth,
              ...controller.state.gemsTextList.map(
                (e) => Padding(
                  padding: EdgeInsets.only(bottom: 8.w),
                  child: Text(
                    e,
                    style: TextStyle(
                      fontSize: controller.state.gemsTextList.indexOf(e) == 0 ? 14.sp : 11.sp,
                      color: controller.state.gemsTextList.indexOf(e) == 0
                          ? AppColor.colorsUtil('#CFD2C6').withValues(alpha: 0.8)
                          : Colors.white,
                      fontStyle:
                          controller.state.gemsTextList.indexOf(e) == 0 ? FontStyle.italic : null,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        10.verticalSpaceFromWidth,
        Container(
          width: 1.sw,
          decoration: BoxDecoration(
            color: AppColor.colorsUtil('#18191B'),
            borderRadius:
                BorderRadius.only(topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          ),
          padding: EdgeInsets.fromLTRB(16.w, 10.w, 16.w, CommonUtil.bottomBarHeight()),
          child: GetBuilder<PurchasePageController>(
            id: 'purchase_gems',
            builder: (controller) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  constraints: BoxConstraints(
                    maxHeight: 108.w * 3 + 7.w * 3,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                          controller.state.storeGemsConfigList.length,
                          (index) => priceItemWidget(
                              index: index,
                              model: controller.state.storeGemsConfigList.elementAt(index))),
                    ),
                  ),
                ),
                7.verticalSpaceFromWidth,
                // // expireWidget(),
                // 14.verticalSpaceFromWidth,
                if (controller.state.storeGemsConfigList.isNotEmpty)
                  GradientColorBtn(
                    height: 48.w,
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                    text: 'Get'.tr,
                    textStyle: TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w600),
                    onTap: () => controller.getPayParam(),
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.toNamed(Routes.webview, parameters: {
                          'url': AppService.configModel.userAgreement ?? defaultUserAgreement
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 10.w),
                        child: Text(
                          'Privacy and terms'.tr,
                          style: TextStyle(
                              fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                        ),
                      ),
                    ),
                    // Text(
                    //   'Cancel anytime',
                    //   style: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                    // ),
                  ],
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  //价格
  Widget priceItemWidget({
    required int index,
    required AppPurchaseModel model,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 7.w),
      child: InkWell(
        onTap: () => controller.selectGemsPurchaseItem(index: index),
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(top: 10.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              height: 98.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  width: 2,
                  color: AppColor.colorsUtil('#CECAC3'),
                ),
                gradient: model.selected == true
                    ? LinearGradient(
                        colors: [
                          AppColor.colorsUtil('#FFDCA4'),
                          AppColor.colorsUtil('#C8984A'),
                        ],
                      )
                    : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        model.serviceProductDetails?.title ?? '',
                        style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w600,
                            color: model.selected == true
                                ? AppColor.colorsUtil('#865A20')
                                : Colors.white,
                            height: 19 / 15,
                            fontStyle: FontStyle.italic),
                      ),
                      const Spacer(),
                      Image.asset(
                        Assets.assetsImagesSessionGems,
                        width: 22.w,
                      ),
                      2.horizontalSpace,
                      Text(
                        //千位分隔符
                        NumberFormat('#,###,###').format(model.serviceProductDetails?.gems ?? 0),
                        style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w600,
                            color: model.selected == true
                                ? AppColor.colorsUtil('#865A20')
                                : Colors.white,
                            height: 19 / 15,
                            fontStyle: FontStyle.italic),
                      ),
                    ],
                  ),
                  10.verticalSpaceFromWidth,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: controller.state.priceUnit,
                              style: TextStyle(
                                fontFamily: "",
                                fontSize: 26.sp,
                                height: 1.2,
                                color: model.selected == true
                                    ? AppColor.colorsUtil('#865A20')
                                    : AppColor.colorsUtil('#F0BE72'),
                                fontWeight: FontWeight.w700,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                            TextSpan(
                              text: (model.productDetails!.rawPrice.toStringAsFixed(
                                  model.productDetails!.price.contains('.') ? 2 : 0)),
                              style: TextStyle(
                                fontSize: 26.sp,
                                height: 1.2,
                                color: model.selected == true
                                    ? AppColor.colorsUtil('#865A20')
                                    : AppColor.colorsUtil('#F0BE72'),
                                fontWeight: FontWeight.w700,
                                fontStyle: FontStyle.italic,
                                fontFamily: fontBeVietnamPro,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Text(
                      //   '${controller.state.priceUnit}${(model.productDetails!.rawPrice.toStringAsFixed(model.productDetails!.price.contains('.') ? 2 : 0))}',
                      //   style: TextStyle(
                      //     fontSize: 26.sp,
                      //     height: 1.2,
                      //     color: model.selected == true
                      //         ? AppColor.colorsUtil('#865A20')
                      //         : AppColor.colorsUtil('#F0BE72'),
                      //     fontWeight: FontWeight.w700,
                      //     fontStyle: FontStyle.italic,
                      //   ),
                      // ),
                      Text(
                        model.serviceProductDetails?.unitPrice ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: model.selected == true
                              ? AppColor.colorsUtil('#865A20')
                              : AppColor.colorsUtil('#F0BE72'),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //倒计时
  Widget expireWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '😁 Discount offers expire in ',
          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w300),
        ),
        SlideCountdownSeparated(
          duration: const Duration(seconds: 1000),
          separatorType: SeparatorType.symbol,
          decoration: BoxDecoration(
            color: AppColor.colorsUtil('#FF4300'),
            borderRadius: BorderRadius.circular(3),
          ),
          separatorStyle: TextStyle(
            fontSize: 13.sp,
            color: AppColor.primaryText,
            fontWeight: FontWeight.w600,
          ),
          style: TextStyle(
              fontSize: 13.sp, color: Colors.white, fontWeight: FontWeight.w600, height: 1.2),
          shouldShowDays: (p0) => false,
          showZeroValue: true,
          onDone: () {
            Get.back();
          },
          onChanged: (value) {},
        ),
      ],
    );
  }
}
