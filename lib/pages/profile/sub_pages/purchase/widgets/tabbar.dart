import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/controller.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/widgets/custom_widget/custom_tabbar.dart' as customtabbar;
import 'package:get/get.dart';

class PurchaseTabbarWidget extends GetView<PurchasePageController> {
  final List<Widget> tabs;
  const PurchaseTabbarWidget({super.key, required this.tabs});

  @override
  Widget build(BuildContext context) {
    return customtabbar.TabBar(
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      controller: controller.tabController,
      indicator: const BoxDecoration(
        image: DecorationImage(
          fit: BoxFit.contain,
          image: AssetImage(Assets.assetsImagesInnerTabbarIndex),
        ),
      ),
      indicatorPadding: const EdgeInsets.fromLTRB(0, 26, 0, 12),
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      labelColor: AppColor.colorsUtil('#F0BE72'),
      unselectedLabelColor: AppColor.primaryText,
      labelStyle: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w700,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.normal,
      ),
      tabs: tabs,
    );
  }
}
