import 'dart:io';

import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:slide_countdown/slide_countdown.dart';

import 'hot_card.dart';

class PurchaseHotWidget extends GetView<PurchasePageController> {
  const PurchaseHotWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        HotCardWidget(),
        10.verticalSpaceFromWidth,
        GetBuilder<PurchasePageController>(
          id: 'purchase_vip',
          builder: (controller) {
            return Container(
              width: 1.sw,
              height: 270.w,
              color: Colors.transparent,
              child: controller.state.storeSvipConfigList.isNotEmpty
                  ? vipProductWidget(productList: controller.state.storeSvipConfigList)
                  : Container(),
            );
          },
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  Get.toNamed(Routes.webview, parameters: {
                    'url': AppService.configModel.userAgreement ?? defaultUserAgreement
                  });
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.w),
                  child: Text(
                    'Privacy and Terms'.tr,
                    style: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (AppService.audit == true)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              'subscribe note'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 8.sp, color: CommonUtil.colorsUtil('#4E4E4E')),
            ),
          ),
        SizedBox(height: CommonUtil.bottomBarHeight()),
      ],
    );
  }

  Widget vipProductWidget({required List<AppPurchaseModel> productList}) {
    int billedIndex = controller.state.premiumBilledIndex;
    AppPurchaseModel selectedProduct = productList[billedIndex];
    double price = selectedProduct.offerPrice != null
        ? selectedProduct.offerPrice!
        : selectedProduct.productDetails!.rawPrice;
    int timeUnit = selectedProduct.serviceProductDetails?.num ?? 7;
    //每日价格
    String dailyPrice = (price / timeUnit).toStringAsFixed(2);
    //审核模式
    bool isAudit = AppService.audit;
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 56.w,
            child: Stack(
              children: [
                //时间切换
                Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    height: 28.w,
                    decoration: BoxDecoration(
                      color: AppColor.colorsUtil('#121212'),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          child: Text(
                            'Billed'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                        VerticalDivider(
                          width: 1,
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                        4.horizontalSpace,
                        ...productList.map((e) {
                          int itemIndex = productList.indexOf(e);
                          bool selected = billedIndex == itemIndex;
                          return Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.w),
                            // margin: EdgeInsets.symmetric(vertical: 3.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6.r),
                              color: selected ? AppColor.colorsUtil('#95B3FF') : Colors.transparent,
                            ),
                            child: Text(
                              (e.serviceProductDetails?.subscriptionCycle ?? ''),
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontStyle: FontStyle.italic,
                                fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
                                color:
                                    selected ? Colors.white : Colors.white.withValues(alpha: 0.5),
                              ),
                            ),
                          );
                        }),
                        4.horizontalSpace,
                      ],
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      58.horizontalSpace,
                      ...productList.map((e) {
                        int itemIndex = productList.indexOf(e);
                        return InkWell(
                          onTap: () {
                            controller.state.premiumBilledIndex = itemIndex;
                            Analytics().logEvent(
                                itemIndex == 0
                                    ? Analytics.clickWeekly
                                    : itemIndex == 1
                                        ? Analytics.clickMonthly
                                        : Analytics.clickYearly,
                                screen: Analytics.pageHot);
                            AdjustUtil.adjustEvent(event: AdjustUtil.select);
                            controller.update(['purchase_vip']);
                          },
                          child: SizedBox(width: 62.w, height: double.infinity),
                        );
                      }),
                    ],
                  ),
                )
              ],
            ),
          ),
          //价格信息
          SizedBox(
            height: 93.w,
            width: double.infinity,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    height: 80.w,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        width: 2,
                        color: selectedProduct.offerPrice == null
                            ? AppColor.colorsUtil('#CECAC3')
                            : AppColor.colorsUtil('#88BAE8'),
                      ),
                      gradient: selectedProduct.offerPrice == null
                          ? null
                          : LinearGradient(colors: [
                              AppColor.colorsUtil('#00567B80').withValues(alpha: 0.5),
                              Colors.transparent
                            ], begin: Alignment.bottomCenter, end: Alignment.topCenter),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: AmorTraService.ar
                                ? EdgeInsets.only(right: 16.w, left: 5.w)
                                : EdgeInsets.only(left: 16.w, right: 5.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  selectedProduct.offerPrice == null
                                      ? 'No Offer Available'.tr
                                      : 'New Subscribers Only'.tr,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: -0.3,
                                    height: 16 / 13,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                                //终身会员不展示每天价格
                                if (timeUnit != 99999) 2.verticalSpaceFromWidth,
                                if (timeUnit != 99999)
                                  CommonUtil.gradientText(
                                    text: 'daily price'
                                        .trArgs([(controller.state.priceUnit), dailyPrice]),
                                    colors: [
                                      AppColor.colorsUtil('#EAC282'),
                                      AppColor.colorsUtil('#C69A51')
                                    ],
                                    textStyle: TextStyle(
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.w400,
                                      fontStyle: FontStyle.italic,
                                      height: 34 / 30,
                                      fontFamily: "",
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: controller.state.priceUnit,
                                    style: TextStyle(
                                      fontFamily: "",
                                      fontSize: 28.sp,
                                      fontWeight: FontWeight.w700,
                                      color: AppColor.colorsUtil('#F0BE72'),
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        '${selectedProduct.offerPrice ?? selectedProduct.productDetails?.rawPrice}',
                                    style: TextStyle(
                                      fontSize: 28.sp,
                                      fontWeight: FontWeight.w700,
                                      color: AppColor.colorsUtil('#F0BE72'),
                                      fontStyle: FontStyle.italic,
                                      fontFamily: fontBeVietnamPro,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                //hot
                if (selectedProduct.serviceProductDetails?.hot == true)
                  Positioned(
                    top: 0,
                    left: 6.w,
                    child: SizedBox(
                        height: 23.w, child: Image.asset(Assets.assetsImagesSaleProductHot)),
                  ),
                if (selectedProduct.offerPrice != null)
                  Positioned(
                    right: 0,
                    top: 6.w,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(14.r),
                          topRight: Radius.circular(14.r),
                        ),
                        gradient: LinearGradient(
                          colors: [AppColor.colorsUtil('#FBA000'), AppColor.colorsUtil('#FC680B')],
                        ),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.w),
                      child: Text(
                        isAudit
                            ? '${((selectedProduct.productDetails!.rawPrice - selectedProduct.offerPrice!) / selectedProduct.productDetails!.rawPrice * 100).toStringAsFixed(0)}% off now for the 1st period'
                            : '${((selectedProduct.productDetails!.rawPrice - selectedProduct.offerPrice!) / selectedProduct.productDetails!.rawPrice * 100).toStringAsFixed(0)}% off!',
                        style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            height: 15 / 12,
                            fontStyle: FontStyle.italic),
                      ),
                    ),
                  )
              ],
            ),
          ),
          Expanded(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: 6.w),
              // height: 36.w,
              child: (selectedProduct.offerPrice != null || isAudit) && timeUnit != 99999
                  ? Text(
                      isAudit
                          ? (selectedProduct.offerPrice == null
                              ? "By tapping “Subscribe”, the subscription will charge you ${controller.state.priceUnit}${selectedProduct.productDetails?.rawPrice ?? 999} for the billing period selected above. It will auto-renew for ${controller.state.priceUnit}${selectedProduct.productDetails?.rawPrice ?? 999}, the original price, and the same package until you cancel at ${Platform.isIOS ? 'AppStore' : 'GooglePlay'} Settings."
                              : "By tapping “Subscribe”, the subscription will charge you ${controller.state.priceUnit}${selectedProduct.offerPrice ?? 999}, as an introductory offer, for the 1st billing period selected above. It will auto-renew for ${controller.state.priceUnit}${selectedProduct.productDetails?.rawPrice ?? 999}, the original price, and the same package until you cancel at ${Platform.isIOS ? 'AppStore Settings' : 'GooglePlay Subscriptions'}.")
                          : "${controller.state.priceUnit}${selectedProduct.productDetails?.rawPrice ?? 999} will be billed after the 1st period.",
                      style: TextStyle(
                        fontSize: isAudit ? 8.sp : 10.sp,
                        color: Colors.white.withValues(alpha: isAudit ? 1.0 : 0.3),
                        fontWeight: FontWeight.w400,
                        height: 12.65 / 10,
                        fontFamily: "",
                        fontStyle: FontStyle.italic,
                      ),
                    )
                  : null,
            ),
          ),
          // expireWidget(
          //     show: controller.state.hasOffer == true &&
          //         selectedProduct.offerPrice != null &&
          //         UserService.to.isVip == false),
          8.verticalSpaceFromWidth,
          InkWell(
            onTap: () => controller.getPayParam(),
            child: Container(
              width: double.infinity,
              height: 48.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(48.w / 2),
                image: UserService.to.isVip
                    ? null
                    : DecorationImage(
                        image: AssetImage(Assets.assetsImagesSubscribeBtnBg),
                        fit: BoxFit.fill,
                      ),
                color: CommonUtil.colorsUtil('#323232'),
              ),
              alignment: Alignment.center,
              child: Text(
                UserService.to.isVip ? 'Subscribed'.tr : 'Subscribe'.tr,
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.w700,
                  color: CommonUtil.colorsUtil(UserService.to.isVip ? '#868686' : '#495CA5'),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  //倒计时
  Widget expireWidget({required bool show}) {
    return SizedBox(
      height: 25.w,
      child: Opacity(
        opacity: show ? 1 : 0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '😁 Offers expire in ',
              style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w300,
                  color: AppColor.colorsUtil('#FF521B')),
            ),
            SlideCountdownSeparated(
              duration: Duration(seconds: controller.state.offerCountDown),
              separatorType: SeparatorType.symbol,
              decoration: BoxDecoration(
                color: AppColor.colorsUtil('#FF4300'),
                borderRadius: BorderRadius.circular(3),
              ),
              separatorStyle: TextStyle(
                fontSize: 14.sp,
                color: AppColor.primaryText,
                fontWeight: FontWeight.w600,
              ),
              separatorPadding: EdgeInsets.symmetric(horizontal: 4.w),
              style: TextStyle(
                  fontSize: 14.sp, color: Colors.white, fontWeight: FontWeight.w600, height: 1.2),
              shouldShowDays: (p0) => false,
              shouldShowHours: (p0) => false,
              showZeroValue: true,
              onDone: () {
                Get.back();
              },
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }
}
