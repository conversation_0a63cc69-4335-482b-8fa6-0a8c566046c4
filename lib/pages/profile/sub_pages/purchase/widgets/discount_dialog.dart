import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DiscountDialogWidget extends StatelessWidget {
  const DiscountDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.w,
        height: 280.w,
        decoration: const BoxDecoration(
            image: DecorationImage(image: AssetImage(Assets.assetsImagesSessionBottomDialogBg))),
        padding: EdgeInsets.symmetric(horizontal: 33.w),
        // margin: EdgeInsets.only(bottom: 100.h),
        child: Column(
          children: [
            40.verticalSpaceFromWidth,
            Image.asset(
              Assets.assetsImagesOfferDialogNum,
              height: 77.w,
            ),
            40.verticalSpaceFromWidth,
            CommonUtil.gradientText(
                text: 'Timed Offer: 00:30:00',
                colors: [
                  AppColor.colorsUtil('#FC680B'),
                  AppColor.colorsUtil('#FBA000'),
                ],
                textStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w700)),
            20.verticalSpaceFromWidth,
            GradientColorBtn(
              height: 42.w,
              colors: [
                AppColor.colorsUtil('#FFDCA4'),
                AppColor.colorsUtil('#C8984A'),
              ],
              text: 'Get it now',
              textStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
              onTap: () {
                Get.back();
              },
            ),
            3.verticalSpaceFromWidth,
            Opacity(
              opacity: 0.4,
              child: CommonUtil.gradientText(
                text: 'For 1st time subscribers only',
                colors: [
                  AppColor.colorsUtil('#F0F4E7'),
                  AppColor.colorsUtil('#CFD2C6'),
                ],
                textStyle: TextStyle(
                    fontSize: 12.sp, fontWeight: FontWeight.w500, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
