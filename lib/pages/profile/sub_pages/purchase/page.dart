import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/widgets/tabbarview_gems.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/widgets/tabbarview_hot.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';
import 'widgets/tabbar.dart';

class PurchaseSheet {
  static String modelIdSource = '';
  //rechargeType : 0 购买会员  1 购买宝石
  static Future show(
      {String? eventAction,
      int? modelId,
      String? msgId,
      String? source,
      required String page,
      int buyType = 0}) async {
    modelIdSource = 'from{$page}{${modelId ?? ''}}';
    if (Get.isRegistered<PurchasePageController>() == true) {
      Get.delete<PurchasePageController>(force: true);
    }
    Get.lazyPut(() => PurchasePageController(buyType: buyType, source: source));
    //检查有没有上传 adjust ID
    AdjustUtil.uploadAdid();
    ReportUtil.reportPurchase(
        page: buyType == 0 ? ReportUtil.hot : ReportUtil.gem,
        action: ReportUtil.view,
        value: 'from{$page}{${modelId ?? ''}}');
    if (source != null && source.startsWith('dev') == false) {
      Analytics().logEvent(source);
    }
    await Get.bottomSheet(const PurchasePage(),
        ignoreSafeArea: false, isScrollControlled: true, enableDrag: false);
    Get.delete<PurchasePageController>(force: true);
    //记录关闭的时间
    Get.find<SPService>().set(spPuchaseCloseTime, CommonUtil.currentTimeMillis());
    Future.delayed(const Duration(milliseconds: 500), () {
      if (Get.currentRoute == Routes.tabs &&
          TabsController.to.page == 3 &&
          Get.isRegistered<ProfilePageController>()) {
        ProfilePageController.to.showSubscribeSuccess();
      }
    });
  }
}

class PurchasePage extends GetView<PurchasePageController> {
  const PurchasePage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: CustomBackButton(
            img: Assets.assetsImagesCloseWhite,
            width: 28,
            back: () {
              Navigator.pop(context);
            },
          ),
          actions: [
            InkWell(
              onTap: () {
                controller.restore();
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Restore'.tr,
                  style: TextStyle(fontSize: 14.sp, color: Colors.white),
                ),
              ),
            ),
          ],
          title: const PurchaseTabbarWidget(
            tabs: [
              Tab(text: 'Hot'),
              Tab(text: 'Gems'),
            ],
          ),
        ),
        // backgroundColor: Colors.amber,
        body: Stack(
          children: [
            Positioned.fill(
              child: Image.asset(
                Assets.assetsImagesPurchaseBg,
                fit: BoxFit.cover,
              ),
            ),
            TabBarView(
              controller: controller.tabController,
              children: const [
                KeepAliveWrapper(
                  child: PurchaseHotWidget(),
                ),
                KeepAliveWrapper(
                  child: PurchaseGemsWidget(),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
