import 'package:amor_app/common/models/profile_model/purchase_config_model.dart';
import 'package:amor_app/common/utils/in_app_purchase/in_app_purchase.dart';
import 'package:get/get.dart';

class PurchasePageControllerState {
  //tabbar下标
  var tabbarIndex = 0.obs;
  //vip card 下标
  // int vipCardIndex = 1;
  //lite billed 下标
  // int liteBilledIndex = 0;
  //premiun billed 下标
  int premiumBilledIndex = 0;
  List<String> gemsTextList = [
    'the one and only tokens that get you to'.tr,
    '💎  ${'Unlock and talk to all Amors'.tr}',
    '😘  ${'Customize your favorite Cards'.tr}',
    '🤩  ${'All other features that require Gems'.tr}',
  ];

  //购买会员配置
  PurchaseConfigModel vipConfigmodel = PurchaseConfigModel();

  //购买宝石配置
  PurchaseConfigModel gemsConfigmodel = PurchaseConfigModel();
  List<PurchaseItemModel> get gemsConfigList => gemsConfigmodel.gem?.recharges ?? [];
  //打开购买页时的时间戳
  int openTime = 0;
  //优惠倒计时
  int offerCountDown = 1800;
  //弹窗关闭后展示优惠价格动画
  var priceNumAnimation = 0.obs;
  //购买会员配置
  // List<AppPurchaseModel> storeVipConfigList = [];
  List<AppPurchaseModel> storeSvipConfigList = [];
  //购买宝石配置
  List<AppPurchaseModel> storeGemsConfigList = [];
  //货币单位
  String priceUnit = '\$';
  //是否有优惠
  bool hasOffer = false;
  //重新获取商店商品信息次数
  int cacheCount = 0;
}
