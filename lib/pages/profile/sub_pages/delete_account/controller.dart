import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:get/get.dart';

class DeleteAccountPageController extends GetxController {
  delAccount() async {
    bool? confirm = await Get.dialog(
      CustomDialogWidget(
        title: 'Are you sure to delete your account?'.tr,
        subTitle: "All the information will be erased".tr,
        confirmTitle: 'Delete account'.tr,
      ),
    );
    if (confirm == true) {
      bool result = await ProfileApis.delAccount();
      if (result) {
        ReportUtil.reportEvents(page: ReportUtil.setting, action: ReportUtil.deleteacct);
        UserService.to.clearUserInfo();
        Get.until((route) => route.settings.name == Routes.tabs);
      }
    }
  }
}
