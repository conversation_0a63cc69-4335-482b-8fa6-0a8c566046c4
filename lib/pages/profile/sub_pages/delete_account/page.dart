import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class DeleteAccountPage extends GetView<DeleteAccountPageController> {
  const DeleteAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    String text = 'delete account note 1'.tr;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delete account'),
        centerTitle: true,
        leading: const CustomBackButton(),
      ),
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        padding: REdgeInsets.symmetric(horizontal: 12, vertical: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 13.sp,
                height: 1.3,
                color: AppColor.primaryText,
              ),
            ),
            20.verticalSpace,
            Text(
              'delete account note 2'.tr,
              style: TextStyle(
                fontSize: 13.sp,
                color: AppColor.colorsUtil('#F53130'),
              ),
            ),
            20.verticalSpace,
            Text(
              'delete account note 3'.tr,
              style: TextStyle(fontSize: 12.sp, color: AppColor.primaryText.withValues(alpha: 0.5)),
            ),
            20.verticalSpace,
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.delAccount();
              },
              child: Container(
                alignment: Alignment.center,
                padding: REdgeInsets.symmetric(vertical: 14),
                margin: REdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: AppColor.colorsUtil('#F53130'),
                  borderRadius: BorderRadius.circular(40.r),
                ),
                child: Text(
                  'Delete account'.tr,
                  style: TextStyle(
                      fontSize: 16.sp, fontWeight: FontWeight.w500, color: AppColor.primaryText),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
