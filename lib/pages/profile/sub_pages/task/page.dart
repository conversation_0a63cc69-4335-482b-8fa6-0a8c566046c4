import 'package:amor_app/common/models/profile_model/profile_task.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:slide_countdown/slide_countdown.dart';

import 'controller.dart';

class TaskPage extends GetView<TaskPageController> {
  const TaskPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        leading: const CustomBackButton(),
        backgroundColor: Colors.transparent,
        actions: _actions(),
      ),
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Image.asset(Assets.assetsImagesInnerTopBg),
          ),
          Positioned(
            top: 37.w + CommonUtil.statusBarHeight(context),
            left: 0,
            right: 0,
            bottom: 0,
            child: GetBuilder<TaskPageController>(
              builder: (controller) => Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        Assets.assetsImagesTaskTopIcon,
                        width: 120.w,
                      ),
                      20.horizontalSpace,
                      Column(
                        children: [
                          12.verticalSpaceFromWidth,
                          Text(
                            'Quest Board'.tr,
                            style: TextStyle(
                                fontSize: 20.sp,
                                fontStyle: FontStyle.italic,
                                color: Colors.white,
                                fontWeight: FontWeight.w800),
                          ),
                          2.verticalSpaceFromWidth,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              twoLinesWidget(
                                  title: 'Actionable'.tr,
                                  subtit: controller.taskModel.actionable ?? 0),
                              twoLinesWidget(
                                  title: 'OnGoing'.tr, subtit: controller.taskModel.onGoing ?? 0),
                              twoLinesWidget(
                                  title: ' Available'.tr,
                                  subtit: controller.taskModel.available ?? 0),
                            ],
                          ),
                        ],
                      )
                    ],
                  ),
                  13.verticalSpaceFromWidth,
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(13.w, 0, 13.w, 13.w),
                      child: Column(
                        children: [
                          if (controller.taskModel.cycleList.isNotEmpty)
                            taskContainer(
                                taskType: 'daily', taskList: controller.taskModel.cycleList),
                          if (controller.taskModel.cycleList.isNotEmpty) 20.verticalSpaceFromWidth,
                          if (controller.taskModel.newbieList.isNotEmpty)
                            taskContainer(
                                taskType: 'welcoming', taskList: controller.taskModel.newbieList),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget taskContainer({required String taskType, required List<TaskListItemModel> taskList}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: AppColor.colorsUtil('#17141F'),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          SizedBox(
            height: 62.w,
            child: Row(
              children: [
                Image.asset(
                  taskType == 'daily'
                      ? Assets.assetsImagesTaskDailyIcon
                      : Assets.assetsImagesTaskWelcomingIcon,
                  width: 30.w,
                ),
                10.horizontalSpace,
                Text(
                  taskType == 'daily' ? 'Daily Tasks'.tr : 'Welcoming Package'.tr,
                  style: TextStyle(
                      fontSize: 17.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontStyle: FontStyle.italic),
                ),
              ],
            ),
          ),
          Divider(
            color: Colors.white.withValues(alpha: 0.2),
            height: 0.5,
          ),
          ...List.generate(
            taskList.length,
            (index) => taskItemWidget(
              showLine: index < taskList.length - 1,
              model: taskList.elementAt(index),
              index: index,
            ),
          )
        ],
      ),
    );
  }

  Widget taskItemWidget(
      {required bool showLine, required TaskListItemModel model, required int index}) {
    return Column(
      children: [
        SizedBox(
          height: 104.w,
          child: Row(
            children: [
              CachedNetworkImage(
                imageUrl: model.icon ?? '',
                width: 64.w,
              ),
              12.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Text(
                          model.title ?? '',
                          style: TextStyle(
                              height: 19 / 15,
                              fontSize: 15.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.w700),
                        ),
                        if (model.taskLabel == 'ads' &&
                            (model.canDoAfterTime ?? 0) > 0 &&
                            model.state == 'Start')
                          expireWidget(model: model, index: index),
                      ],
                    ),
                    4.verticalSpaceFromWidth,
                    Text(
                      model.content ?? '',
                      style: TextStyle(
                        fontSize: 12.sp,
                        height: 1.2,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              10.horizontalSpace,
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      controller.taskAction(model);
                    },
                    child: Container(
                      width: 70.w,
                      height: 30.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.w / 2),
                        border: model.state == 'Claim' || model.state == 'Done'
                            ? null
                            : Border.all(
                                color: (model.state == 'Start' &&
                                        (model.canDoAfterTime ?? 0) > 0) //广告 在倒计时
                                    ? AppColor.colorsUtil('#535353')
                                    : model.state == 'On_it'
                                        ? AppColor.colorsUtil('#FF6B00')
                                        : AppColor.colorsUtil('#F0BE72'),
                              ),
                        gradient: model.state == 'Claim'
                            ? LinearGradient(colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A'),
                              ])
                            : null,
                        color: model.state == 'Done' ? AppColor.colorsUtil('#232323') : null,
                      ),
                      alignment: Alignment.center,
                      child: model.state == 'Locked'
                          ? Image.asset(
                              Assets.assetsImagesTaskItemLock,
                              width: 18.w,
                            )
                          : Text(
                              model.state == 'On_it' ? 'On it' : model.state ?? '',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: model.state == 'Claim'
                                    ? Colors.white
                                    : model.state == 'On_it'
                                        ? AppColor.colorsUtil('#FF6B00')
                                        : (model.state == 'Start' &&
                                                (model.canDoAfterTime ?? 0) > 0) //广告 在倒计时
                                            ? AppColor.colorsUtil('#999999')
                                            : model.state == 'Done'
                                                ? AppColor.colorsUtil('#676767')
                                                : AppColor.colorsUtil('#F0BE72'),
                              ),
                            ),
                    ),
                  ),
                  if ((model.gems ?? 0) > 0)
                    Padding(
                      padding: EdgeInsets.only(top: 4.w),
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.white,
                              letterSpacing: -0.1,
                              fontFamily: fontBeVietnamPro,
                              fontWeight: FontWeight.w600),
                          children: [
                            WidgetSpan(
                              child: Text(
                                '+',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  height: 1.2,
                                ),
                              ),
                            ),
                            WidgetSpan(
                              child: Padding(
                                padding: EdgeInsets.fromLTRB(2, 0, 2, 2.w),
                                child: Image.asset(
                                  Assets.assetsImagesSessionGems,
                                  width: 12.w,
                                ),
                              ),
                            ),
                            WidgetSpan(
                              child: Text(
                                '${model.gems ?? 0}',
                                style: TextStyle(
                                    height: 15 / 12, fontSize: 12.sp, fontWeight: FontWeight.w600),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        if (showLine)
          Divider(
            color: Colors.white.withValues(alpha: 0.2),
            height: 0.5,
          ),
      ],
    );
  }

  //倒计时
  Widget expireWidget({required TaskListItemModel model, required int index}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: SlideCountdownSeparated(
        duration: Duration(seconds: model.canDoAfterTime ?? 180),
        separatorType: SeparatorType.symbol,
        decoration: BoxDecoration(
          color: AppColor.colorsUtil('#FF521B'),
          borderRadius: BorderRadius.circular(3),
        ),
        separatorStyle: TextStyle(
          fontSize: 15.sp,
          color: AppColor.primaryText,
          fontWeight: FontWeight.w600,
        ),
        separatorPadding: EdgeInsets.symmetric(horizontal: 4.w),
        style: TextStyle(
            fontSize: 9.sp, color: Colors.white, fontWeight: FontWeight.w600, height: 1.2),
        shouldShowDays: (p0) => false,
        shouldShowHours: (p0) => false,
        showZeroValue: true,
        onDone: () {
          controller.onAdsCountDownEnd(model: model, index: index);
        },
      ),
    );
  }

  //宝石余额
  List<Widget> _actions() {
    return [
      Container(
        height: 26.w,
        margin: EdgeInsets.only(right: 16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(26.w / 2),
          color: AppColor.colorsUtil('#D9BD90'),
          border: Border.all(
            color: AppColor.colorsUtil('#A9936F'),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Row(
          children: [
            Image.asset(
              Assets.assetsImagesSessionGems,
              fit: BoxFit.contain,
              width: 20.w,
            ),
            3.horizontalSpace,
            Obx(
              () => AnimatedFlipCounter(
                value: controller.gemNum.value,
                duration: const Duration(milliseconds: 500),
                textStyle: TextStyle(
                  fontSize: 12.sp,
                  fontFamily: fontBeVietnamPro,
                  color: AppColor.primaryText,
                  fontWeight: FontWeight.w500,
                  height: 1.2,
                ),
              ),
            ),
          ],
        ),
      ),
    ];
  }

  //任务数量
  Widget twoLinesWidget({required String title, required int subtit}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w500,
                color: AppColor.primaryText.withValues(alpha: 0.8)),
          ),
          Text(
            '$subtit',
            style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: AppColor.primaryText.withValues(alpha: 0.8)),
          ),
        ],
      ),
    );
  }
}
