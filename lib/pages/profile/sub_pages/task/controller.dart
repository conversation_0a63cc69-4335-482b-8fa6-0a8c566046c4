import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/profile_task.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TaskPageController extends GetxController {
  static TaskPageController get to => Get.find();
  TaskModel taskModel = TaskModel();
  var gemNum = 0.obs;
  @override
  onInit() {
    super.onInit();
    gemNum.value = UserService.to.userinfo.value.gems ?? 0;
    getTaskBoard();
    ReportUtil.reportViews(page: ReportUtil.quests, action: ReportUtil.view);
  }

  //获取任务
  getTaskBoard() async {
    TaskModel? model = await ProfileApis.taskBoard(
      cacheCallBack: (cache) {
        // if (cache != null) {
        //   taskModel = cache;
        //   update();
        // }
      },
    );
    if (model != null) {
      taskModel = model;
      update();
    }
  }

  //点击任务
  taskAction(TaskListItemModel model) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    //领取宝石
    if (model.state == 'Claim') {
      tackClaim(model);
      return;
    }
    //锁定
    if (model.state == 'Locked') {
      return;
    }

    if (model.state == 'Start' || model.state == 'On_it') {
      ReportUtil.reportEvents(
          page: ReportUtil.quests, action: ReportUtil.start, value: '${model.title}');

      if (model.taskLabel == 'sayingHi' ||
          model.taskLabel == 'thePlayer' ||
          model.taskLabel == 'theMaster') {
        Get.until((route) => route.settings.name == Routes.tabs);
        TabsController.to.page = 0;
        return;
      }
      //跳 Chat 页面
      if (model.taskLabel == 'timeToReset' || model.taskLabel == 'theGemCraze') {
        ReportUtil.reportViews(
            page: ReportUtil.quests, action: ReportUtil.go, value: ReportUtil.actionChats);
        Get.until((route) => route.settings.name == Routes.tabs);
        TabsController.to.page = 0;
        return;
      }
      //跳 Set 页面
      if (model.taskLabel == 'beYou') {
        ReportUtil.reportViews(
            page: ReportUtil.quests, action: ReportUtil.go, value: ReportUtil.account);
        Get.offNamedUntil(Routes.editUserinfo, ModalRoute.withName(Routes.tabs));
        return;
      }
      //跳聊天页面
      if (model.taskLabel == 'turnItOn' ||
          model.taskLabel == 'tailoredExperience' ||
          model.taskLabel == 'timeToShare') {
        Map? result = await ProfileApis.taskSessionNo();
        if (result != null && result['sessionNo'] != null) {
          ReportUtil.reportViews(
              page: ReportUtil.quests, action: ReportUtil.go, value: ReportUtil.chatwin);
          Get.offNamedUntil(Routes.session, ModalRoute.withName(Routes.tabs),
              arguments: result['sessionNo']);
        }
        return;
      }
      //评分
      if (model.taskLabel == 'theFan') {
        ratingAction(model);
        return;
      }
      //广告
      if (model.taskLabel == 'ads') {
        /*
        if (model.canDoAfterTime == 0) {
          ApplovinUtil.loadRewardedAd(
            adUnitId: model.adWatchedCount == 0
                ? ApplovinUtil.rewardedAdUnitIdTask1
                : (model.adWatchedCount ?? 0) > 2
                    ? ApplovinUtil.rewardedAdUnitIdTask4
                    : ApplovinUtil.rewardedAdUnitIdTask2_3,
            adTaskId: model.id,
            callBack: (bool success) {
              if (success) {
                // 因为看视频获得的宝石存在随机数且是后端自动增加，所以不能像其他任务一样由前端手动增加，需要调用获取用户信息接口刷新宝石
                getGemsFromService();
              }
            },
          );
        } else {
          Loading.toast('Come back later');
        }
        */
      }
    }
    if (model.state == 'Done' && model.taskLabel == 'ads') {
      Loading.toast('Come back tmr');
    }
  }

  //服务端获取宝石数量
  getGemsFromService() async {
    // refreshGems(0);
    // await UserService.to.getUserInfo();
    // if ((UserService.to.userinfo.value.gems ?? 0) > gemNum.value) {
    //   gemNum.value = (UserService.to.userinfo.value.gems ?? 0);
    // }
    Loading.show();
    Future.delayed(const Duration(milliseconds: 1500), () async {
      await UserService.to.getUserInfo();
      Loading.dismiss();
      if ((UserService.to.userinfo.value.gems ?? 0) > gemNum.value) {
        refreshGems(UserService.to.userinfo.value.gems! - gemNum.value);
      } else {
        refreshGems(0);
      }
    });
  }

  //评分
  ratingAction(TaskListItemModel model) async {
    AppService.appReview();
    bool result = await ProfileApis.taskTheFun();
    if (result) {
      //刷新任务列表
      getTaskBoard();
    }
  }

  //看广告任务倒计时结束
  void onAdsCountDownEnd({required TaskListItemModel model, required int index}) {
    MissionTypeModel typeModel = taskModel.cycle!;
    typeModel.missionList?[index].canDoAfterTime = 0;
    taskModel.cycle = typeModel;
    update();
  }

  //领取宝石
  tackClaim(TaskListItemModel model) async {
    if (model.id == null) {
      return;
    }

    //看视频任务
    if (model.taskLabel == 'ads') {
      // AdMobUtil().playAd((result) {
      //   if (result == true) {
      //     receiveRequest(model.id!, 'AD', model.title ?? '');
      //   }
      // });
      return;
    }

    if (UserService.to.isVip == false) {
      GemsDialog.receiveGemsDialog(
        gemsNum: model.gems ?? 0,
        subTitle: 'Congrats! Reward Earned',
        callBack: ({required int result}) {
          if (result == 1) {
            watchAdReward(model);
          } else {
            receiveRequest(model.id!, 'GENERAL', model.title ?? '');
          }
          return;
        },
      );
    } else {
      receiveRequest(model.id!, 'GENERAL', model.title ?? '');
    }
  }

  //看广告获得双倍奖励
  watchAdReward(TaskListItemModel model) async {
    /*
    ApplovinUtil.loadInterstitialAd(
      adUnitId: ApplovinUtil.interstitialAdUnitIdDouble,
      callBack: (success) {
        if (success) {
          receiveRequest(model.id!, 'AD', model.title ?? '');
        }
      },
    );
    */
  }

  receiveRequest(int id, String claimType, String title) async {
    Map? result = await ProfileApis.taskClaim(id: id, claimType: claimType);
    Loading.dismiss();
    if (result != null && result.containsKey('gems')) {
      //刷新宝石数量
      refreshGems(((result['gems'] ?? 0) as int));
      ReportUtil.reportEvents(
          page: ReportUtil.quests,
          action: claimType == 'GENERAL' ? ReportUtil.claim : ReportUtil.double,
          value: title);
    }
  }

  //展示获取宝石动画
  refreshGems(int getGem) {
    gemNum.value = gemNum.value + getGem;
    GemsAnimation.show();
    //刷新任务列表
    getTaskBoard();
  }

  @override
  void dispose() {
    super.dispose();
    ReportUtil.reportViews(page: ReportUtil.quests, action: ReportUtil.quit);
  }
}
