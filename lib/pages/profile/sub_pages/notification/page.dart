import 'package:amor_app/common/models/profile_model/notification_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';
import 'widgets/expandable_text.dart';

class NotificationPage extends GetView<NotificationPageController> {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'.tr),
        leading: const CustomBackButton(),
      ),
      body: EasyRefresh.builder(
          controller: controller.refreshController,
          onRefresh: () => controller.refreshRequest(),
          onLoad: () => controller.loadMoreRequest(),
          childBuilder: (context, physics) {
            return GetBuilder<NotificationPageController>(
              builder: (controller) => ListView.separated(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
                  physics: physics,
                  itemBuilder: (context, index) {
                    return itemWidget(controller.notificationList.elementAt(index), index);
                  },
                  separatorBuilder: (context, index) => 16.verticalSpaceFromWidth,
                  itemCount: controller.notificationList.length),
            );
          }),
    );
  }

  Widget itemWidget(NotficationModel model, int index) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: AppColor.colorsUtil('#1F1C26'),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (model.messageType == 'gems')
                Image.asset(
                  Assets.assetsImagesSessionGems,
                  width: 20.w,
                ),
              if (model.messageType == 'gems') 8.horizontalSpace,
              Expanded(
                child: EasyRichText(
                  model.title ?? '',
                  defaultStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    height: 18 / 16,
                  ),
                  patternList: model.messageType == 'gems' && model.gems != null
                      ? [
                          EasyRichTextPattern(
                            targetString: '${model.gems} gems',
                            style: TextStyle(
                              color: AppColor.colorsUtil('#F0BE72'),
                            ),
                          ),
                        ]
                      : null,
                ),
              ),
            ],
          ),
          10.verticalSpaceFromWidth,
          ExpandableText(
            text: model.message ?? '',
            maxLines: 2,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white.withValues(alpha: 0.6),
            ),
          ),
          16.verticalSpaceFromWidth,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              model.button != null
                  ? InkWell(
                      onTap: () {
                        controller.buttonOntap(index: index);
                      },
                      child: Container(
                        width: 93.w,
                        height: 32.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(32.w / 2),
                          color: model.claimStatus == 0 || (model.jumpUrl ?? '').isNotEmpty
                              ? Colors.transparent
                              : AppColor.colorsUtil('#2E2B35'),
                          border: model.claimStatus == 0 || (model.jumpUrl ?? '').isNotEmpty
                              ? Border.all(
                                  color: AppColor.colorsUtil('#F0BE72'),
                                )
                              : null,
                        ),
                        alignment: Alignment.center,
                        child: CommonUtil.gradientText(
                            text: model.button ?? '',
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: model.claimStatus == 0 || (model.jumpUrl ?? '').isNotEmpty
                                ? [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')]
                                : [
                                    Colors.white.withValues(alpha: 0.3),
                                    Colors.white.withValues(alpha: 0.3)
                                  ],
                            textStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500)),
                      ),
                    )
                  : Container(),
              Text(
                CommonUtil.convertTimestamp(model.creatTime ?? 0),
                style: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.3)),
              )
            ],
          )
        ],
      ),
    );
  }
}
