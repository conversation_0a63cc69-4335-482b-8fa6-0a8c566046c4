import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';

class ExpandableText extends StatefulWidget {
  final String text;
  final int maxLines;
  final TextStyle style;
  const ExpandableText({
    super.key,
    required this.text,
    required this.maxLines,
    required this.style,
  });

  @override
  State<ExpandableText> createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText> {
  bool expand = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          expand = !expand;
        });
      },
      behavior: HitTestBehavior.translucent,
      child: LayoutBuilder(builder: (context, size) {
        final span = TextSpan(
          text: widget.text,
          style: widget.style,
        );
        //通过TextPainter判断是否超过最大行数
        final tp =
            TextPainter(text: span, maxLines: widget.maxLines, textDirection: TextDirection.ltr);
        tp.layout(maxWidth: size.maxWidth);
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Text(
                widget.text,
                style: widget.style,
                overflow: expand ? null : TextOverflow.ellipsis,
                maxLines: expand ? null : widget.maxLines,
              ),
            ),
            if (tp.didExceedMaxLines)
              Image.asset(
                expand ? Assets.assetsImagesSessionMsgPackup : Assets.assetsImagesSessionMsgUnfold,
                width: 16.w,
              ),
          ],
        );
      }),
    );
  }
}
