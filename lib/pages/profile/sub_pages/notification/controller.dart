import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/notification_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class NotificationPageController extends GetxController {
  static NotificationPageController get to => Get.find();
  late EasyRefreshController refreshController;

  var noticePermission = true.obs;
  List<NotficationModel> notificationList = [];
  int page = 1;
  @override
  void onInit() {
    super.onInit();
    refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    // checkPermission();
    getNoticeList();
    ReportUtil.reportViews(page: ReportUtil.notice, action: ReportUtil.view);
  }

  getNoticeList() async {
    List<NotficationModel> list = await ProfileApis.noticeList(
      page,
      cacheCallBack: (cache) {
        setNoticeList(cache);
      },
    );
    setNoticeList(list);
  }

//填充数据
  setNoticeList(List<NotficationModel> list) async {
    page == 1 ? notificationList.assignAll(list) : notificationList.addAllIf(list.isNotEmpty, list);
    update();
    if (list.isEmpty) {
      refreshController.finishLoad(IndicatorResult.noMore);
    } else {
      if (page == 1) {
        refreshController.resetFooter();
      } else {
        list.isEmpty
            ? refreshController.finishLoad(IndicatorResult.noMore)
            : refreshController.finishLoad(IndicatorResult.success);
      }
    }
  }

  //刷新
  refreshRequest() async {
    page = 1;
    await getNoticeList();
    refreshController.finishRefresh();
  }

  //加载更多
  loadMoreRequest() {
    page++;
    getNoticeList();
  }

  //点击按钮
  buttonOntap({required int index}) async {
    NotficationModel model = notificationList.elementAt(index);
    ReportUtil.reportEvents(page: ReportUtil.notice, action: ReportUtil.click, value: model.title);
    //领取奖励
    if (model.claimStatus == 0 && model.messageType == 'gems') {
      if (model.msgId != null) {
        GemsDialog.receiveGemsDialog(
          gemsNum: model.gems ?? 0,
          subTitle: 'Congrats! Reward Earned',
          callBack: ({required int result}) {
            cliamRequest(index, model, result == 1);
          },
        );
      }
      return;
    }
    //功能跳转
    if (model.jumpUrl != null) {
      Map? param = schemeFormat(model.jumpUrl!);
      //购买
      if (model.jumpUrl!.contains('purchase')) {
        if (param != null && param.containsKey('type')) {
          await PurchaseSheet.show(buyType: int.parse(param['type']), page: ReportUtil.notice);
        }
        return;
      }
      //聊天
      if (model.jumpUrl!.contains('session')) {
        if (param != null && param.containsKey('modelId')) {
          Map? params = await AmorsApis.createSession(modelId: int.parse(param['modelId']));
          if (params != null && params['sessionNo'] != null) {
            Map<String, dynamic>? userLoginInfo =
                Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
            if (userLoginInfo?['register'] == true) {
              await Get.toNamed(Routes.register);
            }
            Get.toNamed(Routes.session, arguments: params['sessionNo']);
          }
        }
        return;
      }
      //网页
      if (model.jumpUrl!.contains('webview')) {
        if (param != null && param.containsKey('url')) {
          Get.toNamed(Routes.webview, parameters: {'url': Uri.decodeFull(param['url'])});
        }
        return;
      }
      //任务
      if (model.jumpUrl!.contains('task')) {
        Get.toNamed(Routes.task);
        return;
      }
      //应用商店
      if (model.jumpUrl!.contains('launchStore')) {
        CommonUtil.launchStore();
        return;
      }
      //tabs
      if (model.jumpUrl!.contains('tabs')) {
        if (param != null && param.containsKey('page')) {
          Get.until((route) => route.settings.name == Routes.tabs);
          TabsController.to.page = int.parse(param['page']);
        }
        return;
      }
      //邮件
      if (model.jumpUrl!.contains('launchEmail')) {
        final Uri params = Uri(
          scheme: 'mailto',
          path: Uri.decodeFull(param?['path'] ?? ''),
          query:
              "subject=${Uri.decodeFull(param?['subject'] ?? '')}&body=${Uri.decodeFull(param?['body'] ?? '')}",
        );
        await launchUrl(params);
        return;
      }
      //应用商店
      if (model.jumpUrl!.contains('invite')) {
        Map? shareParam = await CommonApis.commonShare();
        if (shareParam != null) {
          Share.share(shareParam['text']);
        }
        return;
      }
    }
  }

  cliamRequest(int index, NotficationModel model, bool ad) async {
    Map? result = await ProfileApis.notificationClaim(msgId: model.msgId!);
    if (result != null) {
      GemsAnimation.show();
      Future.delayed(const Duration(milliseconds: 1800), () {
        Get.back();
      });
      model.claimStatus = 1;
      notificationList.replaceRange(index, index + 1, [model]);
      update();
    }
  }
/*
  //检查通知权限
  checkPermission() async {
    Future.delayed(const Duration(seconds: 2), () async {
      if (Get.isRegistered<NotificationPageController>() == true) {
        noticePermission.value =
            await PermissionHelper.getPermissionStatus(Permission.notification, showToast: false);
      }
    });
  }

  //跳转手机设置
  launchAppSet() {
    openAppSettings();
  }
  */

  Map? schemeFormat(String scheme) {
    //是否带参数
    if (scheme.contains('?')) {
      String data = scheme.split('?').last;
      //是否带多个参数
      if (data.contains('&')) {
        Map params = {};
        List paramsList = data.split('&');
        for (var element in paramsList) {
          List paramList = element.split('=');
          params[paramList[0]] = paramList[1];
        }
        return params;
      } else {
        List paramList = data.split('=');
        return {paramList[0]: paramList[1]};
      }
    } else {
      return null;
    }
  }

  @override
  void dispose() {
    super.dispose();
    ReportUtil.reportViews(page: ReportUtil.quests, action: ReportUtil.quit);
  }
}
