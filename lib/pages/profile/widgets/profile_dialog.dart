import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileDialog {
  static logoutDialog() async {
    bool? confirm = await Get.dialog(
      CustomDialogWidget(
        //判断一下是否设备登录用户，设备登录用户是否是会员
        title:
            UserService.to.isTempUser ? 'System Prompt'.tr : 'Are you sure you want to log out?'.tr,
        subTitle: UserService.to.isTempUser
            ? (UserService.to.isVip
                ? 'Logging out might lose your subscription'.tr
                : 'Logging out your account'.tr)
            : null,
        confirmTitle: UserService.to.isTempUser ? 'Pass'.tr : 'Log Out'.tr,
        confirmColor: UserService.to.isTempUser ? '#00B0F9' : '#F54A48',
        cancelTitle: UserService.to.isTempUser ? 'Sure'.tr : 'Cancel'.tr,
      ),
    );
    if ((confirm == true && !UserService.to.isTempUser) ||
        (UserService.to.isTempUser && confirm == false)) {
      await UserService.to.userLogout();
      Get.back();
    }
  }

  //用户登录、切换等弹窗
  static Future<bool?> userDialog({
    required String title,
    required String content,
    required String confirmTitle,
    required String cancelTitle,
  }) async {
    bool? confirm = await Get.dialog(
      CustomDialogWidget(
        title: title,
        confirmTitle: confirmTitle,
        subTitle: content,
        confirmColor: '#00B0F9',
        cancelTitle: cancelTitle,
      ),
    );
    return confirm;
  }

  //设备登录用户  弹窗提示绑定
  static Future<bool?> accountWarning() async {
    bool? sure = await Get.dialog(
      Center(
        child: Container(
          margin: EdgeInsets.only(bottom: 50.w),
          width: 300.w,
          height: 311.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: AppColor.colorsUtil('#1F1F21'),
            image: const DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage(
                Assets.assetsImagesDialogAlphBg,
              ),
            ),
          ),
          clipBehavior: Clip.antiAlias,
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    30.verticalSpaceFromWidth,
                    Image.asset(
                      Assets.assetsImagesBindDialogIcon,
                      width: 76.w,
                    ),
                    20.verticalSpaceFromWidth,
                    Text(
                      'Account Warning'.tr,
                      style: TextStyle(
                          fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                    10.verticalSpaceFromWidth,
                    Text(
                      UserService.to.isVip
                          ? 'Bind your account to protect your subscription'.tr
                          : 'Bind your account to protect your\naccount'.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 14.sp,
                          height: 1.4,
                          color: AppColor.primaryText.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w400),
                    ),
                    30.verticalSpaceFromWidth,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        dialogButton(
                            title: 'Pass'.tr,
                            colors: ['#3D3D43'],
                            onTap: () {
                              Get.back();
                            },
                            fontWeight: FontWeight.w500),
                        16.horizontalSpace,
                        dialogButton(
                            title: 'Sure'.tr,
                            colors: ['#FFDCA4', '#C8984A'],
                            onTap: () {
                              Get.back(result: true);
                            },
                            fontWeight: FontWeight.w600)
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
    return sure;
  }

  //提示一键登录
  static Future<bool?> oneClickLogin() async {
    bool? sure = await Get.dialog(
      Center(
        child: Container(
          margin: EdgeInsets.only(bottom: 50.w),
          width: 300.w,
          height: 292.w,
          decoration: const BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage(
                Assets.assetsImagesSessionBottomDialogBg,
              ),
            ),
          ),
          clipBehavior: Clip.antiAlias,
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    30.verticalSpaceFromWidth,
                    Image.asset(
                      Assets.assetsImagesDeviceLoginDialogIcon,
                      width: 76.w,
                    ),
                    20.verticalSpaceFromWidth,
                    Text(
                      'One-Click Login'.tr,
                      style: TextStyle(
                          fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                    10.verticalSpaceFromWidth,
                    Text(
                      "Full features available to login users".tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 14.sp,
                          height: 1.4,
                          color: AppColor.primaryText.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w400),
                    ),
                    30.verticalSpaceFromWidth,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        dialogButton(
                            title: 'Pass',
                            colors: ['#3D3D43'],
                            onTap: () {
                              Get.back();
                            },
                            fontWeight: FontWeight.w500),
                        16.horizontalSpace,
                        dialogButton(
                            title: 'Sure'.tr,
                            colors: ['#FFDCA4', '#C8984A'],
                            onTap: () {
                              Get.back(result: true);
                            },
                            fontWeight: FontWeight.w600)
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
    return sure;
  }

  static Widget dialogButton(
      {required String title,
      required List<String> colors,
      required VoidCallback onTap,
      required FontWeight fontWeight}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        width: 122.w,
        height: 42.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(42.w / 2),
            color: AppColor.colorsUtil(colors.first),
            gradient: colors.length > 1
                ? LinearGradient(colors: colors.map((e) => AppColor.colorsUtil(e)).toList())
                : null),
        child: Text(
          title,
          style: TextStyle(fontSize: 16.sp, fontWeight: fontWeight, color: Colors.white),
        ),
      ),
    );
  }

  static Widget gemsTipWidget() {
    return Stack(
      children: [
        Positioned(
          left: 16.w,
          right: 16.w,
          top: 130.w,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 10.0,
                sigmaY: 10.0,
              ),
              child: Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
                decoration: BoxDecoration(
                  color: CommonUtil.colorsUtil('#874600', alpha: 0.4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    gemsTipContent(text: 'profile gems tip 1'.tr),
                    6.verticalSpaceFromWidth,
                    gemsTipContent(text: 'profile gems tip 2'.tr),
                    6.verticalSpaceFromWidth,
                    gemsTipContent(text: 'profile gems tip 3'.tr),
                    6.verticalSpaceFromWidth,
                    gemsTipContent(text: 'profile gems tip 4'.tr),
                  ],
                ),
              ),
            ),
          ),
          //  Image.asset(
          //   Assets.assetsImagesProfileGemsTip,
          //   width: double.infinity,
          //   fit: BoxFit.contain,
          // ),
        ),
      ],
    );
  }

  static Widget gemsTipContent({required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 2.w),
          child: Image.asset(
            Assets.assetsImagesSessionGems,
            width: 12.w,
          ),
        ),
        6.horizontalSpace,
        Expanded(
          child: Text(
            text,
            style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w400),
          ),
        ),
      ],
    );
  }
}
