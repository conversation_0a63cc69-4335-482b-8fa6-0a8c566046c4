import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:url_launcher/url_launcher.dart';

class ProfileMenu extends StatelessWidget {
  const ProfileMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      child: Padding(
        padding: REdgeInsets.only(top: 16, bottom: 16.w + CommonUtil.bottomBarHeight() + 80),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: REdgeInsets.symmetric(horizontal: 16.w),
              // padding: EdgeInsets.symmetric(horizontal: 14.w),
              // decoration: BoxDecoration(
              //     color: AppColor.secondBg, borderRadius: BorderRadius.circular(12.r)),
              child: Column(
                children: [
                  // if (AmorTraService.locale != AmorTraService.fallbackLocale)
                  _menuItem(
                    title: 'Automatic Translation'.tr,
                    image: Assets.assetsImagesProfileAutoTra,
                    switchWidget: true,
                    onTap: () {},
                  ),
                  _menuItem(
                    title: 'Like us, Rate us?'.tr,
                    image: Assets.assetsImagesProfileLikeUs,
                    onTap: () {
                      AppService.appReview();
                    },
                  ),
                  _menuItem(
                    title: 'Email us'.tr,
                    image: Assets.assetsImagesProfileEmailUs,
                    onTap: () async {
                      if (!UserService.to.isLogin) {
                        UserService.to.loginTip();
                        return;
                      }
                      final Uri params = Uri(
                        scheme: 'mailto',
                        path: '<EMAIL>',
                        query:
                            "subject=Issue Inquiry - ${UserService.to.userinfo.value.shareCode}&body=Dear Amor Support Team,\n\nI am [Your Name], Amor user with ID ${UserService.to.userinfo.value.shareCode}. \nI'm encountering an issue\n[briefly describe issue] \n\nPlease see attached screenshots and screen recordings.\n[please record or shot screens, attach them in the email]\n\nYour prompt assistance is appreciated.\n\nBest,\n[Your Name]\n\n\n--\nPlease help us identify the issue and your account just so we can help you more efficiently and faster, by filling the content in [].",
                      );
                      canLaunchUrl(params).then((value) {
                        if (value != true) {
                          Loading.toast('There is no email-related software.');
                        } else {
                          launchUrl(params);
                        }
                      });
                      ReportUtil.reportEvents(page: ReportUtil.profile, action: ReportUtil.email);
                    },
                  ),
                  if (AppService.audit == false)
                    _menuItem(
                      title: 'Preferences'.tr,
                      image: Assets.assetsImagesProfilePreferences,
                      onTap: () {
                        if (!UserService.to.isLogin) {
                          UserService.to.loginTip();
                          return;
                        }
                        Get.toNamed(Routes.preferences);
                      },
                    ),
                  _menuItem(
                    title: 'Settings'.tr,
                    image: Assets.assetsImagesProfileSetting,
                    onTap: () {
                      if (!UserService.to.isLogin) {
                        UserService.to.loginTip();
                        return;
                      }
                      ReportUtil.reportEvents(
                          page: ReportUtil.profile, action: ReportUtil.actionSetting);
                      Get.toNamed(Routes.setting);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _menuItem({
    required String title,
    required String image,
    String? subTitle,
    bool? switchWidget,
    required Function() onTap,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Row(
        children: [
          Image.asset(
            image,
            height: 22.w,
          ),
          12.horizontalSpace,
          Text(
            title,
            style: TextStyle(fontSize: 16.sp, color: AppColor.primaryText.withValues(alpha: 0.7)),
          ),
          Expanded(child: Container(height: 56.w)),
          subTitle != null
              ? Padding(
                  padding: REdgeInsets.only(right: 10),
                  child: Text(
                    subTitle,
                    strutStyle: const StrutStyle(leading: 0.5),
                    style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColor.primaryText.withValues(alpha: 0.5),
                        letterSpacing: 0),
                  ),
                )
              : Container(),
          if (switchWidget == true)
            SizedBox(
              height: 30.w,
              child: Obx(
                () => Switch(
                  value: UserService.to.isAutoTranslation && UserService.to.isVip,
                  activeColor: CommonUtil.colorsUtil('#F0BE72'),
                  onChanged: (value) {
                    ProfilePageController.to.autoTraSwitch();
                  },
                ),
              ),
            )
          else
            RotatedBox(
              quarterTurns: AmorTraService.ar == true ? 2 : 0,
              child: Image.asset(
                Assets.assetsImagesArrowWhite,
                width: 6.w,
              ),
            ),
        ],
      ),
    );
  }
}
