import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileRechargeWidget extends GetView<ProfilePageController> {
  const ProfileRechargeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (!UserService.to.isLogin) {
          UserService.to.loginTip();
          return;
        }
        ReportUtil.reportViews(
            page: ReportUtil.profile, action: ReportUtil.go, value: ReportUtil.hot);
        controller.showRecharge();
      },
      child: Obx(
        () => Container(
          height: 140.w,
          width: double.infinity,
          padding: EdgeInsets.only(
              right: AmorTraService.ar ? 16.w : 0, left: AmorTraService.ar ? 0 : 16.w),
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            image: DecorationImage(
                fit: BoxFit.fill,
                image: AssetImage(AmorTraService.ar
                    ? Assets.assetsImagesProfileSaleBgAr
                    : Assets.assetsImagesProfileSaleBg)),
            borderRadius: BorderRadius.circular(12.w),
          ),
          clipBehavior: Clip.hardEdge,
          child: Stack(
            children: [
              Align(
                alignment: AmorTraService.ar ? Alignment.centerLeft : Alignment.centerRight,
                child: Image.asset(
                  UserService.to.isVip
                      ? Assets.assetsImagesProfileSaleIconVip
                      : AmorTraService.ar
                          ? Assets.assetsImagesProfileSaleIconAr
                          : Assets.assetsImagesProfileSaleIcon,
                ),
              ),
              Positioned.fill(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    16.verticalSpaceFromWidth,
                    EasyRichText(
                      UserService.to.isVip
                          ? 'Get Gems Now!'.trArgs(['Gems'])
                          : 'Enjoy Amor HOT Now!'.trArgs(['Amor HOT']),
                      defaultStyle: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w600,
                        fontStyle: FontStyle.italic,
                        color: CommonUtil.colorsUtil('#874600'),
                      ),
                      patternList: [
                        EasyRichTextPattern(
                          targetString: UserService.to.isVip ? 'Gems' : 'Amor HOT',
                          style: TextStyle(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w900,
                            fontStyle: FontStyle.italic,
                            color: CommonUtil.colorsUtil('#874600'),
                          ),
                          matchWordBoundaries: true,
                        ),
                      ],
                    ),
                    10.verticalSpaceFromWidth,
                    Text(
                      UserService.to.isVip
                          ? 'Deal Expires By:'.trArgs([
                              UserService.to.isLifetimeVip()
                                  ? 'Lifetime'.tr
                                  : (CommonUtil.timeMillisTransition(
                                      timestamp: UserService.to.userinfo.value.endDate ?? 0))
                            ])
                          : 'Best Companionship'.tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w600,
                        color: CommonUtil.colorsUtil('#997544'),
                      ),
                    ),
                    20.verticalSpaceFromWidth,
                    Container(
                      height: 32.w,
                      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 7.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32.w / 2),
                        color: CommonUtil.colorsUtil('#874600'),
                      ),
                      child: Text(
                        UserService.to.isVip ? 'Check'.tr : 'On Sale'.tr,
                        style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, height: 1.2),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
