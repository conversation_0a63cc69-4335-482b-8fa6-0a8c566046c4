import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileHeader extends GetView<ProfilePageController> {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      width: 1.sw,
      height: 160.w + CommonUtil.statusBarHeight(context),
      child: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              Assets.assetsImagesInnerTopBg,
              fit: BoxFit.cover,
            ),
          ),

          //消息
          Positioned(
            top: CommonUtil.statusBarHeight(context) + 10.w,
            right: 17.w,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                if (!UserService.to.isLogin) {
                  UserService.to.loginTip();
                  return;
                }
                ReportUtil.reportViews(
                    page: ReportUtil.profile, action: ReportUtil.go, value: ReportUtil.notif);
                Get.toNamed(Routes.notification);
              },
              child: Obx(
                () => CustomBadge.mineNoticeBadge(
                  needRedWidget: Image.asset(
                    Assets.assetsImagesProfileNotice,
                    width: 24.w,
                    height: 24.w,
                  ),
                  newMsgNum:
                      UserService.to.isLogin ? UserService.to.userinfo.value.unreadCount ?? 0 : 0,
                  end: -5,
                ),
              ),
            ),
          ),

          //任务
          if (AppService.audit == false)
            Positioned(
              top: CommonUtil.statusBarHeight(context) + 10.w,
              left: 16.w,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (!UserService.to.isLogin) {
                    UserService.to.loginTip();
                    return;
                  }
                  Get.toNamed(Routes.task);
                },
                child: Obx(
                  () => CustomBadge.mineNoticeBadge(
                    needRedWidget: Image.asset(
                      Assets.assetsImagesProfileTask,
                      width: 24.w,
                      height: 24.w,
                    ),
                    newMsgNum: UserService.to.isLogin
                        ? ((UserService.to.userinfo.value.actionable ?? 0) +
                            (UserService.to.userinfo.value.available ?? 0))
                        : 0,
                    end: -5,
                  ),
                ),
              ),
            ),
          //用户信息、签到
          Positioned(
            top: CommonUtil.statusBarHeight(context) + 45.w,
            left: 0,
            right: 0,
            child: GestureDetector(
              onTap: () async {
                if (!UserService.to.isLogin) {
                  UserService.to.loginTip();
                  return;
                }
                Map<String, dynamic>? userLoginInfo =
                    Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
                if (userLoginInfo?['register'] == true) {
                  await Get.toNamed(Routes.register);
                } else {
                  ReportUtil.reportViews(
                      page: ReportUtil.profile, action: ReportUtil.go, value: ReportUtil.account);
                  Get.toNamed(Routes.editUserinfo);
                }
              },
              behavior: HitTestBehavior.translucent,
              child: Container(
                padding: EdgeInsets.only(left: 16.w, right: 8.w),
                child: Row(
                  children: [
                    Container(
                      width: 90.w,
                      height: 90.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(90.w / 2),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Obx(
                        () => UserService.to.isLogin
                            ? CachedNetworkImage(
                                imageUrl: UserService.to.userinfo.value.avatarUrl ??
                                    AppService.configModel.avatar ??
                                    '',
                                memCacheWidth: Get.width.toInt(),
                                placeholder: (context, url) {
                                  return Image.asset(
                                    Assets.assetsImagesProfileDefAvatar,
                                    fit: BoxFit.cover,
                                  );
                                },
                                errorWidget: (context, url, error) {
                                  return Image.asset(
                                    Assets.assetsImagesProfileDefAvatar,
                                    fit: BoxFit.cover,
                                  );
                                },
                                fit: BoxFit.cover,
                              )
                            : Image.asset(
                                Assets.assetsImagesProfileDefAvatar,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    16.horizontalSpace,
                    Expanded(
                      child: Obx(
                        () => Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              overflow: TextOverflow.clip,
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: 20.sp,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: fontBeVietnamPro,
                                  color: UserService.to.isVip && UserService.to.isLogin
                                      ? AppColor.colorsUtil('#F0BE72')
                                      : AppColor.primaryText,
                                ),
                                children: [
                                  TextSpan(
                                    text: UserService.to.isLogin
                                        ? (UserService.to.userinfo.value.nickName ?? '').isEmpty
                                            ? 'NO NAME YET'.tr
                                            : UserService.to.userinfo.value.nickName ?? ''
                                        : 'Sign in/up'.tr,
                                  ),
                                ],
                              ),
                            ),
                            if (UserService.to.isLogin)
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  controller.showGemsDialog();
                                },
                                child: Container(
                                  height: 26.w,
                                  margin: REdgeInsets.only(top: 14),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(26.w / 2),
                                    color: AppColor.colorsUtil('#3D3531'),
                                    border: Border.all(
                                      color: AppColor.colorsUtil('#322A2A'),
                                    ),
                                  ),
                                  padding: REdgeInsets.symmetric(horizontal: 8),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Image.asset(
                                        Assets.assetsImagesSessionGems,
                                        width: 20.w,
                                      ),
                                      5.horizontalSpace,
                                      //宝石数量
                                      (UserService.to.userinfo.value.gems ?? 0) < 1000
                                          ? userDiamondWidget(
                                              diamonNum: UserService.to.userinfo.value.gems ?? 0)
                                          : Text(
                                              CommonUtil.numberUnits(
                                                  UserService.to.userinfo.value.gems ?? 0),
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: AppColor.primaryText,
                                                height: 15 / 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    10.horizontalSpace,
                    //签到
                    Padding(
                      padding: REdgeInsets.only(top: 0),
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          if (!UserService.to.isLogin) {
                            UserService.to.loginTip();
                            return;
                          }
                          if (SignService.signState.value == 0) {
                            SignService.showSignDialog();
                          }
                        },
                        child: Obx(
                          () => Container(
                            width: 80.w,
                            height: 76.w,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage(
                                  SignService.signState.value == 0
                                      ? Assets.assetsImagesSignGet
                                      : Assets.assetsImagesSignGot,
                                ),
                              ),
                            ),
                            alignment: Alignment.bottomCenter,
                            padding: EdgeInsets.only(bottom: 20.w),
                            child: Stack(
                              children: [
                                //文字 阴影  使用重叠实现
                                Text(
                                  '+${(SignService.signinModel?.signGems) ?? 500}',
                                  style: TextStyle(
                                    fontSize: 11.sp,
                                    fontWeight: FontWeight.w900,
                                    letterSpacing: 3,
                                    foreground: Paint()
                                      ..style = PaintingStyle.stroke
                                      ..strokeWidth = 3
                                      ..color = SignService.signState.value == 0
                                          ? AppColor.colorsUtil('#FF6B00')
                                          : AppColor.colorsUtil('#C1C1C1'),
                                  ),
                                ),
                                Text(
                                  '+${(SignService.signinModel?.signGems) ?? 500}',
                                  style: TextStyle(
                                      fontSize: 11.sp,
                                      letterSpacing: 3,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w900),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget userDiamondWidget({required int diamonNum}) {
    return AnimatedFlipCounter(
      value: diamonNum,
      duration: const Duration(milliseconds: 500),
      textStyle: const TextStyle(
        fontSize: 12,
        fontFamily: fontBeVietnamPro,
        color: AppColor.primaryText,
        height: 15 / 12,
        fontWeight: FontWeight.w500,
      ),
      // padding: const EdgeInsets.only(top: 0.5),
    );
  }
}
