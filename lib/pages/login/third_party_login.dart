import 'package:amor_app/common/widgets/widgets.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class ThirdPartyLogin {
  // apple登录
  static Future<String?> appleLogIn() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ]);
      return credential.identityToken;
    } catch (e) {
      // LogUtil.e(e);
    }
    return null;
  }

  // facebook登录
  static Future<User?> facebookLogIn() async {
    try {
      final LoginResult loginResult = await FacebookAuth.instance.login();
      if (loginResult.status != LoginStatus.success) {
        return null;
      }
      final OAuthCredential facebookAuthCredential =
          FacebookAuthProvider.credential(loginResult.accessToken?.tokenString ?? '');
      final UserCredential authResult =
          await FirebaseAuth.instance.signInWithCredential(facebookAuthCredential);
      final user = authResult.user;
      return user;
    } on FirebaseAuthException {
      // Loading.dismiss();
      // if (e.code == 'user-not-found') {
      //   LogUtil.e('No user found for that email.');
      // } else if (e.code == 'wrong-password') {
      //   LogUtil.e('Wrong password provided for that user.');
      // } else {
      // LogUtil.e("_signInWithFacebook e = ${e.toString()}");
      // }
      // print(e.toString());
      return null;
    } finally {}
  }

  // Google登录
  static Future<User?> googleLogin() async {
    GoogleSignIn plugin = GoogleSignIn(
      scopes: <String>['email'],
    );
    try {
      var app = await Firebase.initializeApp();
      var auth = FirebaseAuth.instanceFor(app: app);
      await plugin.signOut();
      final account = await plugin.signIn();
      if (account != null) {
        final googleSignInAuthentication = await account.authentication;
        final credential = GoogleAuthProvider.credential(
          accessToken: googleSignInAuthentication.accessToken,
          idToken: googleSignInAuthentication.idToken,
        );
        final UserCredential authResult = await auth.signInWithCredential(credential);
        final user = authResult.user;
        return user;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      Loading.dismiss();
      if (e.code == 'user-not-found') {
        // print('No user found for that email.');
      } else if (e.code == 'wrong-password') {
        // debugp('Wrong password provided for that user.');
      } else {
        // print("_signInWithGoogle e = ${e.toString()}");
      }
      return null;
    } finally {
      Loading.dismiss();
      // print("_signInByGoogle end ----");
    }
  }
}
