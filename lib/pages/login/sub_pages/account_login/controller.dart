import 'dart:convert';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../../common/routes/pages.dart';
import 'widgets/selec_country.dart';

class AccountLoginPageController extends GetxController with GetSingleTickerProviderStateMixin {
  static AccountLoginPageController get to => Get.find();
  late TabController tabController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  late TextEditingController searchTextCtl;

  late TextEditingController codeController;

  var phoneSendCodeEnable = false.obs;
  var phoneSendCodeError = false.obs;
  var emailSendCodeEnable = false.obs;
  var countryCode = "+1".obs;
  var countryName = "US".obs;
  List countryList = [];
  List az = [];
  //排序后的国家列表
  List countrySortList = [];
  var searchPageList = [].obs;
  String phoneErroeMsg = '';
  String? bindType;
  @override
  void onInit() {
    super.onInit();
    bindType = Get.arguments;
    emailController = TextEditingController();
    phoneController = TextEditingController();
    codeController = TextEditingController();
    searchTextCtl = TextEditingController();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(
      () async {
        Get.focusScope?.unfocus();
        if (tabController.animation!.value == tabController.index) {}
      },
    );
    searchTextCtl.addListener(() {
      if (searchTextCtl.text.isEmpty) {
        searchPageList.value = countrySortList;
      }
    });
    phoneController.addListener(() {
      phoneSendCodeEnable.value = phoneController.text.length > 3;
      phoneSendCodeError.value = false;
    });
    if (bindType != 'Email') {
      //登录或绑定手机时才获取手机号码
      getPhoneNumber();
    }
  }

  //获取countryCode
  //先通过IP获取地区如果失败使用设备地区
  Future<String> getCountryPhoneCode() async {
    /*
    Loading.show();
    var response = await Dio().get(
      'http://ip-api.com/json',
      options: Options(
        //3秒超时
        sendTimeout: const Duration(milliseconds: 3000),
        receiveTimeout: const Duration(milliseconds: 3000),
      ),
    );
    Map<String, dynamic>? jsonResponse = response.data;
    if (jsonResponse != null && jsonResponse.isNotEmpty) {
      final isoCode = jsonResponse['countryCode'];
      return isoCode;
    } else {
      String? isoCode = Get.locale?.countryCode;
      if (isoCode != null) {
        return isoCode;
      }
    }
    */
    //使用设备的地区
    String? isoCode = Get.deviceLocale?.countryCode;
    if (isoCode != null) {
      return isoCode;
    }
    return 'US';
  }

  //自动获取设备手机号
  getPhoneNumber() async {
    /*
    countryName.value = await getCountryPhoneCode();
    countryList = await loadCountryJson();
    Map<String, dynamic> curData =
        countryList.where((element) => element['abbr2'] == countryName.value).toList().first;
    countryCode.value = curData['code'];
    // Loading.dismiss();
    if (GetPlatform.isAndroid) {
      /*
      //判断是否支持google服务
      GooglePlayServicesAvailability availability =
          await GoogleApiAvailability.instance.checkGooglePlayServicesAvailability();
      if (availability.value == 0) {
        String phoneNumber = await SmsAutoFill().hint ?? '';
        if (phoneNumber.contains(countryCode.value)) {
          phoneController.text = phoneNumber.replaceAll(countryCode.value, '');
        }
      }
      */
      String phoneNumber = await SmsAutoFill().hint ?? '';
      if (phoneNumber.contains(countryCode.value)) {
        phoneController.text = phoneNumber.replaceAll(countryCode.value, '');
      }
    }
    countrySortList = countryListSort(countryList);
    */
  }

  //获取本地数据
  Future<List> loadCountryJson() async {
    String jsonString = await rootBundle.loadString('assets/country/country_code.json');
    List data = json.decode(jsonString);
    return data;
  }

  //选择国家
  selectCountry() async {
    searchPageList.value = countrySortList;
    await Get.bottomSheet(
      const LoginSelectCountry(),
      ignoreSafeArea: false,
      isScrollControlled: true,
    );
  }

  //搜索国家
  searchCountry() {
    List searchList = [];
    for (var element in countryList) {
      if (CommonUtil.containsIgnoreCase(element['en'], searchTextCtl.text.trim())) {
        searchList.add(element);
      }
    }
    searchPageList.value = countryListSort(searchList);
  }

  //发送邮件验证码
  sendEmailCode() {
    if (emailSendCodeEnable.value) {
      handleSendCodeEvent();
    }
  }

  // 检查邮箱格式
  void checkEmail() {
    final email = emailController.text.toString().trim();
    emailSendCodeEnable.value = GetUtils.isEmail(email);
  }

  ///发送邮件
  Future<void> handleSendCodeEvent({bool? isResend}) async {
    String? sign = await LoginApi.fetchLoginCodeByEmail(
      email: emailController.text.toString().trim(),
      captchaUse: bindType == null ? '1' : '2',
    );
    if (sign != null && isResend != true) {
      Get.toNamed(Routes.loginInputCode, parameters: {
        'sign': sign,
        'bindType': bindType ?? '',
        'loginType': 'email',
        'account': emailController.text.toString().trim(),
        'countDown': '60',
      });
    }
  }

  //国家列表 按首字母排序
  List countryListSort(List dataList) {
    Map<String, List> dataMap = {};
    for (var i = 0; i < dataList.length; i++) {
      //首字母
      String firstLetter = (dataList.elementAt(i)['en'] ?? 'a').substring(0, 1);
      firstLetter = firstLetter.toUpperCase();

      dataMap.putIfAbsent(firstLetter, () => []).add(dataList[i]);
    }
    az = (dataMap.keys.toList());
    //排序
    az.sort((a, b) {
      List<int> al = a.codeUnits;
      List<int> bl = b.codeUnits;
      for (int i = 0; i < al.length; i++) {
        if (bl.length <= i) return 1;
        if (al[i] > bl[i]) {
          return 1;
        } else if (al[i] < bl[i]) {
          return -1;
        }
      }
      return 0;
    });
    List resultList = [];
    for (var i = 0; i < az.length; i++) {
      resultList.add(CountryBean(initial: az[i], countryName: dataMap[az[i]]));
    }
    return resultList;
  }

  //发送手机短信验证码
  sendPhoneCode() async {
    // Get.toNamed(Routes.loginInputCode, parameters: {
    //   'loginType': 'phone',
    //   'bindType': bindType ?? '',
    //   'countryCode': countryCode.value,
    //   'account': phoneController.text,
    //   'countDown': 12.toString()
    // });
    // return;
    if (phoneSendCodeEnable.value == false) {
      return;
    }
    Map<String, dynamic>? result = await LoginApi.sendSmsCode(
        phone: '${countryCode.value}${phoneController.text}',
        captchaUse: bindType == null ? '1' : '2',
        timestamp: CommonUtil.currentTimeMillis());
    if (result != null) {
      if (result['errorCode'] == 0) {
        int countDown = 60;
        if (result.containsKey('countDown')) {
          countDown = result['countDown'];
        }
        Get.toNamed(Routes.loginInputCode, parameters: {
          'loginType': 'phone',
          'bindType': bindType ?? '',
          'countryCode': countryCode.value,
          'account': phoneController.text,
          'countDown': countDown.toString()
        });
        phoneSendCodeError.value = false;
      } else {
        phoneErroeMsg = result['errorMsg'];
        phoneSendCodeError.value = true;
      }
    } else {
      phoneSendCodeError.value = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    phoneController.dispose();
    codeController.dispose();
    tabController.dispose();
    searchTextCtl.dispose();
  }
}

class CountryBean {
  String? initial; // 字母导航
  List? countryName; // 内容列表

  CountryBean({
    this.initial,
    this.countryName,
  });
}
