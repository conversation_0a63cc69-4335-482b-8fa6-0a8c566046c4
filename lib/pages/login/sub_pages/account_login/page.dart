import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';
import 'widgets/email_login_widget.dart';

class AccountLoginPage extends GetView<AccountLoginPageController> {
  const AccountLoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.bindType == null ? 'Sign in/up'.tr : 'Link'.tr),
        centerTitle: true,
        leading: const CustomBackButton(),
      ),
      resizeToAvoidBottomInset: false,
      body: const Column(
        children: [
          /*
          if (controller.bindType == null)
            TabBar(
              controller: controller.tabController,
              indicatorSize: TabBarIndicatorSize.tab,
              // indicator: CustomTabIndicator(
              //   width: 110.w,
              //   borderSide: BorderSide(
              //     width: 2.w,
              //     color: AppColor.colorsUtil("#3266FF"),
              //   ),
              // ),
              indicatorColor: Colors.amber,
              labelColor: AppColor.colorsUtil('#FFDCA4'),
              // overlayColor: const MaterialStatePropertyAll(Colors.transparent),
              unselectedLabelColor: AppColor.primaryText,
              labelStyle: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
              labelPadding: EdgeInsets.symmetric(horizontal: 12.w),
              tabs: const [Tab(text: "Email"), Tab(text: "Phone")],
            ),
          if (controller.bindType == null)
            Divider(
              height: 1,
              color: AppColor.colorsUtil('#3B3C3E'),
            ),
          
          Expanded(
            child: controller.bindType == null
                ? TabBarView(
                    controller: controller.tabController,
                    children: const [
                      EmailLoginWidget(),
                      PhoneLoginWidget(),
                    ],
                  )
                : controller.bindType == 'Phone'
                    ? const PhoneLoginWidget()
                    : const EmailLoginWidget(),
             
          ),
             */
          EmailLoginWidget()
        ],
      ),
    );
  }
}
