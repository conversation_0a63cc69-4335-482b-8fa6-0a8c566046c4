import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PhoneLoginWidget extends GetView<AccountLoginPageController> {
  const PhoneLoginWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        60.verticalSpaceFromWidth,
        Container(
          width: 1.sw - 20.w * 2,
          height: 50.w,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.white.withValues(alpha: 0.1)),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  controller.selectCountry();
                },
                child: Row(
                  children: [
                    Obx(
                      () => Text(
                        '${controller.countryName.value} ${controller.countryCode.value}',
                        maxLines: 1,
                        style: TextStyle(fontSize: 16.sp, color: Colors.white),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 6.w),
                      child: Image.asset(
                        Assets.assetsImagesLoginCountryArrow,
                        width: 10.w,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 1.5,
                height: 12.w,
                color: AppColor.colorsUtil('#3B3C3E'),
              ),
              Expanded(
                child: _phoneInputWidget(),
              ),
            ],
          ),
        ),
        Obx(
          () => controller.phoneSendCodeError.value == false
              ? Container()
              : Padding(
                  padding: REdgeInsets.only(top: 15, left: 37, right: 37),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      controller.phoneErroeMsg,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColor.colorsUtil('#F54A48'),
                      ),
                    ),
                  ),
                ),
        ),
        30.verticalSpace,
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            controller.sendPhoneCode();
          },
          child: Obx(
            () => AnimatedOpacity(
              opacity: controller.phoneSendCodeEnable.value == true ? 1 : 0.5,
              duration: const Duration(milliseconds: 200),
              child: Container(
                alignment: Alignment.center,
                width: 1.sw - 70.w * 2,
                height: 45.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(45.w / 2),
                  gradient: LinearGradient(
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                  ),
                ),
                child: Text(
                  'Send Code',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                    color: controller.phoneSendCodeEnable.value == true
                        ? Colors.white
                        : AppColor.colorsUtil('#9C835C'),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _phoneInputWidget() => TextField(
        controller: controller.phoneController,
        style: TextStyle(color: AppColor.primaryText, fontSize: 16.sp, height: 1.3),
        // cursorColor: AppColor.colorsUtil('666768'),
        keyboardType: TextInputType.phone,
        decoration: InputDecoration(
          // isCollapsed: true,
          hintText: 'Phone number',
          hintStyle: TextStyle(
              fontSize: 16.sp, color: AppColor.primaryText.withValues(alpha: 0.2), height: 1.3),
          border: InputBorder.none,
          contentPadding: REdgeInsets.symmetric(horizontal: 8),
        ),
      );
}
