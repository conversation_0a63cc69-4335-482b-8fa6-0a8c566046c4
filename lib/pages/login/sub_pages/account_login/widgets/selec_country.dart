import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginSelectCountry extends StatelessWidget {
  const LoginSelectCountry({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select country/region'),
        leading: Container(),
        centerTitle: true,
        actions: [
          Padding(
            padding: REdgeInsets.symmetric(horizontal: 5),
            child: TextButton(
              onPressed: () => Get.back(),
              child: Text(
                "Cancel",
                style: TextStyle(
                  fontSize: 16.sp,
                  letterSpacing: GetPlatform.isIOS ? -0.0 : 0,
                  color: AppColor.colorsUtil('#F0BE72'),
                ),
              ),
            ),
          ),
        ],
      ),
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          10.verticalSpace,
          Padding(
            padding: REdgeInsets.symmetric(
              horizontal: 12,
            ),
            child: _searchInputWidget(),
          ),
          15.verticalSpace,
          Expanded(
            child: Obx(() => ListView.builder(
                  itemCount: AccountLoginPageController.to.searchPageList.length,
                  itemBuilder: (context, index) {
                    return _groupList(
                        data: AccountLoginPageController.to.searchPageList.elementAt(index));
                  },
                )),
          ),
        ],
      ),
    );
  }

  Widget _searchInputWidget() {
    return Container(
      height: 40.w,
      decoration: BoxDecoration(
        color: AppColor.colorsUtil('#303133'),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            alignment: Alignment.center,
            child: Image.asset(
              Assets.assetsImagesSearchWhite,
              width: 16.w,
            ),
          ),
          Expanded(
            child: CustomTextField.searchTextField(
              ctl: AccountLoginPageController.to.searchTextCtl,
              hintText: 'Search countrues and regions',
              autofocus: false,
              onSubmit: (value) {
                Get.focusScope?.unfocus();
                AccountLoginPageController.to.searchCountry();
              },
            ),
          ),
        ],
      ),
    );
  }

  //每个字母分组列表
  Widget _groupList({required CountryBean data}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 1.sw,
          height: 32.w,
          color: AppColor.secondBg,
          padding: REdgeInsets.symmetric(horizontal: 16),
          alignment: Alignment.centerLeft,
          child: Text(
            data.initial ?? '',
            style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.colorsUtil('#A3A3A3'),
                fontWeight: FontWeight.w500),
          ),
        ),
        ListView.builder(
          itemCount: data.countryName?.length,
          itemExtent: 50.w,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AccountLoginPageController.to.countryCode.value =
                    data.countryName?.elementAt(index)['code'];
                AccountLoginPageController.to.countryName.value =
                    data.countryName?.elementAt(index)['abbr2'];
                Get.back();
              },
              child: Padding(
                padding: REdgeInsets.symmetric(horizontal: 16),
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            data.countryName?.elementAt(index)['en'],
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColor.primaryText,
                            ),
                          ),
                          Text(
                            data.countryName?.elementAt(index)['code'],
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColor.primaryText.withValues(alpha: 0.5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Divider(
                        height: 1,
                        color: AppColor.colorsUtil('#3B3C3E'),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        )
      ],
    );
  }
}
