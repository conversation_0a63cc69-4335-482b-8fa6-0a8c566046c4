import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/controller.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class EmailLoginWidget extends GetView<AccountLoginPageController> {
  const EmailLoginWidget({super.key});
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          60.verticalSpaceFromWidth,
          Container(
            width: 1.sw - 20.w * 2,
            height: 50.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white.withValues(alpha: 0.1)),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: _emailInputWidget(),
          ),
          30.verticalSpace,
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.sendEmailCode();
              },
              child: Obx(
                () => AnimatedOpacity(
                  opacity: controller.emailSendCodeEnable.value == true ? 1 : 0.5,
                  duration: const Duration(milliseconds: 200),
                  child: Container(
                    alignment: Alignment.center,
                    width: 1.sw - 70.w * 2,
                    height: 45.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(45.w / 2),
                      gradient: LinearGradient(
                        colors: [
                          AppColor.colorsUtil('#FFDCA4'),
                          AppColor.colorsUtil('#C8984A'),
                        ],
                      ),
                    ),
                    child: Text(
                      'Send Code'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: controller.emailSendCodeEnable.value == true
                            ? Colors.white
                            : AppColor.colorsUtil('#9C835C'),
                      ),
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _emailInputWidget() => TextField(
        controller: controller.emailController,
        // autofocus: true,
        onChanged: (val) => controller.checkEmail(),
        style: TextStyle(
          letterSpacing: GetPlatform.isIOS ? -0.5 : 0,
          color: AppColor.primaryText,
          fontSize: 16.sp,
          height: 1.2,
        ),
        // cursorColor: AppColor.colorsUtil('#7449F5'),
        keyboardType: TextInputType.emailAddress,
        inputFormatters: [
          // 去除汉字
          FilteringTextInputFormatter.deny(RegExp(r'[\u4e00-\u9fa5]'))
        ],
        decoration: InputDecoration(
          hintText: 'Email Address'.tr,
          hintStyle: TextStyle(
              letterSpacing: GetPlatform.isIOS ? -1 : 0,
              fontSize: 16.sp,
              color: AppColor.primaryText.withValues(alpha: 0.2)),
          border: InputBorder.none,
          contentPadding: REdgeInsets.symmetric(horizontal: 8),
        ),
      );
}
