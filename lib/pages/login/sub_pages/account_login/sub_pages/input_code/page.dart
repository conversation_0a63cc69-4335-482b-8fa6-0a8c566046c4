import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

import 'controler.dart';

class LoginInputCodePage extends GetView<LoginInputCodeController> {
  const LoginInputCodePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: const CustomBackButton(),
      ),
      body: Padding(
        padding: REdgeInsets.symmetric(horizontal: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            20.verticalSpace,
            Text(
              'Enter 6-digit code'.tr,
              style: TextStyle(
                  fontSize: 20.sp, color: AppColor.primaryText, fontWeight: FontWeight.w500),
            ),
            10.verticalSpace,
            Text(
              controller.loginType == 'email'
                  ? 'email code send tip'.trArgs([controller.account])
                  : 'A text message with a 6-digit verification code was just sent to ${controller.countryCode} ${controller.account}',
              style: TextStyle(
                fontSize: 14.sp,
                height: 1.4,
                color: AppColor.primaryText.withValues(alpha: 0.5),
              ),
            ),
            30.verticalSpace,
            inputBox(),
            20.verticalSpace,
            Obx(
              () => controller.loginError.value == false
                  ? Container()
                  : Padding(
                      padding: REdgeInsets.only(bottom: 15),
                      child: Text(
                        'Incorrect code'.tr,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColor.colorsUtil('#F54A48'),
                        ),
                      ),
                    ),
            ),
            GestureDetector(
              onTap: () {
                controller.resendCode();
              },
              child: Obx(
                () => Text(
                  controller.timeNum > 0
                      ? 'Resend code countdown'.trArgs(['${controller.timeNum}'])
                      : 'Resend code'.tr,
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: controller.timeNum > 0
                          ? AppColor.primaryText.withValues(alpha: 0.5)
                          : AppColor.colorsUtil('#3266FF'),
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget inputBox() {
    final defaultPinTheme = PinTheme(
      width: 45.w,
      height: 50.w,
      textStyle: TextStyle(fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w500),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 2.0, color: Colors.white),
        ),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border(
        bottom: BorderSide(width: 1.0, color: AppColor.colorsUtil('#666768')),
      ),
    );

    // final submittedPinTheme = defaultPinTheme.copyWith(
    //   decoration: defaultPinTheme.decoration?.copyWith(),
    // );
    return Pinput(
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: focusedPinTheme,
      // submittedPinTheme: submittedPinTheme,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      length: 6,
      pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
      showCursor: true,
      autofocus: true,
      onCompleted: (pin) {
        controller.inputCode = pin;
        controller.loginRequest();
      },
    );
  }
}
