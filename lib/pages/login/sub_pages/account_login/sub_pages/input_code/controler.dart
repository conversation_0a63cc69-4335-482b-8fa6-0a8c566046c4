import 'dart:async';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/edit_userinfo/controller.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:get/get.dart';

class LoginInputCodeController extends GetxController {
  String sign = '';
  String loginType = '';
  String account = '';
  String countryCode = '';
  String inputCode = '';
  int countDown = 60;
  //是否是绑定
  String bindType = '';

  //计时器
  Timer? resendTimer;
  var timeNum = 60.obs;
  var loginError = false.obs;

  @override
  void onInit() {
    super.onInit();
    sign = Get.parameters['sign'] ?? '';
    loginType = Get.parameters['loginType'] ?? '';
    account = Get.parameters['account'] ?? '';
    countryCode = Get.parameters['countryCode'] ?? '';
    countDown = int.parse(Get.parameters['countDown'] ?? '60');
    bindType = Get.parameters['bindType'] ?? '';
    startTimer();
    //安卓 开启手机短信自动填充(短信内容无法加入签名，所以弃用)
    /*
    if (loginType == 'phone' && GetPlatform.isAndroid) {
      SmsAutoFill().listenForCode();
      showSign();
    }
    */
  }
/*
  //展示签名
  showSign() async {
    String signature = await SmsAutoFill().getAppSignature;
    Get.snackbar('signature', signature);
    Clipboard.setData(ClipboardData(text: signature));
  }
  */

  //开始倒计时
  startTimer() {
    timeNum.value = countDown;
    resendTimer ??
        Timer.periodic(const Duration(seconds: 1), (timer) {
          if (timeNum > 0) {
            timeNum--;
          } else {
            timer.cancel();
            resendTimer = null;
          }
        });
  }

  //重新发送
  resendCode() async {
    if (timeNum.value != 0) {
      return;
    }
    //重发邮件
    if (loginType == 'email') {
      String? result = await LoginApi.fetchLoginCodeByEmail(
        email: account,
        captchaUse: bindType.isEmpty ? '1' : '2',
      );
      if (result != null) {
        sign = result;
        startTimer();
      }
    } else {
      Map<String, dynamic>? result = await LoginApi.sendSmsCode(
        phone: '$countryCode$account',
        timestamp: CommonUtil.currentTimeMillis(),
        captchaUse: bindType.isEmpty ? '1' : '2',
      );
      if (result != null) {
        if (result['errorCode'] == 0) {
          startTimer();
        }
      }
    }
  }

  //登录
  loginRequest() async {
    if (inputCode.isEmpty || account.isEmpty) {
      Loading.toast('error');
      return;
    }
    //登录
    if (bindType.isEmpty) {
      //验证账号是否注册
      Map<String, dynamic>? authInfo = await LoginApi.authAccountExist(
          thirdToken: loginType == 'email' ? account : '$countryCode$account',
          thirdType: loginType.toUpperCase());
      if (authInfo == null) {
        return;
      }
      //当前账号与准备登录的账号是同一账号
      if (authInfo['same'] == true) {
        Loading.toast("You're already logged in");
        return;
      }
      //准备登录的账号未注册
      if (authInfo['status'] == 0) {
        bool? result = await ProfileDialog.userDialog(
          title: "Account doesn't exist",
          content: 'Sign up your account?',
          confirmTitle: 'Sign up',
          cancelTitle: 'Pass',
        );
        if (result == false) {
          Get.until((route) => route.settings.name == Routes.tabs);
          return;
        }
      }
      //准备登录的账号已注册且与已登录账号非同一账号
      if (authInfo['status'] == 1 && authInfo['same'] == false) {}
      loginError.value = false;
      Map<String, dynamic>? loginInfo;
      if (loginType == 'email') {
        loginInfo = await LoginApi.loginByEmail(
            authCode: inputCode,
            email: account,
            sign: sign,
            loginAction: UserService.to.isLogin == true ? 'switchAccount' : 'login');
      } else {
        loginInfo = await LoginApi.smsCodeLogin(
            phone: '$countryCode$account',
            code: inputCode,
            loginAction: UserService.to.isLogin == true ? 'switchAccount' : 'login');
      }
      if (loginInfo != null && loginInfo.containsKey('token')) {
        await UserService.to.saveLoginInfo(loginInfo);
        loginSuccess(loginInfo: loginInfo);
      } else {
        loginError.value = true;
      }
    } else {
      //绑定
      loginError.value = false;
      bool result = await ProfileApis.bindPhoneOrEmail(
          code: inputCode,
          account: bindType == 'Phone' ? '$countryCode$account' : account,
          sign: sign,
          bindType: bindType.toUpperCase());

      if (result == true) {
        Get.until((route) {
          if (Get.isRegistered<EditUserinfoPageController>()) {
            EditUserinfoPageController.to.getUserInfo();
          }
          //绑定成功后设置登录的用户类型
          Map<String, dynamic> userLoginInfo =
              Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>;
          userLoginInfo['loginType'] = '';
          Get.find<SPService>().set(spLoginInfo, userLoginInfo);
          UserService.to.userLoginType = '';
          return (route.settings.name ?? '').contains(Routes.editUserinfo) ||
              route.settings.name == Routes.tabs;
        });
      } else {
        loginError.value = true;
      }
    }
  }

  loginSuccess({required Map<String, dynamic> loginInfo}) async {
    UserService.to.getUserInfo();
    Get.until((route) => route.settings.name == Routes.tabs);
  }

  @override
  onClose() {
    super.onClose();
    /*
    if (loginType == 'phone' && GetPlatform.isAndroid) {
      SmsAutoFill().unregisterListener();
    }
    */
  }
}
