import 'dart:async';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import '../../common/routes/pages.dart';
import 'third_party_login.dart';

class LoginPageController extends GetxController {
  TextEditingController emailController = TextEditingController();
  TextEditingController codeController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
  }

  goLogin(String type) async {
    switch (type) {
      case "account":
        Get.toNamed(Routes.accountLogin,
            preventDuplicates:
                false); //跳转到搜索国家页面时使用了bottomSheet，https://codeantenna.com/a/TLxz9p2NVu
        break;
      case "google":
        googleLogIn();
        break;
      case "apple":
        appleLogIn();
        break;
      case "facebook":
        facebookLogIn();
        break;
    }
  }

//第三方登录
  authLogin({required String thirdToken, required String thirdType}) async {
    //验证账号是否注册
    Map<String, dynamic>? authInfo =
        await LoginApi.authAccountExist(thirdToken: thirdToken, thirdType: thirdType);
    if (authInfo == null) {
      return;
    }
    //当前账号与准备登录的账号是同一账号
    if (authInfo['same'] == true) {
      Loading.toast("You're already logged in");
      return;
    }
    //准备登录的账号未注册
    if (authInfo['status'] == 0) {
      bool? result = await ProfileDialog.userDialog(
        title: "Account doesn't exist".tr,
        content: 'Sign up your account?'.tr,
        confirmTitle: 'Sign up'.tr,
        cancelTitle: 'Pass'.tr,
      );
      if (result == false) {
        Get.until((route) => route.settings.name == Routes.tabs);
        return;
      }
    }
    //准备登录的账号已注册且与已登录账号非同一账号
    if (authInfo['status'] == 1 && authInfo['same'] == false) {}
    Map<String, dynamic>? loginInfo = await LoginApi.oauthLogin(
        thirdType: thirdType,
        thirdToken: thirdToken,
        loginAction: UserService.to.isLogin == true ? 'switchAccount' : 'login');
    if (loginInfo != null && loginInfo.containsKey('token')) {
      await UserService.to.saveLoginInfo(loginInfo);
      loginSuccess(loginInfo: loginInfo);
    }
  }

  loginSuccess({required Map<String, dynamic> loginInfo}) async {
    Loading.dismiss();
    Get.back();
    await UserService.to.getUserInfo();
  }

  Future appleLogIn() async {
    String? identityToken = await ThirdPartyLogin.appleLogIn();
    if (identityToken != null) {
      authLogin(
        thirdType: "APPLE",
        thirdToken: identityToken,
      );
    } else {
      Loading.toast('Network error! Please sign in with another channel or try again later.🧎');
    }
  }

  Future facebookLogIn() async {
    Loading.show();
    User? user = await ThirdPartyLogin.facebookLogIn();
    if (user != null) {
      String? idToken = await user.getIdToken(true);
      Loading.dismiss();
      if (idToken == null) {
        Loading.toast('login error');
        return;
      }
      authLogin(
        thirdType: "META",
        thirdToken: idToken,
      );
    } else {
      Loading.toast('Network error! Please sign in with another channel or try again later.🧎');
    }
  }

  Future googleLogIn() async {
    Loading.show();
    User? user = await ThirdPartyLogin.googleLogin();
    if (user != null) {
      String? idToken = await user.getIdToken(true);
      Loading.dismiss();
      authLogin(
        thirdType: "GOOGLE",
        thirdToken: idToken ?? "",
      );
    } else {
      Loading.toast('Network error! Please sign in with another channel or try again later.🧎');
    }
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    codeController.dispose();
  }
}
