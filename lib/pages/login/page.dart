import 'dart:io';

import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/login/controller.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Login {
  static showLogin() async {
    if (Get.isRegistered<LoginPageController>() == true) {
      Get.delete<LoginPageController>(force: true);
    }
    Get.lazyPut(() => LoginPageController());
    await Get.bottomSheet(
      const LoginPage(),
      isScrollControlled: true,
    );
    Get.delete<LoginPageController>(force: true);
  }
}

class LoginPage extends GetView<LoginPageController> {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    //获取服务器返回的登录方式
    String loginMethods = AppService.configModel.loginMethods ?? '';
    return Container(
      decoration: const BoxDecoration(
        color: AppColor.mainBg,
        image: DecorationImage(
          fit: BoxFit.cover,
          image: AssetImage(Assets.assetsImagesLoginBg),
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: CommonUtil.statusBarHeight(context) + 4.h),
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: REdgeInsets.all(16),
              child: InkWell(
                onTap: () => Get.back(),
                child: Image.asset(
                  Assets.assetsImagesLoginCloseWhite,
                  width: 20.w,
                ),
              ),
            ),
          ),
          50.verticalSpace,
          Text(
            'Amor AI',
            style: TextStyle(
                fontSize: 68.sp, height: 83 / 68, fontWeight: FontWeight.w700, letterSpacing: -1),
          ),
          15.verticalSpace,
          Text(
            "the fun that gets you".tr,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 20.sp, fontWeight: FontWeight.w500, fontStyle: FontStyle.italic),
          ),
          // 143.verticalSpace,
          const Spacer(),
          if (Platform.isIOS && (loginMethods.contains('apple') || loginMethods.isEmpty))
            _buildLoginItem(
              icon: Assets.assetsImagesLoginTypeApple,
              title: "Continue with Apple",
              onTap: () => controller.goLogin("apple"),
              color: '#ffffff',
            ),
          if (loginMethods.contains('google') || loginMethods.isEmpty)
            _buildLoginItem(
              icon: Assets.assetsImagesLoginTypeGoogle,
              title: "Continue with Google",
              onTap: () => controller.goLogin("google"),
              color: '#393939',
            ),
          if (loginMethods.contains('meta') || loginMethods.isEmpty)
            _buildLoginItem(
              icon: Assets.assetsImagesLoginTypeMeta,
              title: "Continue with Facebook",
              onTap: () => controller.goLogin("facebook"),
              color: '#3777E5',
            ),
          _buildLoginItem(
            icon: Assets.assetsImagesLoginTypeAccount,
            title: "Use email".tr,
            onTap: () => controller.goLogin("account"),
            color: '#393939',
          ),
          // const Spacer(),
          100.verticalSpace,
          Padding(
            padding: REdgeInsets.only(bottom: 20 + CommonUtil.bottomBarHeight()),
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(
                    letterSpacing: GetPlatform.isIOS ? -0.0 : 0,
                    fontSize: 13.sp,
                    height: 1.4,
                    color: Colors.white.withValues(alpha: 0.5)),
                children: [
                  TextSpan(text: "login tip".tr),
                  TextSpan(
                    text: "  ${'Terms of Service'.tr}  ",
                    style: TextStyle(
                        color: AppColor.colorsUtil('#F0BE72'), fontWeight: FontWeight.w500),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(Routes.webview, parameters: {
                          'url': AppService.configModel.userAgreement ?? (defaultUserAgreement)
                        });
                      },
                  ),
                  const TextSpan(text: "and"),
                  TextSpan(
                    text: "  ${'Privacy Policy'.tr}  ",
                    style: TextStyle(
                        color: AppColor.colorsUtil('#F0BE72'), fontWeight: FontWeight.w500),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(Routes.webview, parameters: {
                          'url': AppService.configModel.privacyPolicy ?? defaultPrivacyPolicy
                        });
                      },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildLoginItem(
      {required String icon,
      required String title,
      required GestureTapCallback onTap,
      required String color}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.w / 2),
          color: AppColor.colorsUtil(color),
        ),
        margin: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.w),
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Row(
          children: [
            Image.asset(
              icon,
              width: 30.w,
            ),
            Expanded(
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 15.sp,
                    color: title.contains('Apple') ? AppColor.colorsUtil('#333333') : Colors.white,
                    fontWeight: FontWeight.w500),
              ),
            ),
            30.horizontalSpace,
          ],
        ),
      ),
    );
  }
}
