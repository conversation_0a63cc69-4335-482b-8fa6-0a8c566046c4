import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/widgets/search.dart';
import 'package:get/get.dart';

class AmorsSearchController extends GetxController {
  static AmorsSearchController get to => Get.find();

  //搜索框动画
  var searchAnimated = false.obs;
  String inputStr = '';
  List searchList = [].obs;

  @override
  void onInit() {
    super.onInit();
  }

  //搜索
  search() async {
    if (inputStr.isEmpty) {
      searchList.clear();
      return;
    }
    List<AmorsFeedModel> list = await AmorsApis.amorsSearch(inputStr, 'main');
    if (inputStr.isEmpty) {
      searchList.clear();
      return;
    }
    searchList.assignAll(list);
  }

  //点击搜索的角色
  ontapSearchRole(int index) async {
    AmorsFeedModel model = searchList.elementAt(index);
    if (model.modelId == null) {
      return;
    }
    String? inviteCode;
    if (model.purview == 0 && model.owner != true) {
      ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.code);
      String? result = await Get.dialog(
        const SearchItemEnterCodeWidget(),
        transitionDuration: const Duration(milliseconds: 200),
      );
      if (result != null && result.isNotEmpty) {
        inviteCode = result;
      } else {
        return;
      }
    }
    startChat(modelId: model.modelId!, inviteCode: inviteCode);
  }

  //开始聊天
  startChat({required int modelId, String? inviteCode}) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    //新用户没有填写基础信息
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: modelId, inviteCode: inviteCode);
    if (params != null && params['sessionNo'] != null) {
      Get.offNamed(Routes.session, arguments: params['sessionNo']);
    }
  }
}
