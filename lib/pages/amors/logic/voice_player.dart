// import 'dart:async';
// import 'dart:typed_data';
// import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
// import 'package:amor_app/common/routes/routes.dart';
// import 'package:amor_app/common/utils/db_util/audio_entity.dart';
// import 'package:amor_app/common/utils/utils.dart';
// import 'package:amor_app/pages/amors/controller.dart';
// import 'package:amor_app/pages/tabs/controller.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:just_audio/just_audio.dart';

// class AmorListVoicePlayer {
//   late AudioPlayer player;
//   init() {
//     player = AudioPlayer();
//     player.playerStateStream.listen((state) {
//       if (state.processingState == ProcessingState.completed) {
//         player.stop();
//       }
//     });
//     player.playingStream.listen((event) {
//       if (event == false) {
//         if (Get.isRegistered<AmorsPageController>()) {
//           AmorsPageController.to.state.playingUrl.value = '';
//         }
//       }
//     });
//   }

//   dispose() {
//     player.stop();
//     player.dispose();
//   }

//   Future play({
//     required String url,
//     bool cacheVoice = false,
//   }) async {
//     //手动点击停止播放
//     if (AmorsPageController.to.state.playingUrl.value == url) {
//       await stopPlayVoice();
//       return;
//     }
//     AudioEntity audioEntity = AudioEntity.instan();
//     List<Map<String, dynamic>> cacheData = await audioEntity.find(where: {'url': url});
//     //已缓存了音频
//     if (cacheData.isNotEmpty && cacheData.first['audio'] != null) {
//       //缓存音频 不播放
//       if (cacheVoice == false) {
//         Uint8List fromDataBuffer = cacheData.first['audio'];
//         player.setAudioSource(BytesSource(fromDataBuffer), preload: true);
//         player.play();
//         AmorsPageController.to.state.playingUrl.value = url;
//       }
//       return;
//     }
//     AmorsPageController.to.state.loadingVoiceList.add(url);
//     //下载和缓存音频
//     ApiRequest.downloadVoice(url).then((value) {
//       if (value != null) {
//         //判断URL是否是当前展示模型的 && 页面停留在首页
//         if (cacheVoice == false &&
//             (AmorsPageController.to.state.dataList.elementAt(AmorsPageController.to.state.curIndex)
//                         as AmorsFeedModel)
//                     .welcomeVoice ==
//                 url &&
//             Get.currentRoute == Routes.tabs &&
//             TabsController.to.page == 0) {
//           player.setAudioSource(BytesSource(value), preload: true);
//           player.play();
//           AmorsPageController.to.state.playingUrl.value = url;
//         }
//         AmorsPageController.to.state.loadingVoiceList.remove(url);
//         return;
//       }
//     });
//   }

//   //停止播放
//   Future stopPlayVoice() async {
//     if (player.playing) {
//       debugPrint('停止播放');
//       await player.stop();
//     }
//   }
// }

// class BytesSource extends StreamAudioSource {
//   final Uint8List _buffer;
//   BytesSource(this._buffer) : super(tag: 'BytesSource');
//   @override
//   Future<StreamAudioResponse> request([int? start, int? end]) async {
//     // Returning the stream audio response with the parameters
//     return StreamAudioResponse(
//       sourceLength: _buffer.length,
//       contentLength: (end ?? _buffer.length) - (start ?? 0),
//       offset: start ?? 0,
//       stream: Stream.fromIterable([_buffer.sublist(start ?? 0, end)]),
//       contentType: 'audio/mpeg',
//     );
//   }
// }
