import 'dart:async';
import 'dart:math';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:get/get.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:video_player/video_player.dart';

class CreatImgVideoPageController extends GetxController {
  String modelId = '';
  String mainImg = '';
  //0图片 1视频
  int creatType = 0;
  //选择的效果
  var selectEffect = 10000.obs;
  //可用效果
  List effectList = [];
  //0 未开始 1 正在生成 2 生成完成
  int creatStatus = 0;
  //生成中 计时器
  Timer? creatTimer;
  //开始生成的时间戳
  // int startTime = 0;
  //生成结果
  AmorsFeedModel? creatResult;
  //视频是否在加载
  var videoLoading = false.obs;
  //视频播放控制器
  VideoPlayerController? videoPlayController;
  //标签url
  Map<String, List<String>> tagUrlMap = {};
  //价格
  int price = 10;
  @override
  void onInit() {
    super.onInit();
    modelId = Get.parameters['modelId'] ?? '';
    mainImg = Get.parameters['img'] ?? '';
    creatType = int.tryParse(Get.parameters['type'] ?? '') ?? 0;
    try {
      price =
          RemoteConfigUtil.getConfig<int>(key: creatType == 0 ? createImgGems : createVideoGems);
    } catch (e) {
      price = creatType == 0 ? 8 : 10;
    }
    getModelProfile();
  }

  //获取配置
  getModelProfile() async {
    Loading.show();
    //请求详情接口
    SessionApis.getModelProfile(
      modelId: modelId.toString(),
      cacheCallBack: (cache) {
        if (cache != null) {
          Loading.dismiss();
          creatResult = cache;
          setEffectList();
        }
      },
    ).then((value) {
      if (value != null && creatResult == null) {
        creatResult = value;
        setEffectList();
        Loading.dismiss();
      }
      if (value == null) {
        Loading.toast('Sorry, something went wrong');
      }
    });
  }

  //设置效果标签
  setEffectList() {
    mainImg = creatResult!.cover ?? Get.parameters['img'] ?? '';
    // 创建一个 Map 来存储每个 tag 对应的 url 列表

    if (creatType == 0 && (creatResult!.genImg ?? []).isNotEmpty) {
      for (var element in creatResult!.genImg!) {
        final tag = element['tag'] ?? '';
        final url = element['url'] ?? '';
        //有同名的标签
        if (!tagUrlMap.containsKey(tag)) {
          tagUrlMap[tag] = [url];
        } else {
          tagUrlMap[tag]?.add(url);
        }
      }
      effectList = tagUrlMap.keys.toList();
      // for (var entry in tagUrlMap.entries) {
      //   print('Tag: ${entry.key}, URLs: ${entry.value}');
      // }
      // 展示标签按钮
      update();
      return;
    }
    if (creatType == 1 && (creatResult!.genVideo ?? []).isNotEmpty) {
      for (var element in creatResult!.genVideo!) {
        final tag = element['tag'] ?? '';
        final url = element['url'] ?? '';
        //有同名的标签
        if (!tagUrlMap.containsKey(tag)) {
          tagUrlMap[tag] = [url];
        } else {
          tagUrlMap[tag]?.add(url);
        }
      }
      effectList = tagUrlMap.keys.toList();
      // 展示标签按钮
      update();
      return;
    }
    //失败
    Loading.toast('Sorry, something went wrong');
  }

  //开始创建
  startCreat() async {
    if (selectEffect.value == 10000) {
      Loading.toast('Please select an effect');
      return;
    }
    if (UserService.to.isVip == false) {
      PurchaseSheet.show(
          page: ReportUtil.chatwin,
          source: creatType == 0 ? Analytics.devsucvipcreimg : Analytics.devsucvipcrevideo);
      return;
    }
    //获取最新的余额信息
    Loading.show();
    await UserService.to.getUserInfo();
    //判断余额
    if ((UserService.to.userinfo.value.gems ?? 0) < price) {
      Loading.toast('Insufficient Balance');
      Future.delayed(const Duration(milliseconds: 2000), () {
        PurchaseSheet.show(
            page: ReportUtil.chatwin,
            source: creatType == 0 ? Analytics.devsucvipcreimg : Analytics.devsucvipcrevideo,
            buyType: 1);
      });
      return;
    }
    //扣除宝石
    bool success = await SessionApis.voiceCallGems(gems: price);
    if (success != true) {
      Loading.toast('Insufficient Balance');
      Future.delayed(const Duration(milliseconds: 2000), () {
        PurchaseSheet.show(
            page: ReportUtil.chatwin,
            source: creatType == 0 ? Analytics.devsucvipcreimg : Analytics.devsucvipcrevideo,
            buyType: 1);
      });
      return;
    }
    Loading.dismiss();
    creatStatus = 1;
    update();
    // 创建图片 根据用户选择的标签去缓存对应的图片
    if (creatType == 0) {
      //延时
      await Future.delayed(Duration(seconds: 2));
      List<String> urlList = tagUrlMap[effectList[selectEffect.value]] ?? [];
      if (urlList.isNotEmpty) {
        mainImg = urlList[Random().nextInt(urlList.length)];
      }
      DefaultCacheManager().getSingleFile(mainImg).then((value) async {
        creatStatus = 2;
        update();
      });
      return;
    }
    // 创建视频 根据用户选择的标签去加载对应视频
    if (creatType == 1) {
      List<String> urlList = tagUrlMap[effectList[selectEffect.value]] ?? [];
      if (urlList.isNotEmpty) {
        setupVideoPlayer(videoUrl: urlList[Random().nextInt(urlList.length)]);
      }
      return;
    }
  }

  //设置视频播放器
  setupVideoPlayer({required String videoUrl}) async {
    videoPlayController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
    videoPlayController?.addListener(() {
      if (videoPlayController?.value.isCompleted == false) {
        videoLoading.value = videoPlayController!.value.isBuffering;
      }
      if (videoPlayController?.value.isCompleted == true) {
        videoLoading.value = false;
        update();
      }
    });
    await videoPlayController?.initialize();
    videoPlayController?.play();
    creatStatus = 2;
    update();
  }

  // 点击暂停、播放
  onTapVideo() async {
    if (videoPlayController!.value.isPlaying) {
      await videoPlayController?.pause();
    } else {
      await videoPlayController?.play();
    }
    update();
  }

  closeTimer() {
    creatTimer?.cancel();
    creatTimer = null;
  }

  @override
  void onClose() {
    super.onClose();
    if (videoPlayController != null) {
      videoPlayController!.dispose();
    }
    closeTimer();
  }
}
