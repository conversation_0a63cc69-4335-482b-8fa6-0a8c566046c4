import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:lottie/lottie.dart';
import 'package:video_player/video_player.dart';

import 'controller.dart';

class CreatImgVideoPage extends StatelessWidget {
  const CreatImgVideoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<CreatImgVideoPageController>(
        builder: (controller) => Stack(
          children: [
            if (controller.creatStatus != 2 || controller.creatType == 0)
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: controller.mainImg,
                  placeholder: (context, url) => CachedNetworkImage(imageUrl: chatDefaultBg),
                  errorWidget: (context, url, error) => CachedNetworkImage(imageUrl: chatDefaultBg),
                  fit: BoxFit.cover,
                ),
              ), //阴影
            Align(
              alignment: Alignment.topCenter,
              child: Container(
                width: 1.sw,
                height: 170.h + CommonUtil.statusBarHeight(context),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#150E0E'),
                      CommonUtil.colorsUtil('#707070').withValues(alpha: 0),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
            //阴影
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: 1.sw,
                height: 330.h + CommonUtil.bottomBarHeight(),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#707070').withValues(alpha: 0),
                      CommonUtil.colorsUtil('#150E0E'),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
            //标签
            if (controller.creatStatus == 0 && controller.effectList.isNotEmpty)
              Positioned(
                left: 16.w,
                right: 16.w,
                bottom: CommonUtil.bottomBarHeight() + 38.h,
                child: Column(
                  children: [
                    Align(
                      alignment: AmorTraService.ar ? Alignment.centerRight : Alignment.centerLeft,
                      child: Text(
                        'Select an effect:'.tr,
                        style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                      ),
                    ),
                    16.verticalSpaceFromWidth,
                    GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisSpacing: 12.w,
                          mainAxisSpacing: 12.w,
                          crossAxisCount: 2,
                          mainAxisExtent: 38.w),
                      itemCount: controller.effectList.length,
                      shrinkWrap: true,
                      padding: EdgeInsets.all(0),
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () => controller.selectEffect.value = index,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                              child: Obx(
                                () => Container(
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      color: controller.selectEffect.value == index
                                          ? CommonUtil.colorsUtil('#EAC282').withValues(alpha: 0.1)
                                          : CommonUtil.colorsUtil('#1F1F1F')
                                              .withValues(alpha: 0.38),
                                      border: controller.selectEffect.value == index
                                          ? GradientBoxBorder(
                                              gradient: LinearGradient(
                                                colors: [
                                                  CommonUtil.colorsUtil('#EAC282'),
                                                  CommonUtil.colorsUtil('#C69351')
                                                ],
                                              ),
                                            )
                                          : null),
                                  alignment: Alignment.center,
                                  child: Text(
                                    controller.effectList.elementAt(index),
                                    style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    24.verticalSpaceFromWidth,
                    InkWell(
                      onTap: () {
                        controller.startCreat();
                      },
                      child: Container(
                        width: 234.w,
                        height: 45.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(45.w / 2),
                          color: CommonUtil.colorsUtil('#4E4E4E'),
                          gradient: LinearGradient(
                            colors: [
                              CommonUtil.colorsUtil('#FFDCA4'),
                              CommonUtil.colorsUtil('#C8984A'),
                            ],
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'CONTINUE'.tr,
                          style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            //正在生成 蒙层
            if (controller.creatStatus == 1)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.45),
                  ),
                ),
              ),
            //正在生成 动画
            if (controller.creatStatus == 1)
              Positioned.fill(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Lottie.asset(
                        'assets/lottie/undress_loading.json',
                        animate: true,
                        repeat: true,
                      ),
                      Text(
                        controller.creatType == 0
                            ? 'AI Generating the image...'
                            : 'AI Generating the video...',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            //视频生成完成 开始播放
            if (controller.creatStatus == 2 && controller.creatType == 1)
              // 点击视频 暂停、继续播放功能
              InkWell(
                onTap: () {
                  controller.onTapVideo();
                },
                child: Center(
                  child: AspectRatio(
                    aspectRatio: controller.videoPlayController!.value.aspectRatio,
                    child: VideoPlayer(controller.videoPlayController!),
                  ),
                ),
              ),
            //播放按钮
            if (controller.creatStatus == 2 &&
                controller.creatType == 1 &&
                controller.videoPlayController!.value.isPlaying == false)
              // 点击视频 暂停、继续播放功能
              InkWell(
                onTap: () {
                  controller.onTapVideo();
                },
                child: Center(
                  child: Image.asset(
                    Assets.assetsImagesSessionVideoPlay,
                    width: 36.w,
                  ),
                ),
              ),
            //视频加载
            Align(
              alignment: Alignment.center,
              child: Obx(
                () => controller.videoLoading.value == true
                    ? Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          backgroundColor: Colors.transparent,
                        ),
                      )
                    : Container(),
              ),
            ),
            //关闭
            Positioned(
              left: 12.w,
              top: CommonUtil.statusBarHeight(context),
              child: CustomBackButton(
                img: Assets.assetsImagesNavCloseBlackCircle,
                width: 28.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
