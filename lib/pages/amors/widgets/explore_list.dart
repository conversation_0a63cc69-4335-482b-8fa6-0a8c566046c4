import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/amors/widgets/discover_item.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

class ExploreListWidget extends GetView<AmorsPageController> {
  const ExploreListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return EasyRefresh.builder(
      controller: controller.discoverRefreshCtl,
      onRefresh: () => controller.refreshdataList(),
      onLoad: () => controller.loadMoredataList(),
      childBuilder: (context, physics) => Obx(
        () {
          return MasonryGridView.count(
            padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 20.w, top: 10.w),
            physics: physics,
            crossAxisCount: 2,
            mainAxisSpacing: 12.w,
            crossAxisSpacing: 12.w,
            itemCount: controller.state.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              AmorsFeedModel model = controller.state.dataList.elementAt(index);
              int idLast = int.tryParse(('${model.modelId}'
                      .substring('${model.modelId}'.length - 1, '${model.modelId}'.length))) ??
                  0;
              controller.scrollObserver(index: index);
              return SizedBox(
                height: 240.w + (idLast.isOdd ? 40.w : 0),
                child: DiscoverItemWidget(
                  index: index,
                  model: model,
                  isOdd: idLast.isOdd,
                  isVideo: false,
                  like: () => controller.likeOperation(modelId: model.modelId ?? 0, index: index),
                  onTap: () => controller.toChat(model),
                ),
              );
            },
          );
        },
      ),

      //  GridViewObserver(
      //   // autoTriggerObserveTypes: const [ObserverAutoTriggerObserveType.scrollEnd],
      //   onObserve: (resultModel) {
      //     controller.scrollObserver(index: resultModel.displayingChildIndexList.last);
      //   },
      //   child:   ),
    );
  }
}
