import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/assets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:lottie/lottie.dart';

class DiscoverItemWidget extends StatelessWidget {
  final int index;
  final AmorsFeedModel model;
  final bool isOdd;
  final bool isVideo;
  final VoidCallback onTap;
  final VoidCallback? like;

  const DiscoverItemWidget({
    super.key,
    required this.index,
    required this.model,
    required this.isOdd,
    required this.isVideo,
    required this.onTap,
    this.like,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: model.purview != 2
            ? Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                strokeAlign: BorderSide.strokeAlignOutside)
            : null,
        borderRadius: BorderRadius.circular(16.w),
      ),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Stack(
          children: [
            Positioned.fill(
              child: CachedNetworkImage(
                imageUrl: (isVideo && model.gifUrl != null)
                    ? model.gifUrl!
                    : (model.cover ?? '').replaceAll(
                        '?x-oss-process=style/compress', '?x-oss-process=image/resize,w_1000'),
                fit: BoxFit.cover,
                errorWidget: (context, url, error) {
                  return imageErrorWidget();
                },
                placeholder: (context, url) {
                  return imageErrorWidget();
                },
                // memCacheWidth: Get.width.toInt() * 2,
              ),
            ),
            if (model.purview == 2 && isVideo == false)
              Positioned.fill(
                child: Lottie.asset('assets/lottie/amor_paid/amor_paid.json',
                    animate: true, fit: BoxFit.fill),
              ),
            if (isVideo == false)
              Positioned(
                left: 8.w,
                top: 14.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (model.contentCategory != null)
                      Container(
                        height: 13.w,
                        padding: EdgeInsets.symmetric(horizontal: 5.w),
                        margin: EdgeInsets.only(bottom: 4.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.r),
                            bottomLeft: Radius.circular(4.r),
                            bottomRight: Radius.circular(4.r),
                          ),
                          border: Border.all(color: Colors.white.withValues(alpha: 0.5)),
                          color: AppColor.colorsUtil('#00B0F9'),
                          gradient: model.contentCategory!.toUpperCase() == 'NSFW'
                              ? LinearGradient(colors: [
                                  AppColor.colorsUtil('#FF68B9'),
                                  AppColor.colorsUtil('#FF3D62'),
                                ])
                              : null,
                        ),
                        child: Row(
                          children: [
                            Text(
                              model.contentCategory!.toUpperCase(),
                              style: TextStyle(fontSize: 8.sp),
                            )
                          ],
                        ),
                      ),
                    ...List.generate(
                      (model.tags ?? []).length > 4 ? 5 : (model.tags ?? []).length,
                      (index) => Container(
                        height: 13.w,
                        padding: EdgeInsets.symmetric(horizontal: 5.w),
                        margin: EdgeInsets.only(bottom: 4.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.r),
                            bottomLeft: Radius.circular(4.r),
                            bottomRight: Radius.circular(4.r),
                          ),
                          border: GradientBoxBorder(
                            gradient: LinearGradient(colors: [
                              Colors.white.withValues(alpha: 0.8),
                              Colors.transparent,
                            ]),
                          ),
                          color: AppColor.colorsUtil('#00B0F9'),
                          gradient: LinearGradient(colors: [
                            AppColor.colorsUtil('#3E2D12').withValues(alpha: 0.8),
                            AppColor.colorsUtil('#1A1000').withValues(alpha: 0.6),
                          ]),
                        ),
                        child: Row(
                          children: [
                            Text(
                              index == 4 ? '......' : model.tags![index],
                              style: TextStyle(
                                  fontSize: 8.sp,
                                  color: AppColor.colorsUtil('#FFDEAE'),
                                  height: 9 / 8),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            if (isVideo == false)
              Positioned(
                right: 8.w,
                top: 14.w,
                child: ClipRRect(
                  clipBehavior: Clip.antiAlias,
                  borderRadius: BorderRadius.circular(4.r),
                  child: InkWell(
                    onTap: like,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.w),
                      decoration: BoxDecoration(
                        color: AppColor.colorsUtil('#131213').withValues(alpha: 0.4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            model.hasLike == true
                                ? Assets.assetsImagesAmorsShare
                                : Assets.assetsImagesAmorsLike,
                            width: 11.w,
                          ),
                          4.horizontalSpace,
                          Text(
                            CommonUtil.numberUnits(model.likeCount ?? 0),
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: AppColor.primaryText,
                              height: 1.2,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            if (model.clothe == true && isVideo == false)
              Positioned(
                top: 38.w,
                right: 8.w,
                child: Image.asset(
                  Assets.assetsImagesListClothIcon,
                  width: 16.w,
                ),
              ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                height: isOdd ? 98.w : 68.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.6),
                      Colors.black.withValues(alpha: 0.7)
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 8.w,
              left: 8.w,
              right: 8.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (model.purview == 2 && isVideo == false)
                    Padding(
                      padding: EdgeInsets.only(bottom: 3.w),
                      child: Image.asset(
                        Assets.assetsImagesAmorHotIcon,
                        width: 33.w,
                      ),
                    ),
                  Text(
                    model.modelName ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColor.primaryText,
                      fontWeight: FontWeight.w700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  2.verticalSpaceFromWidth,
                  if (isVideo == true) 6.verticalSpaceFromWidth,
                  if (isVideo == false)
                    Text(
                      model.scenario ?? model.intro ?? '',
                      overflow: TextOverflow.ellipsis,
                      maxLines: isOdd ? 4 : 2,
                      style: TextStyle(
                        fontSize: 11.sp,
                        height: 1.3,
                        fontStyle: FontStyle.italic,
                        color: AppColor.primaryText.withValues(alpha: 0.8),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imageErrorWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColor.colorsUtil('#434343'),
      alignment: Alignment.center,
      child: Image.asset(
        Assets.assetsImagesAmorListPlacehOlder,
        width: 45.w,
        height: 45.w,
      ),
    );
  }
}
