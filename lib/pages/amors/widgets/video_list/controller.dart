import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';

class HomeVideoListWidgetController extends GetxController {
  static HomeVideoListWidgetController get to => Get.find();

  late EasyRefreshController refreshCtl;
  int page = 1;
  List videoList = [];
  @override
  void onInit() {
    super.onInit();
    refreshCtl = EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true);
    getVideoList(refresh: false);
  }

  //获取分类列表
  getVideoList({required bool refresh}) async {
    List<AmorsFeedModel> list = [];
    AmorsApis.amorsSearchMedia(
      videoCall: true,
      cacheCallBack: (cache) {
        if (refresh == false && cache.isNotEmpty) {
          list = cache;
          setVideoList(list: cache);
        }
      },
      page: page,
    ).then((value) {
      //没有缓存数据
      if (list.isEmpty) {
        setVideoList(list: value);
      }
    });
  }

  setVideoList({required List list}) {
    if (page == 1) {
      videoList.assignAll(list);
      refreshCtl.resetFooter();
    } else {
      videoList.addAll(list);
    }
    refreshCtl.finishRefresh();
    if (list.isEmpty) {
      refreshCtl.finishLoad(IndicatorResult.noMore);
    } else {
      refreshCtl.finishLoad(IndicatorResult.success);
    }
    update();
  }

  onRefresh() {
    page = 1;
    getVideoList(refresh: true);
  }

  onLoadMore() {
    page++;
    getVideoList(refresh: true);
  }

  //跳转视频聊天
  void goVideoChat(int index) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    Analytics().logEvent(Analytics.cvideochatchar);
    //创建会话ID
    AmorsFeedModel model = videoList.elementAt(index);
    Map? result = await AmorsApis.createSession(modelId: model.modelId ?? 0);
    if (result != null && result['sessionNo'] != null) {
      //获取会话配置
      Map<String, dynamic>? config = await SessionApis.getSessionConfig(
          sessionNo: result['sessionNo'], cacheCallBack: (Map<String, dynamic>? cache) {});
      if (config != null &&
          await SttUtil().checkAvailable() == true &&
          Get.currentRoute.startsWith(Routes.tabs)) {
        Get.toNamed(Routes.voiceCall, parameters: {
          'voiceType': '0',
          'callType': 'video',
          'modelId': '${model.modelId ?? 0}',
          'sessionNo': result['sessionNo'],
          'avatarUrl': model.cover ?? '',
          'nickname': model.modelName ?? '',
          'bgImgUrl': config['bgImg'] ?? '',
          'defaultVoice': config['voice'] ?? '',
        });
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshCtl.dispose();
  }
}
