import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/amors/widgets/discover_item.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import 'controller.dart';

class HomeVideoListWidget extends StatelessWidget {
  const HomeVideoListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: HomeVideoListWidgetController(),
      builder: (controller) => EasyRefresh.builder(
        controller: controller.refreshCtl,
        onRefresh: () => controller.onRefresh(),
        onLoad: () => controller.onLoadMore(),
        childBuilder: (context, physics) => MasonryGridView.count(
          padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 20.w, top: 10.w),
          physics: physics,
          crossAxisCount: 2,
          mainAxisSpacing: 10.w,
          crossAxisSpacing: 10.w,
          itemCount: controller.videoList.length,
          itemBuilder: (BuildContext context, int index) {
            AmorsFeedModel model = controller.videoList.elementAt(index);
            int idLast = int.tryParse(('${model.modelId}'
                    .substring('${model.modelId}'.length - 1, '${model.modelId}'.length))) ??
                0;
            return SizedBox(
              height: 240.w + (idLast.isOdd ? 20.w : 0),
              child: DiscoverItemWidget(
                index: index,
                model: model,
                isOdd: idLast.isOdd,
                isVideo: true,
                onTap: () => controller.goVideoChat(index),
              ),
            );
          },
        ),
      ),
    );
  }

  // Widget videoListItem({required int index, required AmorsFeedModel model}) {}
}
