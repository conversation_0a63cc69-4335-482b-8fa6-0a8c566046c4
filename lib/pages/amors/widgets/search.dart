import 'dart:ui';

import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/logic/search_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AmorSearchWidget extends GetView<AmorsSearchController> {
  const AmorSearchWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (Get.focusScope?.hasFocus == true) {
          Get.focusScope?.unfocus();
          Future.delayed(const Duration(milliseconds: 300), () => Get.back());
        } else {
          Get.back();
        }
      },
      child: Container(
        color: Colors.black.withValues(alpha: 1),
        padding:
            EdgeInsets.only(top: 12 + CommonUtil.statusBarHeight(context), left: 16.w, right: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: controller.searchAnimated.value ? 1.sw - 32.w : 54.w,
                height: 42.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(42.w / 2),
                  color: Colors.white.withValues(alpha: 0.3),
                ),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Image.asset(
                        Assets.assetsImagesSearchWhite,
                        width: 22.w,
                      ),
                    ),
                    if (controller.searchAnimated.value)
                      Expanded(
                        child: CustomTextField.searchTextField(
                          hintText: 'search hintText'.trArgs(['Amors']),
                          onChanged: (value) {
                            controller.inputStr = value;
                            controller.search();
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
            14.verticalSpace,
            Expanded(
              child: NotificationListener<ScrollStartNotification>(
                onNotification: (notification) {
                  //滚动时收起键盘
                  Get.focusScope?.unfocus();
                  return true;
                },
                child: Obx(
                  () => ListView.separated(
                    padding: EdgeInsets.symmetric(vertical: 16.w),
                    itemBuilder: (context, index) {
                      return searchListItem(
                        onTap: () {
                          controller.ontapSearchRole(index);
                        },
                        model: controller.searchList.elementAt(index),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return 16.verticalSpaceFromWidth;
                    },
                    itemCount: controller.searchList.length,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget searchListItem({required VoidCallback onTap, required AmorsFeedModel model}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 120.w,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Stack(
          children: [
            Positioned.fill(
              child: Padding(
                padding: EdgeInsets.all(10.w),
                child: Row(
                  children: [
                    Container(
                      width: 100.w,
                      height: 100.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: CachedNetworkImage(
                              imageUrl: model.cover ?? '',
                              fit: BoxFit.cover,
                              memCacheWidth: Get.width.toInt(),
                              placeholder: (context, url) => ColoredBox(
                                color: AppColor.colorsUtil('#303133'),
                              ),
                            ),
                          ),
                          if (model.purview == 0 && model.owner != true)
                            Positioned.fill(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10.r),
                                clipBehavior: Clip.antiAlias,
                                child: BackdropFilter(
                                  filter: ImageFilter.blur(
                                    sigmaX: 5.0,
                                    sigmaY: 5.0,
                                  ),
                                  child: Column(
                                    children: [
                                      28.verticalSpaceFromWidth,
                                      Image.asset(
                                        Assets.assetsImagesAmorSearchLock,
                                        width: 30.w,
                                      ),
                                      7.verticalSpaceFromWidth,
                                      Text(
                                        'Private Clone'.tr,
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            color: AppColor.colorsUtil('#FCC772'),
                                            fontWeight: FontWeight.w600,
                                            fontStyle: FontStyle.italic),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                model.modelName ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            if (model.clothe == true)
                              Padding(
                                padding: EdgeInsets.only(
                                  left: AmorTraService.ar ? 0 : 8.w,
                                  right: AmorTraService.ar ? 8.w : 0,
                                ),
                                child: Image.asset(Assets.assetsImagesListClothIcon, width: 16.w),
                              ),
                          ],
                        ),
                        4.verticalSpaceFromWidth,
                        Text(
                          model.intro ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white.withValues(alpha: 0.6),
                              fontWeight: FontWeight.w400),
                        ),
                        5.verticalSpaceFromWidth,
                        Row(
                          children: [
                            Image.asset(
                              Assets.assetsImagesAmorSearchLiked,
                              width: 16.w,
                            ),
                            5.horizontalSpace,
                            Text(
                              'Liked num'.trArgs([CommonUtil.numberUnits(model.likeCount ?? 0)]),
                              style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.white.withValues(alpha: 0.6),
                                  fontWeight: FontWeight.w400),
                            ),
                            const Spacer(),
                            if (model.purview == 0 && model.owner != true)
                              Text(
                                'Enter Code'.tr,
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColor.colorsUtil('#FCC772'),
                                    fontWeight: FontWeight.w600,
                                    fontStyle: FontStyle.italic),
                              ),
                          ],
                        ),
                      ],
                    ))
                  ],
                ),
              ),
            ),
            if (model.owner == true)
              Align(
                alignment: Alignment.topRight,
                child: Container(
                  width: 60.w,
                  height: 23.w,
                  decoration: BoxDecoration(
                    color: AppColor.colorsUtil('#00B0F9'),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(12.r),
                      bottomLeft: Radius.circular(12.r),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Owner'.tr,
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontStyle: FontStyle.italic),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class SearchItemEnterCodeWidget extends StatelessWidget {
  const SearchItemEnterCodeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    String code = '';
    return Center(
      child: Container(
        width: 300.w,
        height: 186.w,
        margin: EdgeInsets.only(bottom: 50.h),
        decoration: BoxDecoration(
          color: AppColor.colorsUtil('#414145'),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          children: [
            30.verticalSpaceFromWidth,
            Text(
              'Enter Code'.tr,
              style: TextStyle(fontSize: 17.sp, color: Colors.white, fontWeight: FontWeight.w700),
            ),
            15.verticalSpaceFromWidth,
            Container(
              width: 200.w,
              height: 38.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(38.w / 2),
                color: Colors.white.withValues(alpha: 0.05),
              ),
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: CustomTextField.textField(
                textAlign: TextAlign.center,
                autofocus: true,
                onChanged: (value) => code = value,
              ),
            ),
            30.verticalSpaceFromWidth,
            Divider(
              height: 0.3,
              color: AppColor.colorsUtil('#545458').withValues(alpha: 0.65),
            ),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back();
                      },
                      child: Text(
                        'Cancel'.tr,
                        style: TextStyle(
                          fontSize: 17.sp,
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 0.3,
                    color: AppColor.colorsUtil('#545458').withValues(alpha: 0.65),
                  ),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back(result: code);
                      },
                      child: Text(
                        'Chat'.tr,
                        style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColor.colorsUtil('#00B0F9'),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
