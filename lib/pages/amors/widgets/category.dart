// import 'dart:ui';

// import 'package:amor_app/common/models/amors_models/amors_category_model.dart';
// import 'package:amor_app/common/utils/utils.dart';
// import 'package:amor_app/common/values/values.dart';
// import 'package:amor_app/common/widgets/widgets.dart';
// import 'package:amor_app/pages/amors/controller.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// class AmorCategoryWidget extends GetView<AmorsPageController> {
//   const AmorCategoryWidget({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(
//           child: InkWell(
//             onTap: () => Get.back(),
//             child: Container(
//               color: Colors.transparent,
//             ),
//           ),
//         ),
//         ClipRect(
//           child: BackdropFilter(
//             filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0, tileMode: TileMode.repeated),
//             child: Container(
//               color: AppColor.colorsUtil('#1D1712').withValues(alpha: 0.7),
//               padding: EdgeInsets.symmetric(horizontal: 14.w),
//               width: 1.sw,
//               child: SafeArea(
//                 child: InkWell(
//                   onTap: () => Get.back(),
//                   child: Obx(
//                     () => Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         SizedBox(
//                           height: CommonUtil.appBarHeight(),
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             TagSwitchWidget(
//                               selected: controller.state.tagSwitchType.isEmpty
//                                   ? []
//                                   : controller.state.tagSwitchType,
//                               selectedCallBack: (index) {
//                                 if (controller.state.tagSwitchType.contains(index)) {
//                                   controller.state.tagSwitchType.remove(index);
//                                 } else {
//                                   controller.state.tagSwitchType.add(index);
//                                 }
//                               },
//                             ),
//                             ImageBtn(
//                                 iconSting: Assets.assetsImagesHomeTagClose,
//                                 onPressed: () => Get.back(),
//                                 width: 10.w,
//                                 height: 13.w),
//                           ],
//                         ),
//                         12.verticalSpaceFromWidth,
//                         Expanded(
//                           child: SingleChildScrollView(
//                             padding: EdgeInsets.only(bottom: 10.w),
//                             child: tagWidget(),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         )
//       ],
//     );
//   }

//   Widget tagWidget() {
//     return Obx(
//       () => Wrap(
//         children: List.generate(controller.state.tagsList.length, (index) {
//           AmorsCategoryModel tagModel = controller.state.tagsList.elementAt(index);
//           bool hidden = false;
//           if ((tagModel.label == 'nsfw' && controller.state.tagSwitchType.contains(1) == false) ||
//               (tagModel.label == 'bdsm' && controller.state.tagSwitchType.contains(2) == false)) {
//             hidden = true;
//           }
//           bool selected = controller.state.selectedCategoryIndex.value == index;
//           return hidden
//               ? const SizedBox(width: 0)
//               : InkWell(
//                   onTap: () {
//                     if (controller.state.selectedCategoryIndex.value != index) {
//                       controller.state.selectedCategoryIndex.value = index;
//                       controller.state.clearList = true;
//                       controller.state.dataList.clear();
//                       controller.selectedCategory(index);
//                       ReportUtil.reportEvents(
//                           page: ReportUtil.amor,
//                           action: ReportUtil.select,
//                           value: tagModel.cateName);
//                     }
//                     Get.back();
//                   },
//                   child: Container(
//                     height: 38.w,
//                     // color: Colors.amber,
//                     margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.w),
//                     constraints: BoxConstraints(minWidth: 50.w),
//                     child: Stack(
//                       children: [
//                         Container(
//                           padding: EdgeInsets.symmetric(horizontal: 10.w),
//                           margin: EdgeInsets.only(top: 6.w),
//                           height: 32.w,
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(36.w / 2),
//                             border: Border.all(
//                                 color: selected
//                                     ? AppColor.colorsUtil('#F0BE72')
//                                     : Colors.white.withValues(alpha: 0.4),
//                                 strokeAlign: BorderSide.strokeAlignOutside),
//                           ),
//                           child: Row(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               Text(
//                                 tagModel.cateName ?? '',
//                                 style: TextStyle(
//                                     fontSize: 16.sp,
//                                     fontWeight: FontWeight.w600,
//                                     color: selected == true
//                                         ? AppColor.colorsUtil('F0BE72')
//                                         : Colors.white.withValues(alpha: 0.4)),
//                               )
//                             ],
//                           ),
//                         ),
//                         if ((tagModel.label == 'nsfw' &&
//                                 controller.state.tagSwitchType.contains(1) == true) ||
//                             (tagModel.label == 'bdsm' &&
//                                 controller.state.tagSwitchType.contains(2) == true))
//                           Positioned(
//                             top: 0,
//                             left: 2.w,
//                             child: Image.asset(
//                               tagModel.label == 'nsfw'
//                                   ? Assets.assetsImagesTagNsfw
//                                   : Assets.assetsImagesTagBdsm,
//                               width: 48.w,
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 );
//         }),
//       ),
//     );
//   }
// }
