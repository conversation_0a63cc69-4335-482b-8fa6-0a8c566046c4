// import 'dart:ui';

// import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
// import 'package:amor_app/common/routes/routes.dart';
// import 'package:amor_app/common/utils/utils.dart';
// import 'package:amor_app/common/values/values.dart';
// import 'package:amor_app/pages/amors/controller.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:easy_rich_text/easy_rich_text.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:lottie/lottie.dart';

// class AmorsSwiperItem extends GetView<AmorsPageController> {
//   final int index;
//   final AmorsFeedModel model;
//   const AmorsSwiperItem({
//     super.key,
//     required this.index,
//     required this.model,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         Positioned.fill(
//           child: CachedNetworkImage(
//             imageUrl: model.bgImg ?? '',
//             placeholder: (context, url) {
//               return Container();
//             },
//             errorWidget: (context, url, error) {
//               return Container();
//             },
//             fit: BoxFit.cover,
//           ),
//         ),

//         //顶部阴影
//         if (model.purview == 2)
//           Align(
//             alignment: Alignment.topCenter,
//             child: Container(
//               width: 1.sw,
//               height: 56 + CommonUtil.statusBarHeight(context),
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   colors: [
//                     AppColor.colorsUtil('#FFC739').withValues(alpha: 0.5),
//                     AppColor.colorsUtil('#FFC739').withValues(alpha: 0.0),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         //底部阴影
//         Align(
//           alignment: Alignment.bottomCenter,
//           child: Obx(
//             () => AnimatedContainer(
//               duration: const Duration(milliseconds: 100),
//               width: 1.sw,
//               height: controller.state.unfoldIntro.value == index ? 390.h : 190.h,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   colors: model.purview == 2
//                       ? [
//                           AppColor.colorsUtil('#FFC739').withValues(alpha: 0.0),
//                           AppColor.colorsUtil('#FFC739').withValues(alpha: 0.5),
//                         ]
//                       : [
//                           Colors.transparent,
//                           Colors.black.withValues(alpha: 0.7),
//                         ],
//                 ),
//               ),
//             ),
//           ),
//         ),

//         Align(
//           alignment: Alignment.bottomCenter,
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               AnimatedSize(
//                 duration: const Duration(milliseconds: 100),
//                 child: Padding(
//                   padding: EdgeInsets.symmetric(horizontal: 16.w),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       //打招呼
//                       messageContent(),
//                       8.verticalSpaceFromWidth,
//                       //名称
//                       GestureDetector(
//                         behavior: HitTestBehavior.translucent,
//                         onTap: () {
//                           //震动
//                           if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true) {
//                             HapticFeedback.mediumImpact();
//                           }
//                           if (controller.state.unfoldIntro.value == index) {
//                             controller.resetUnfoldIntro();
//                             ReportUtil.reportEvents(
//                                 page: ReportUtil.amor, action: ReportUtil.collapse);
//                           } else {
//                             controller.state.unfoldIntro.value = index;
//                             ReportUtil.reportEvents(
//                                 page: ReportUtil.amor, action: ReportUtil.expand);
//                           }
//                         },
//                         child: SingleChildScrollView(
//                           scrollDirection: Axis.horizontal,
//                           child: Row(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               Text(
//                                 model.modelName ?? '',
//                                 style: TextStyle(
//                                   fontSize: 26.sp,
//                                   color: Colors.white,
//                                   fontWeight: FontWeight.w800,
//                                   fontStyle: FontStyle.italic,
//                                   shadows: [
//                                     Shadow(color: Colors.black.withValues(alpha: 0.25), blurRadius: 6)
//                                   ],
//                                 ),
//                               ),
//                               10.horizontalSpace,
//                               Obx(
//                                 () => Image.asset(
//                                   controller.state.unfoldIntro.value == index
//                                       ? Assets.assetsImagesAmorsPackup
//                                       : Assets.assetsImagesAmorsUnfold,
//                                   width: 8.w,
//                                 ),
//                               ),
//                               if (model.purview == 2)
//                                 Padding(
//                                   padding: EdgeInsets.only(left: 10.w),
//                                   child: Image.asset(
//                                     Assets.assetsImagesAmorsHot,
//                                     width: 70.w,
//                                   ),
//                                 ),
//                             ],
//                           ),
//                         ),
//                       ),
//                       4.verticalSpaceFromWidth,
//                       Obx(
//                         () => Text(
//                           model.intro ?? '',
//                           style: TextStyle(
//                             fontSize: 14.sp,
//                             height: 18.sp / 14.sp,
//                             color: Colors.white,
//                             shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)],
//                           ),
//                           maxLines: controller.state.unfoldIntro.value == index ? 12 : 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Obx(
//                         () => controller.state.unfoldIntro.value == index
//                             ? Padding(
//                                 padding: EdgeInsets.only(top: 10.w, bottom: 16.w),
//                                 child: Row(
//                                   children: [
//                                     CircleAvatar(
//                                       radius: 20.w / 2,
//                                       backgroundColor: Colors.transparent,
//                                       backgroundImage: model.authorAvatar != null
//                                           ? CachedNetworkImageProvider(model.authorAvatar!)
//                                           : null,
//                                     ),
//                                     6.horizontalSpace,
//                                     Text(
//                                       model.author ?? '',
//                                       style: TextStyle(
//                                         fontSize: 14.sp,
//                                         color: Colors.white,
//                                         shadows: [
//                                           Shadow(
//                                               color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)
//                                         ],
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               )
//                             : 16.verticalSpaceFromWidth,
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.symmetric(horizontal: 16.w),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     bottomButton(
//                       icon: Assets.assetsImagesAmorsCard,
//                       title: 'Card',
//                       onTap: () {
//                         if (!UserService.to.isLogin) {
//                           UserService.to.loginTip();
//                           return;
//                         }
//                         ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.card);
//                         ReportUtil.reportViews(
//                             page: ReportUtil.amor, action: ReportUtil.go, value: ReportUtil.cards);
//                         Get.toNamed(Routes.sessionSetBg,
//                             parameters: {'modelId': '${model.modelId}'});
//                       },
//                     ),
//                     Obx(
//                       () => bottomButton(
//                         icon: controller.state.likedList.contains(model.modelId) ||
//                                 model.hasLike == true
//                             ? Assets.assetsImagesAmorsShare
//                             : Assets.assetsImagesAmorsLike,
//                         title: controller.state.likedList.contains(model.modelId) ||
//                                 model.hasLike == true
//                             ? 'Share'
//                             : CommonUtil.numberUnits(model.likeCount ?? 0),
//                         onTap: () {
//                           controller.likeOrShare(model);
//                         },
//                       ),
//                     ),
//                     bottomButton(
//                       icon: Assets.assetsImagesAmorsChat,
//                       title: 'Chat',
//                       onTap: () {
//                         controller.toChat(model);
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//               16.verticalSpaceFromWidth,
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   //打招呼消息
//   Widget messageContent() {
//     List specialString = CommonUtil.getTextBetweenStr(
//             text: model.welcomeMsg ?? '', exp: [r'\*([^]*?)\*', r'\((.*?)\)'], include: 1) ??
//         [];
//     String msgContent = model.welcomeMsg ?? '';
//     //将*替换为空格
//     msgContent = msgContent.replaceAll(RegExp(r'\*'), ' ');
//     //如果第一个是空格就删除
//     if (msgContent.startsWith(' ')) {
//       msgContent = msgContent.replaceRange(0, 1, '');
//     }
//     return Stack(
//       children: [
//         Padding(
//           padding: EdgeInsets.only(
//               bottom: model.welcomeVoice == null ? 0 : 25.w / 2,
//               top: model.purview == 2 ? 36.w : 0),
//           child: ClipRRect(
//             clipBehavior: Clip.antiAlias,
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(6.r),
//               topRight: Radius.circular(16.r),
//               bottomRight: Radius.circular(16.r),
//               bottomLeft: Radius.circular(16.r),
//             ),
//             child: BackdropFilter(
//               filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
//               child: Container(
//                 constraints: BoxConstraints(maxWidth: 1.sw - 96.w),
//                 decoration: BoxDecoration(
//                   color: AppColor.colorsUtil('#404048').withValues(alpha: 0.5),
//                 ),
//                 padding: REdgeInsets.fromLTRB(20, 14, 20, model.welcomeVoice == null ? 14 : 20),
//                 child: EasyRichText(
//                   msgContent,
//                   defaultStyle: TextStyle(fontSize: 15.sp, color: Colors.white),
//                   patternList: specialString
//                       .map((e) => EasyRichTextPattern(
//                             targetString: e,
//                             hasSpecialCharacters: true,
//                             style: TextStyle(
//                               color: Colors.white.withValues(alpha: 0.7),
//                               fontStyle: FontStyle.italic,
//                             ),
//                           ))
//                       .toList(),
//                 ),
//               ),
//             ),
//           ),
//         ),
//         if (model.welcomeVoice != null)
//           Positioned(
//             bottom: 0,
//             left: 20.w,
//             child: voicePlayButton(),
//           ),
//         if (model.purview == 2)
//           Positioned(
//             top: 0,
//             left: 0,
//             child: Image.asset(
//               Assets.assetsImagesAmorsPurviewIcon,
//               width: 50.w,
//             ),
//           ),
//       ],
//     );
//   }

//   //播放按钮
//   Widget voicePlayButton() {
//     return GestureDetector(
//       onTap: () {
//         controller.playVoice(model);
//       },
//       behavior: HitTestBehavior.translucent,
//       child: Container(
//         height: 25.w,
//         padding: REdgeInsets.symmetric(horizontal: 12),
//         decoration: BoxDecoration(
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(4.r),
//               topRight: Radius.circular(25.w / 2),
//               bottomRight: Radius.circular(25.w / 2),
//               bottomLeft: Radius.circular(25.w / 2),
//             ),
//             gradient: LinearGradient(
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//               colors: [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69351')],
//             )),
//         child: Obx(
//           () => Row(
//             mainAxisSize: MainAxisSize.min,
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               //生成或下载中
//               controller.state.loadingVoiceList.contains(model.welcomeVoice)
//                   ? SizedBox(
//                       height: 13.w,
//                       width: 13.w,
//                       child: CircularProgressIndicator(
//                         strokeWidth: 2,
//                         valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primaryText),
//                         backgroundColor: AppColor.primaryText.withValues(alpha: 0.2),
//                       ),
//                     )
//                   : (controller.state.playingUrl.value == model.welcomeVoice) &&
//                           (model.welcomeVoice ?? '').isNotEmpty
//                       ? SizedBox(
//                           height: 13.w,
//                           width: 17.w,
//                           child:
//                               Lottie.asset('assets/lottie/session_voice_play.json', animate: true),
//                         )
//                       : Image.asset(
//                           Assets.assetsImagesSessionPagePlay,
//                           width: 12.w,
//                           height: 12.w,
//                         ),

//               Padding(
//                 padding: EdgeInsets.only(left: 8.w),
//                 child: Text(
//                   '${model.duration}″',
//                   style: TextStyle(
//                       fontSize: 12.sp,
//                       color: AppColor.primaryText,
//                       height: 1.2,
//                       fontWeight: FontWeight.w500),
//                 ),
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   //底部操作按钮
//   Widget bottomButton({required String icon, required String title, required VoidCallback onTap}) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         height: 45.w,
//         padding: EdgeInsets.symmetric(horizontal: title == 'Chat' ? 32.w : 16.w),
//         decoration: BoxDecoration(
//           borderRadius: title == 'Chat' ? null : BorderRadius.circular(12.r),
//           color: title == 'Chat' ? null : Colors.black.withValues(alpha: 0.35),
//           image: title == 'Chat'
//               ? const DecorationImage(image: AssetImage(Assets.assetsImagesAmorsChatBg))
//               : null,
//           // boxShadow: [
//           //   BoxShadow(
//           //       blurRadius: 1.0, color: Colors.black.withValues(alpha: 0.2), offset: const Offset(0, 1))
//           // ],
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Image.asset(
//               icon,
//               width: 20.w,
//             ),
//             SizedBox(
//               width: title == 'Chat' ? 6.w : 4.w,
//             ),
//             Text(
//               title,
//               style: TextStyle(
//                   fontSize: title == 'Chat' ? 16.sp : 14.sp,
//                   color: title == 'Card' ? AppColor.colorsUtil('#E9C181') : Colors.white,
//                   fontWeight: title == 'Chat' ? FontWeight.w600 : FontWeight.w400),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
