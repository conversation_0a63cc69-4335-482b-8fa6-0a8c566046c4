import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

class HomeClothListWidgetController extends GetxController {
  static HomeClothListWidgetController get to => Get.find();
  late EasyRefreshController refCtl;
  //列表数据
  var clothList = [].obs;
  int page = 1;
  @override
  void onInit() {
    super.onInit();
    refCtl = EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true);
    getList(refresh: false);
  }

  //获取分类列表
  getList({required bool refresh}) async {
    List<AmorsFeedModel> list = [];
    AmorsApis.amorsSearchMedia(
      clothing: true,
      cacheCallBack: (cache) {
        if (refresh == false && cache.isNotEmpty) {
          list = cache;
          setList(list: cache);
        }
      },
      page: page,
    ).then((value) {
      //没有缓存数据
      if (list.isEmpty) {
        setList(list: value);
      }
    });
  }

  setList({required List list}) {
    if (page == 1) {
      clothList.assignAll(list);
      refCtl.resetFooter();
    } else {
      clothList.addAll(list);
    }
    refCtl.finishRefresh();
    if (list.isEmpty) {
      refCtl.finishLoad(IndicatorResult.noMore);
    } else {
      refCtl.finishLoad(IndicatorResult.success);
    }
  }

  onRefresh() {
    page = 1;
    getList(refresh: true);
  }

  onLoadMore() {
    page++;
    getList(refresh: true);
  }

  likeOperation({int? index, required int modelId}) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    //震动
    if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true && index != null) {
      HapticFeedback.mediumImpact();
    }
    AmorsFeedModel? model;
    //其他页面操作刷新
    if (index == null) {
      model = clothList.firstWhereOrNull((value) => (value as AmorsFeedModel).modelId == modelId);
    } else {
      model = clothList.elementAt(index);
    }
    if (model?.modelId == null) {
      return;
    }
    if (model?.hasLike == true) {
      if (index != null) {
        Map? shareParam = await CommonApis.commonShare(modelId: model?.modelId);
        if (shareParam != null) {
          Share.share(shareParam['text']);
          ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.share);
        }
      } else {
        index = clothList.indexOf(model);
        model?.hasLike = false;
        clothList.replaceRange(index, index + 1, [model]);
      }
    } else {
      if (index != null) {
        model?.hasLike = true;
        clothList.replaceRange(index, index + 1, [model]);
        ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.like);
        bool success = await AmorsApis.doLike(modelId: model?.modelId);
        if (success && Get.isRegistered<InnerPageController>() == true) {
          InnerPageController.to.refreshIndexTabbarList(index: 1);
        }
      } else {
        index = clothList.indexOf(model);
        model?.hasLike = true;
        clothList.replaceRange(index, index + 1, [model]);
      }
    }
  }

  //跳转聊天
  toChat(AmorsFeedModel model) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    if (model.modelId == null) {
      return;
    }
    //新用户没有填写基础信息
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: model.modelId!);
    if (params != null && params['sessionNo'] != null && Get.currentRoute.startsWith(Routes.tabs)) {
      ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.chat);
      ReportUtil.reportViews(
          page: ReportUtil.amor, action: ReportUtil.go, value: ReportUtil.chatwin);
      Analytics().logEvent(Analytics.clickChar,
          visualTag: model.visualTag, charID: model.modelId, screen: Analytics.pageAmors);
      Get.toNamed(Routes.session, arguments: params['sessionNo']);
    }
  }
}
