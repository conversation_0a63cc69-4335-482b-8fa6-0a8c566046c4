import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/amors/widgets/discover_item.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import 'controller.dart';

class HomeClothListWidget extends StatelessWidget {
  const HomeClothListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: HomeClothListWidgetController(),
      builder: (controller) => EasyRefresh.builder(
        controller: controller.refCtl,
        onRefresh: () => controller.onRefresh(),
        onLoad: () => controller.onLoadMore(),
        childBuilder: (context, physics) => Obx(
          () {
            return MasonryGridView.count(
              padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 20.w, top: 10.w),
              physics: physics,
              crossAxisCount: 2,
              mainAxisSpacing: 12.w,
              crossAxisSpacing: 12.w,
              itemCount: controller.clothList.length,
              itemBuilder: (BuildContext context, int index) {
                AmorsFeedModel model = controller.clothList.elementAt(index);
                int idLast = int.tryParse(('${model.modelId}'
                        .substring('${model.modelId}'.length - 1, '${model.modelId}'.length))) ??
                    0;
                return SizedBox(
                  height: 240.w + (idLast.isOdd ? 40.w : 0),
                  child: DiscoverItemWidget(
                    index: index,
                    model: model,
                    isOdd: idLast.isOdd,
                    isVideo: false,
                    onTap: () => controller.toChat(model),
                    like: () => controller.likeOperation(modelId: model.modelId ?? 0, index: index),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
