import 'package:amor_app/common/models/amors_models/amors_chats_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatsListItem extends GetView<AmorsPageController> {
  final AmorsChatsModel model;
  const ChatsListItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.session, arguments: model.sessionNo);
      },
      child: Padding(
        padding: EdgeInsetsDirectional.symmetric(horizontal: 16.w, vertical: 10.w),
        child: Row(
          children: [
            CustomBadge.mineNoticeBadge(
                needRedWidget: ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: model.avatar ?? '',
                    fit: BoxFit.cover,
                    memCacheWidth: Get.width.toInt(),
                    width: 60.w,
                    height: 60.w,
                    placeholder: (context, url) => ColoredBox(
                      color: AppColor.colorsUtil('#303133'),
                    ),
                  ),
                ),
                fontSizeNum: 12,
                newMsgNum: model.unReadCount ?? 0,
                end: 0,
                top: -2
                // top: 0,
                ),
            12.horizontalSpace,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          model.title ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      10.horizontalSpace,
                      Text(
                        CommonUtil.convertTimestamp(
                            model.lastDatetime ?? DateTime.now().millisecondsSinceEpoch),
                        style:
                            TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.3)),
                      ),
                    ],
                  ),
                  12.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                          child: Text(
                        model.lastMsgSnapshot ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style:
                            TextStyle(fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.6)),
                      )),
                      /*
                      model.unReadCount == null || model.unReadCount == 0
                          ? 16.horizontalSpace
                          : Container(
                              height: 16.w,
                              constraints: BoxConstraints(minWidth: 16.w),
                              padding: REdgeInsets.symmetric(horizontal: 5),
                              margin: REdgeInsets.only(left: 4),
                              decoration: BoxDecoration(
                                color: AppColor.colorsUtil('#FF4A3D'),
                                borderRadius: BorderRadius.circular(16.w / 2),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                '${model.unReadCount}',
                                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w500),
                              ),
                            ),
                      if (model.top != null && model.top == true)
                        Padding(
                          padding: REdgeInsets.only(left: 8),
                          child: Image.asset(
                            Assets.assetsImages20ChatListItemStatusPin,
                            width: 14.w,
                          ),
                        ),
                    */
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
