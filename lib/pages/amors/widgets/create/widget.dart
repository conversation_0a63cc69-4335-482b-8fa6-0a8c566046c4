import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/widgets/create/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'undress_widget.dart';

class HomeCreatWidget extends GetView<HomeCreatWidgetController> {
  const HomeCreatWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: HomeCreatWidgetController(),
      builder: (controller) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Column(
          children: [
            8.verticalSpaceFromWidth,
            Row(
              children: [
                pageTitleWidget(
                  title: 'Generate img'.tr,
                  selected: controller.selectPage == 0,
                  onTap: () => controller.changePage(0),
                ),
                8.horizontalSpace,
                pageTitleWidget(
                  title: 'Generate video'.tr,
                  selected: controller.selectPage == 1,
                  onTap: () => controller.changePage(1),
                ),
                /*
                8.horizontalSpace,
                pageTitleWidget(
                  title: 'Undress'.tr,
                  selected: controller.selectPage == 2,
                  onTap: () => controller.changePage(2),
                ),
                */
              ],
            ),
            if (controller.selectPage < 2) ...createWidgetList(),
            if (controller.selectPage == 2)
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(top: 12.w),
                  child: CreatUndressWidget(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  List<Widget> createWidgetList() {
    return [
      18.verticalSpaceFromWidth,
      Align(
        alignment: Alignment.centerLeft,
        child: Text(
          'Choose character:'.tr,
          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400),
        ),
      ),
      10.verticalSpaceFromWidth,
      Expanded(
        child: EasyRefresh.builder(
          controller: controller.refreshCtl,
          onRefresh: () => controller.onRefresh(),
          childBuilder: (context, physics) => GridView.builder(
            physics: physics,
            padding: EdgeInsets.all(0),
            controller: controller.scrollCtl,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 10.w,
                mainAxisSpacing: 8.w,
                mainAxisExtent: (1.sw - 44.w) / 3 + 20.w),
            itemCount: controller.selectPage == 0
                ? controller.creatImgModelList.length
                : controller.creatVideoModelList.length,
            itemBuilder: (BuildContext context, int index) {
              return modelItemWidget(
                index,
                (controller.selectPage == 0
                        ? controller.creatImgModelList
                        : controller.creatVideoModelList)
                    .elementAt(index),
              );
            },
          ),
        ),
      ),
      InkWell(
        onTap: () {
          controller.toCreat();
        },
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 24.w),
          width: 234.w,
          height: 45.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(45.w / 2),
            color: CommonUtil.colorsUtil('#4E4E4E'),
            gradient: LinearGradient(
              colors: [
                CommonUtil.colorsUtil('#FFDCA4'),
                CommonUtil.colorsUtil('#C8984A'),
              ],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
          ),
          alignment: Alignment.center,
          child: Text(
            'CONTINUE'.tr,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
          ),
        ),
      )
    ];
  }

  Widget pageTitleWidget({
    required String title,
    required bool selected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: CommonUtil.colorsUtil('#4E4E4E'),
            gradient: selected == true
                ? LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#C69351'),
                      CommonUtil.colorsUtil('#EAC282'),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  )
                : null),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.w),
        child: Text(
          title,
          style: TextStyle(
              fontSize: 12.sp, fontWeight: selected == true ? FontWeight.w600 : FontWeight.w400),
        ),
      ),
    );
  }

  Widget modelItemWidget(int index, AmorsFeedModel model) {
    return InkWell(
      onTap: () {
        controller.selectModel(index);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: double.infinity,
            height: (1.sw - 44.w) / 3,
            child: Stack(
              children: [
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: (model.cover ?? '').replaceAll(
                          '?x-oss-process=style/compress', '?x-oss-process=image/resize,w_500'),
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Center(
                        child: Image.asset(
                          Assets.assetsImagesAmorListPlacehOlder,
                          width: 45.w,
                          height: 45.w,
                        ),
                      ),
                      errorWidget: (context, url, error) => Center(
                        child: Image.asset(
                          Assets.assetsImagesAmorListPlacehOlder,
                          width: 45.w,
                          height: 45.w,
                        ),
                      ),
                    ),
                  ),
                ),
                if ((controller.selectPage == 0 && controller.creatImgSelectIndex == index) ||
                    (controller.selectPage == 1 && controller.creatVideoSelectedIndex == index))
                  Positioned.fill(
                    child: Container(
                      color: Colors.black.withValues(alpha: 0.5),
                      alignment: Alignment.center,
                      child: Image.asset(
                        Assets.assetsImagesClonePermissionSelected,
                        width: 16.w,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Text(
              model.modelName ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w400, height: 1.2),
            ),
          ),
        ],
      ),
    );
  }
}
