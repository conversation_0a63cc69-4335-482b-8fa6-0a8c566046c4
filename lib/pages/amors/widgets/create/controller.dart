import 'dart:async';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/sub_pages/undress/logic.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class HomeCreatWidgetController extends GetxController {
  static HomeCreatWidgetController get to => Get.find();
  List creatImgModelList = [];
  List creatVideoModelList = [];
  int selectPage = 0;
  int creatImgSelectIndex = 0;
  int creatVideoSelectedIndex = 0;
  late ScrollController scrollCtl;
  late EasyRefreshController refreshCtl;

  //undress 相关
  var sliderValue = 0.88.obs;
  var undressStepIndex = 0.obs;
  //用户选择的图片
  String? userSelectedImage;
  //获取结果需要的task ID
  String? undressServiceTaskid;
  //选中的模式
  var undressSelectedMode = 0.obs;
  //循环请求结果计时
  static Timer? countDownTimer;
  //结果图片
  String? resultImage;
  //配置信息
  Map? undressConfig;
  List templateConfigList = [];
  //模版滚动控制器
  late ItemScrollController templateScrollCtl;

  @override
  void onInit() {
    super.onInit();
    templateScrollCtl = ItemScrollController();
    refreshCtl = EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true);
    scrollCtl = ScrollController();
    getUnDressConfig();
    getCreatImgList();
    getCreatVideoList();
  }

  //获取创建图片和视频的角色列表
  getCreatImgList() async {
    AmorsApis.amorsSearchMedia(
      genImg: true,
      cacheCallBack: (cache) {
        if (cache.isNotEmpty) {
          creatImgModelList.assignAll(cache);
          update();
        }
      },
      page: 1,
    ).then((value) {
      if (creatImgModelList.isEmpty) {
        creatImgModelList.assignAll(value);
        update();
      }
      refreshCtl.finishRefresh();
    });
  }

  //获取创建图片和视频的角色列表
  getCreatVideoList() async {
    AmorsApis.amorsSearchMedia(
      genVideo: true,
      cacheCallBack: (cache) {
        creatVideoModelList.assignAll(cache);
        update();
      },
      page: 1,
    ).then((value) {
      if (creatVideoModelList.isEmpty) {
        creatVideoModelList.assignAll(value);
        refreshCtl.finishRefresh();
        update();
      }
      refreshCtl.finishRefresh();
    });
  }

  //选择模型
  selectModel(int index) {
    if (selectPage == 0) {
      creatImgSelectIndex = index;
    } else {
      creatVideoSelectedIndex = index;
    }
    update();
  }

  //page切换
  void changePage(int index) {
    if (index == 0 && creatImgModelList.isEmpty) {
      getCreatImgList();
    }
    if (index == 1 && creatVideoModelList.isEmpty) {
      getCreatVideoList();
    }
    bool selectUndress = selectPage == 2;
    selectPage = index;
    update();
    if (selectUndress == false) {
      scrollCtl.jumpTo(0);
    }
    refreshCtl.finishRefresh();
  }

  //开始生成
  toCreat() {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    AmorsFeedModel model = selectPage == 0
        ? creatImgModelList[creatImgSelectIndex]
        : creatVideoModelList[creatVideoSelectedIndex];
    Get.toNamed(Routes.creatImgVideo, parameters: {
      'modelId': '${model.modelId}',
      'img': model.cover ?? '',
      'type': '$selectPage'
    });
  }

  onRefresh() {
    if (selectPage == 0) {
      getCreatImgList();
    } else {
      getCreatVideoList();
    }
  }

  ///////////////////////////undress 相关/////////////////////////////////////////
//获取配置信息
  getUnDressConfig() async {
    undressConfig = await AmorsApis.getUnDressConfig();
    if (undressConfig != null) {
      Map priceConfig = undressConfig!['price'];
      Map imgConfig = undressConfig!['noDressImg'];
      for (var i = 0; i < priceConfig.keys.length; i++) {
        String name = priceConfig.keys.toList()[i];
        String style = priceConfig.keys.toList()[i];
        if (name.contains('underwear_')) {
          name = style.replaceAll('underwear_', '');
        }
        templateConfigList.add({
          'name': name,
          'style': style,
          'price': priceConfig[style],
          'img': imgConfig[style],
        });
      }
    }
  }

  //选择模式
  selectUndressMode(int index) {
    undressSelectedMode.value = index;
    templateScrollCtl.scrollTo(
        index: index, duration: const Duration(milliseconds: 200), alignment: 0.4);
  }

  onTapBtn() {
    if (undressStepIndex.value == 0) {
      selectImage();
      return;
    }
    if (undressStepIndex.value == 1) {
      startUndress();
      return;
    }
    if (undressStepIndex.value == 3) {
      undressStepIndex.value = 0;
      resultImage = null;
      userSelectedImage = null;
      undressServiceTaskid = null;
      selectImage();
    }
  }

  //选择照片
  selectImage() async {
    Analytics().logEvent(Analytics.cunupload);
    String? result = await UndressLogic.selectImage();
    if (result != null && undressConfig != null) {
      userSelectedImage = result;
      //上传图片后展示选择模式
      undressStepIndex.value = 1;
    } else {}
  }

  //检查用户信息，开始脱衣操作
  startUndress() async {
    Analytics().logEvent(Analytics.cungenerate);
    //非会员弹出购买页
    if (UserService.to.isVip == false) {
      PurchaseSheet.show(source: Analytics.devsucvipundrphoto, page: Analytics.undress);
      return;
    }
    //获取最新的余额信息
    Loading.show();
    await UserService.to.getUserInfo();
    Loading.dismiss();
    // 检查余额
    if ((UserService.to.userinfo.value.gems ?? 0) <
        templateConfigList[undressSelectedMode.value]['price']) {
      Loading.toast('Insufficient Balance');
      Future.delayed(const Duration(milliseconds: 2000), () {
        PurchaseSheet.show(source: Analytics.tvipundress, page: Analytics.undress);
      });
      return;
    }
    startUndressRequest();
  }

  //请求后端脱衣接口
  startUndressRequest() async {
    Map? result = await AmorsApis.unDressLaunchTask(
      sourceImage: userSelectedImage!,
      style: templateConfigList[undressSelectedMode.value]['style'],
    );
    if (result?['taskId'] != null) {
      undressServiceTaskid = result?['taskId'];
      //开始loading
      undressStepIndex.value = 2;
      requestCountDown();
    }
  }

  //请求脱衣结果图片倒计时
  requestCountDown() {
    //30秒倒计时间隔10
    int awaitCountDownNum = 50;
    countDownTimer ??= Timer.periodic(const Duration(seconds: 1), (timer) {
      awaitCountDownNum--;
      if (awaitCountDownNum % 10 == 0) {
        requestImage(showError: awaitCountDownNum == 0);
      }
      if (awaitCountDownNum <= 0 || resultImage != null) {
        removeTimer();
      }
    });
  }

  //请求结果图片
  requestImage({bool showError = false}) async {
    if (undressServiceTaskid != null) {
      Map? result = await AmorsApis.unDressTaskStatus(taskId: undressServiceTaskid!);
      if (result?['picUrl'] != null) {
        resultImage = result?['picUrl'];
        DefaultCacheManager().getSingleFile(resultImage!).then((value) async {
          undressStepIndex.value = 3;
        });
        Analytics().logEvent(Analytics.ungensuc);
        removeTimer();
      } else if (showError == true) {
        Loading.toast('Generation failed.');
        undressStepIndex.value = 0;
      }
    }
  }

  removeTimer() {
    if (countDownTimer != null) {
      countDownTimer!.cancel();
      countDownTimer = null;
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollCtl.dispose();
    removeTimer();
  }
}
