import 'dart:convert';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:before_after/before_after.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'controller.dart';

class CreatUndressWidget extends GetView<HomeCreatWidgetController> {
  const CreatUndressWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 10.verticalSpaceFromWidth,
                Container(
                  width: double.infinity,
                  // height: 400.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  // margin: EdgeInsets.symmetric(horizontal: 12.w),
                  clipBehavior: Clip.antiAlias,
                  child: controller.undressStepIndex.value == 0
                      ? BeforeAfter(
                          value: controller.sliderValue.value,
                          direction: SliderDirection.horizontal,
                          width: double.infinity,
                          after: CachedNetworkImage(
                            fit: BoxFit.cover,
                            imageUrl: undressBefore,
                            // alignment: Alignment.topCenter,
                          ),
                          before: CachedNetworkImage(
                            fit: BoxFit.cover,
                            imageUrl: undressAfter,
                            // alignment: Alignment.topCenter,
                          ),
                          onValueChanged: (value) {
                            if (value < 0.99) {
                              controller.sliderValue.value = value;
                            }
                          },
                          trackColor: Colors.white,
                          trackWidth: 2,
                          thumbHeight: 30.w,
                          thumbWidth: 30.w,
                          thumbPosition: 0.5,
                          thumbDecoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(Assets.assetsImagesUndressThumbIcon),
                            ),
                          ),
                        )
                      : Stack(
                          children: [
                            if (controller.undressStepIndex.value == 1 ||
                                controller.undressStepIndex.value == 2)
                              Image.memory(
                                base64Decode(controller.userSelectedImage!),
                                width: double.infinity,
                                fit: BoxFit.cover,
                                alignment: Alignment.topCenter,
                              ),
                            if (controller.undressStepIndex.value == 3)
                              CachedNetworkImage(
                                fit: BoxFit.cover,
                                imageUrl: controller.resultImage!,
                              ),
                          ],
                        ),
                ),
                16.verticalSpaceFromWidth,
                if (controller.undressStepIndex.value == 0)
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'undressTip1'.tr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        6.verticalSpaceFromWidth,
                        Text(
                          'undressTip2'.tr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        6.verticalSpaceFromWidth,
                        Text(
                          'undressTip3'.tr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        14.verticalSpaceFromWidth,
                        Center(
                          child: Text(
                            'undressTip4'.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (controller.undressStepIndex.value == 1) undressTemplate(),
                90.verticalSpaceFromWidth,
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: 1.sw,
              height: 80.w,
              padding: EdgeInsets.symmetric(horizontal: 35.w, vertical: 10.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    CommonUtil.colorsUtil('#020204').withValues(alpha: 0),
                    CommonUtil.colorsUtil('#020204').withValues(alpha: 0.5),
                    CommonUtil.colorsUtil('#020204').withValues(alpha: 0.8),
                    CommonUtil.colorsUtil('#020204'),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              alignment: Alignment.topCenter,
              child: InkWell(
                onTap: () {
                  controller.onTapBtn();
                },
                child: Container(
                  height: 45.w,
                  width: 240.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(45.w / 2),
                    gradient: LinearGradient(
                      colors: [
                        AppColor.colorsUtil('#FFDCA4'),
                        AppColor.colorsUtil('#C8984A'),
                      ],
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.undressStepIndex.value == 0
                        ? 'Upload a photo'.tr
                        : controller.undressStepIndex.value == 3
                            ? 'Generate another one'.tr
                            : 'Generate'.tr,
                    style: TextStyle(
                        fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                  ),
                ),
              ),
            ),
          ),
          if (controller.undressStepIndex.value == 2)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.45),
                ),
              ),
            ),
          if (controller.undressStepIndex.value == 2)
            Positioned.fill(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset(
                      'assets/lottie/undress_loading.json',
                      animate: true,
                      repeat: true,
                    ),
                    Text(
                      'AI Generating...'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  //模版
  Widget undressTemplate() {
    return SizedBox(
      width: 1.sw,
      height: 116.w,
      child: NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (OverscrollIndicatorNotification? overscroll) {
          overscroll!.disallowIndicator();
          return true;
        },
        child: ScrollablePositionedList.separated(
          physics: const ClampingScrollPhysics(),
          itemScrollController: controller.templateScrollCtl,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          scrollDirection: Axis.horizontal,
          itemCount: controller.templateConfigList.length,
          itemBuilder: (BuildContext context, int index) {
            Map data = controller.templateConfigList[index];
            return InkWell(
              onTap: () {
                controller.selectUndressMode(index);
              },
              child: SizedBox(
                width: 76.w,
                child: Column(
                  children: [
                    Obx(
                      () => Container(
                        width: 76.w,
                        height: 76.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.r),
                          border: controller.undressSelectedMode.value == index
                              ? Border.all(color: CommonUtil.colorsUtil('#EAC282'), width: 2)
                              : null,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: data['img'] ?? '',
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Image.asset(
                                Assets.assetsImagesMessageImagePlaceholder,
                                fit: BoxFit.cover,
                              );
                            },
                            placeholder: (context, url) {
                              return Image.asset(
                                Assets.assetsImagesMessageImagePlaceholder,
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${data['name']}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w600),
                    ),
                    2.verticalSpaceFromWidth,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.assetsImagesSessionGems,
                          width: 15.w,
                        ),
                        4.horizontalSpace,
                        Text(
                          '${data['price']}',
                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 8.horizontalSpace;
          },
        ),
      ),
    );
  }
}
