import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/moment_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/widgets/video/controller.dart';
import 'package:amor_app/pages/session/widgets/video/video_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeMomentsListWidgetController extends GetxController {
  static HomeMomentsListWidgetController get to => Get.find();
  late EasyRefreshController refreshCtl;
  int page = 1;
  List momentsList = [];
  @override
  void onInit() {
    super.onInit();
    refreshCtl = EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true);
    getMomentsList(refresh: false);
  }

  //获取分类列表
  getMomentsList({required bool refresh}) async {
    List<MomentModel> list = [];
    AmorsApis.getMomentsDataList(
      page: page,
      cacheCallBack: (cache) {
        if (refresh == false) {
          list = cache;
          setMomentsList(list: cache);
        }
      },
    ).then((value) {
      //没有缓存数据
      if (list.isEmpty) {
        setMomentsList(list: value);
      }
    });
  }

  setMomentsList({required List list}) {
    if (page == 1) {
      momentsList.assignAll(list);
      refreshCtl.resetFooter();
    } else {
      momentsList.addAll(list);
    }
    refreshCtl.finishRefresh();
    if (list.isEmpty) {
      refreshCtl.finishLoad(IndicatorResult.noMore);
    } else {
      refreshCtl.finishLoad(IndicatorResult.success);
    }
    update();
  }

  onRefresh() {
    page = 1;
    getMomentsList(refresh: true);
  }

  onLoadMore() {
    page++;
    getMomentsList(refresh: true);
  }

  onTapItem(int index) {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    MomentModel model = momentsList.elementAt(index);
    if (UserService.to.isVip == false && model.istop != true) {
      PurchaseSheet.show(
          page: ReportUtil.chatwin,
          source:
              model.duration == null ? Analytics.devsucvippostpic : Analytics.devsucvippostvideo);
      return;
    }
    //展示图片
    if (model.duration == null) {
      showImage(index);
    }
    //展示视频
    if (model.duration != null) {
      showVideo(index);
    }
  }

  showImage(int index) {
    MomentModel model = momentsList.elementAt(index);
    Get.dialog(
      InkWell(
        onTap: () => Get.back(),
        child: CachedNetworkImage(
          imageUrl: model.media ?? '',
          placeholder: (context, url) => const Center(
              child: CircularProgressIndicator(
            color: Colors.white,
          )),
          errorWidget: (context, url, error) => Icon(Icons.image_not_supported_outlined, size: 40),
        ),
      ),
      barrierColor: AppColor.mainBg,
      barrierDismissible: true,
    );
  }

  showVideo(int index) async {
    MomentModel model = momentsList.elementAt(index);
    Get.lazyPut(() => SessionVideoPlayController(videoUrl: model.media ?? ''));
    await Get.dialog(const SessionVideoPlayPage(), useSafeArea: false, barrierDismissible: false);
    Get.delete<SessionVideoPlayController>(force: true);
  }

  //跳转聊天
  toChat(MomentModel model) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    if (model.characterId == null) {
      return;
    }
    //新用户没有填写基础信息
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: model.characterId!);
    if (params != null && params['sessionNo'] != null && Get.currentRoute.startsWith(Routes.tabs)) {
      Get.toNamed(Routes.session, arguments: params['sessionNo']);
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshCtl.dispose();
  }
}
