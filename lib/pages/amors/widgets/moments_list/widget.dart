import 'dart:ui';
import 'package:amor_app/common/models/amors_models/moment_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/widgets/moments_list/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeMomentsListWidget extends StatelessWidget {
  const HomeMomentsListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: HomeMomentsListWidgetController(),
      builder: (controller) => EasyRefresh.builder(
        controller: controller.refreshCtl,
        onRefresh: () => controller.onRefresh(),
        onLoad: () => controller.onLoadMore(),
        childBuilder: (context, physics) => ListView.separated(
          itemBuilder: (context, index) => listItemWidget(index),
          physics: physics,
          separatorBuilder: (context, index) => Container(
            width: double.infinity,
            height: 1.w,
            color: CommonUtil.colorsUtil('#4E4E4E'),
            margin: EdgeInsets.symmetric(vertical: 16.w),
          ),
          itemCount: controller.momentsList.length,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.w),
        ),
      ),
    );
  }

  Widget listItemWidget(int index) {
    MomentModel model = HomeMomentsListWidgetController.to.momentsList.elementAt(index);
    String cover = (model.duration != null ? (model.cover ?? '') : (model.media ?? ''));
    String avatar = (model.characterAvatar ?? '');
    // if (avatar.endsWith('.png')) {
    //   avatar = '$avatar?x-oss-process=image/resize,w_1000';
    // }
    // if (cover.endsWith('.png')) {
    //   cover = '$cover?x-oss-process=image/resize,w_1000';
    // }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () => HomeMomentsListWidgetController.to.toChat(model),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 32.w,
                height: 32.w,
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: avatar,
                          fit: BoxFit.cover,
                          placeholder: (context, url) {
                            return Container(
                              width: double.infinity,
                              height: double.infinity,
                              color: CommonUtil.colorsUtil('#949494'),
                            );
                          },
                          errorWidget: (context, url, error) => Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: CommonUtil.colorsUtil('#949494'),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 1.w,
                      right: 1.w,
                      child: Container(
                        width: 7.w,
                        height: 7.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(width: 1.w, color: Colors.white),
                          color: CommonUtil.colorsUtil('#30D158'),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              12.horizontalSpace,
              Text(
                model.characterName ?? '',
                style: TextStyle(
                    fontSize: 16.sp, fontWeight: FontWeight.w600, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        8.verticalSpaceFromWidth,
        Text(
          model.text ?? '',
          style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400),
        ),
        6.verticalSpaceFromWidth,
        InkWell(
          onTap: () {
            HomeMomentsListWidgetController.to.onTapItem(index);
          },
          child: Container(
            width: double.infinity,
            height: 1.sw - 24.w,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Padding(
                    padding: EdgeInsets.all(
                        UserService.to.isVip == false && model.istop != true ? 1 : 0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: CachedNetworkImage(
                        imageUrl: cover,
                        fadeInDuration: Duration(milliseconds: 100),
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: double.infinity,
                          height: 1.sw - 26.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: AppColor.mainBg,
                          ),
                        ),
                        errorWidget: (context, url, err) => Container(
                          width: double.infinity,
                          height: 1.sw - 26.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: AppColor.mainBg,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                if (UserService.to.isVip == false && model.istop != true)
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 20.0, sigmaY: 20.0),
                        child: Container(
                          color: Colors.black.withValues(alpha: 0.6),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                Assets.assetsImagesHomeMomLockIcon,
                                width: 36.w,
                              ),
                              12.verticalSpaceFromWidth,
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 4.w),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.w / 2),
                                  gradient: LinearGradient(
                                    colors: [
                                      CommonUtil.colorsUtil('#C69351'),
                                      CommonUtil.colorsUtil('#EAC282'),
                                    ],
                                  ),
                                ),
                                child: Text(
                                  'Subscribe to view posts'.tr,
                                  style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w400),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                if (UserService.to.isVip == false && model.istop != true)
                  Positioned(
                    bottom: 12.w,
                    right: 12.w,
                    child: model.duration == null
                        ? Image.asset(
                            Assets.assetsImagesHomeMomLockImage,
                            width: 26.w,
                          )
                        : Container(
                            height: 26.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(26.w / 2),
                              color: Colors.white.withValues(alpha: 0.2),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 12.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  Assets.assetsImagesHomeMomLockVideoTime,
                                  height: 20.w,
                                ),
                                4.horizontalSpace,
                                Text(
                                  CommonUtil.durationTransformPro(model.duration!),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w400,
                                    height: 1.2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                // 视频类型展示播放按钮
                if ((UserService.to.isVip == true || model.istop == true) && model.duration != null)
                  Positioned.fill(
                    child: Center(
                      child: Image.asset(
                        Assets.assetsImagesSessionVideoPlay,
                        width: 36.w,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget imageErrorWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColor.colorsUtil('#434343'),
      alignment: Alignment.center,
      child: Image.asset(
        Assets.assetsImagesAmorListPlacehOlder,
        width: 45.w,
        height: 45.w,
      ),
    );
  }
}
