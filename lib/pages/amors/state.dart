import 'package:get/get.dart';

class AmorsPageControllerState {
  //列表数据
  var dataList = [].obs;
  int discoverPage = 1;
  //分类类别列表
  var tagsList = [].obs;
  //选中的分类下标
  var selectedCategoryIndex = 0.obs;
  //discoverPage为1时是否删除列表数据（刷新或者手动选择了某一个分类就需要删除之前的列表数据，如果是请求当前分类数据为空后自动加载下一个分类的数据就不删除）
  bool clearList = true;
  //右下角最后一个item的下标
  int lastItemindex = 0;
  //已曝光的角色
  Map exposureData = {};
  //判断是否展示搜索和分类按钮
  var showSearch = false.obs;
  //标签切换
  var tagSwitchType = [];
  //展示搜索列表
  bool showSearchWidget = false;
  //顶部菜单切换
  var topMenuIndex = 0.obs;
}
