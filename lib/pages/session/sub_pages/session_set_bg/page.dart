import 'dart:ui';

import 'package:amor_app/common/models/session_model/session_bg_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'controller.dart';

class SessionSetBgPage extends StatelessWidget {
  const SessionSetBgPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<SessionSetBgPageController>(
        builder: (controller) {
          return SingleChildScrollView(
            child: Stack(
              children: [
                SizedBox(
                  width: 1.sw,
                  height: 1.sh,
                  child: GestureDetector(
                    onTap: () {
                      // controller.showBottomView.toggle();
                    },
                    child: Obx(
                      () => controller.bgList.isEmpty
                          ? Container()
                          : TabBarView(
                              controller: controller.tabController,
                              physics: const NeverScrollableScrollPhysics(),
                              children: controller.bgList
                                  .map(
                                    (element) => CachedNetworkImage(
                                      imageUrl: (element as SessionBgModelListItem).url ?? '',
                                      fit: BoxFit.cover,
                                      width: 1.sw,
                                      height: 1.sh,
                                    ),
                                  )
                                  .toList(),
                            ),
                    ),
                  ),
                ),
                Positioned(
                  top: CommonUtil.statusBarHeight(context) + 8,
                  left: 16,
                  right: 16,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const CustomBackButton(),
                      Obx(
                        () => controller.scrollIndex != controller.selectedIndex
                            ? InkWell(
                                onTap: () {
                                  controller.bgSave();
                                },
                                child: Container(
                                  width: 68.w,
                                  height: 32.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(32.w / 2),
                                    color: AppColor.colorsUtil('#F0BE72'),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Sure'.tr,
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColor.primaryText,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ),
                              )
                            : Container(),
                      ),
                    ],
                  ),
                ),
                Obx(
                  () => AnimatedPositioned(
                    bottom: controller.showBottomView.value
                        ? CommonUtil.bottomBarHeight() + 30.w
                        : -150.w,
                    left: 0,
                    right: 0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.elasticOut,
                    child: SizedBox(
                      width: 1.sw,
                      height: 164.w,
                      child: NotificationListener<OverscrollIndicatorNotification>(
                        onNotification: (OverscrollIndicatorNotification? overscroll) {
                          overscroll!.disallowIndicator();
                          return true;
                        },
                        child: Obx(
                          () => controller.bgList.isEmpty
                              ? Container()
                              : ScrollablePositionedList.builder(
                                  physics: const ClampingScrollPhysics(),
                                  itemScrollController: controller.bgItemScrollCtl,
                                  // initialAlignment: 0.4,
                                  // initialScrollIndex: controller.scrollIndex.value,
                                  padding: REdgeInsets.symmetric(horizontal: 10, vertical: 2),
                                  scrollDirection: Axis.horizontal,
                                  itemCount: controller.bgList.length + 1,
                                  itemBuilder: (BuildContext context, int index) {
                                    return bgItemWidget(
                                        index: index,
                                        model: index == 0
                                            ? null
                                            : controller.bgList.elementAt(index - 1));
                                  },
                                ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget bgItemWidget({required int index, SessionBgModelListItem? model}) {
    return GestureDetector(
      onTap: () {
        if (index == 0) {
          SessionSetBgPageController.to.selecImage();
          return;
        }
        SessionSetBgPageController.to.sessionChatBgSet(model: model!, index: index);
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        width: 90.w,
        height: 160.w,
        margin: REdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          border: index == 0 || model?.selected == true
              ? Border.all(
                  width: index == 0 ? 1 : 2,
                  color: index == 0 ? Colors.white : AppColor.colorsUtil('#FF6B00'),
                  strokeAlign: BorderSide.strokeAlignOutside,
                )
              : null,
        ),
        clipBehavior: Clip.antiAlias,
        child: index == 0
            ? customButtonWidget()
            : Stack(
                children: [
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: model?.url ?? '',
                      fit: BoxFit.cover,
                      errorWidget: (context, url, err) {
                        return Container(
                          color: AppColor.colorsUtil('#111214').withValues(alpha: 0.6),
                        );
                      },
                      placeholder: (context, url) {
                        return Container(
                          color: AppColor.colorsUtil('#111214').withValues(alpha: 0.6),
                        );
                      },
                    ),
                  ),
                  if (model?.type == 'official' &&
                      model?.paymentStatus == 'unpaid' &&
                      model?.paymentStatus != 'free')
                    Positioned.fill(
                      child: Container(
                        margin: EdgeInsets.all(4.w),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              CommonUtil.colorsUtil('#F3C072', alpha: 0),
                              CommonUtil.colorsUtil('#E8B361', alpha: 0.9),
                              CommonUtil.colorsUtil('#FFCB7C', alpha: 1),
                              CommonUtil.colorsUtil('#FFCB7C', alpha: 1),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                    ),
                  if (model?.type == 'custom')
                    Positioned.fill(
                      child: Image.asset(
                        Assets.assetsImagesSessionSetBgCustomBorder,
                        fit: BoxFit.fill,
                      ),
                    ),
                  //官方非免费、未购买图片才展示价格
                  if (model?.type == 'official' && model?.paymentStatus == 'unpaid')
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.0),
                              Colors.black,
                            ],
                          ),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: priceWidgetList(
                              customItem: false,
                              originGems: '${model?.originGems ?? 0}',
                              saleGems: '${model?.saleGems ?? 0}',
                              discount: '-${model?.discount ?? 0}%'),
                        ),
                      ),
                    ),
                  //官方非免费图片才展示边框
                  if (model?.type == 'official' && model?.paymentStatus != 'free')
                    Positioned.fill(
                      child: Image.asset(
                        Assets.assetsImagesSessionSetBgChargeBorder,
                        fit: BoxFit.fill,
                      ),
                    ),
                  if (model?.type == 'defaultUrl')
                    Positioned(
                      bottom: 10.w,
                      left: 0,
                      right: 0,
                      child: Text(
                        'Default'.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 8.sp,
                          color: AppColor.colorsUtil('#56525A'),
                          shadows: [
                            Shadow(color: Colors.black.withValues(alpha: 0.25), blurRadius: 1)
                          ],
                        ),
                      ),
                    ),
                ],
              ),
      ),
    );
  }

  //创建自定义
  Widget customButtonWidget() {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
      child: Container(
        color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: priceWidgetList(
              customItem: true,
              originGems: '${SessionSetBgPageController.to.bgModel.originGems ?? 0}',
              saleGems: '${SessionSetBgPageController.to.bgModel.saleGems ?? 0}',
              discount: '-${SessionSetBgPageController.to.bgModel.discount ?? 0}%'),
        ),
      ),
    );
  }

  List<Widget> priceWidgetList(
      {required bool customItem, //自定义
      required String originGems,
      required String saleGems,
      required String discount}) {
    return [
      if (customItem)
        Image.asset(
          Assets.assetsImagesSessionSetBgCustomIcon,
          width: 36.w,
        ),
      if (customItem) 5.verticalSpace,
      if (customItem)
        Text(
          'Custom'.tr,
          style: TextStyle(fontSize: 12.sp),
        ),
      if (customItem) 15.verticalSpace,
      SizedBox(
        width: 40.w,
        height: 24.w,
        child: Stack(
          children: [
            Positioned(
              top: 6.w,
              width: 40.w,
              height: 18.w,
              child: Image.asset(
                customItem ? Assets.assetsImagesSessionSetHot : Assets.assetsImagesSessionSetHot,
              ),
            ),
            Positioned(
              top: 0,
              right: 3.w,
              child: Text(
                discount,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 9.sp,
                  height: 1.3,
                  fontWeight: FontWeight.w700,
                  fontStyle: FontStyle.italic,
                  shadows: [Shadow(color: Colors.black.withValues(alpha: 0.57), blurRadius: 1)],
                ),
              ),
            ),
          ],
        ),
      ),
      customItem ? 3.verticalSpaceFromWidth : 0.verticalSpace,
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: REdgeInsets.only(top: customItem ? 4 : 2),
            child: Image.asset(
              Assets.assetsImagesSessionGems,
              width: customItem ? 14.w : 12.w,
            ),
          ),
          3.horizontalSpace,
          Padding(
            padding: REdgeInsets.only(top: customItem ? 4 : 2),
            child: Text(
              saleGems,
              style: TextStyle(
                fontSize: customItem ? 17.sp : 15.sp,
                color: AppColor.colorsUtil('#FFCB7C'),
                fontWeight: FontWeight.w600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
          3.horizontalSpace,
          Padding(
            padding: REdgeInsets.only(top: customItem ? 6 : 2),
            child: Text(
              originGems,
              style: TextStyle(
                  fontSize: customItem ? 11.sp : 9.sp,
                  fontWeight: FontWeight.w500,
                  fontStyle: FontStyle.italic,
                  color: Colors.white.withValues(alpha: 0.5),
                  decoration: TextDecoration.lineThrough,
                  decorationColor: Colors.white.withValues(alpha: 0.5)),
            ),
          ),
        ],
      ),
      10.verticalSpaceFromWidth,
    ];
  }
}
