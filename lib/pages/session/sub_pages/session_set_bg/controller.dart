import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_bg_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'widgets/chat_bg_set_preview.dart';
import 'widgets/confirm_purchase.dart';
import 'widgets/permissions_dialog.dart';

class SessionSetBgPageController extends GetxController with GetTickerProviderStateMixin {
  static SessionSetBgPageController get to => Get.find();
  late ItemScrollController bgItemScrollCtl;
  TabController? tabController;

  String? modelId;
  var bgList = [].obs;
  // var currentBgUrl = (ChatPageController.to.state.bgImg.value).obs;
  var selectedIndex = 0.obs;
  var scrollIndex = 0.obs;
  //展示底部列表
  var showBottomView = true.obs;
  SessionBgModel bgModel = SessionBgModel();
  @override
  void onInit() {
    super.onInit();
    modelId = Get.parameters["modelId"];
    bgItemScrollCtl = ItemScrollController();
    getBgList();
    ReportUtil.reportViews(page: ReportUtil.cards, action: ReportUtil.view);
  }

  //获取背景列表
  getBgList({bool refresh = false, bool useCache = true}) async {
    if (modelId == null) {
      Loading.toast('Please wait...');
      return;
    }
    SessionBgModel? model = await SessionApis.sessionChatBgList(
        modelId: int.parse(modelId!),
        cacheCallBack: (SessionBgModel? cache) {
          if (refresh == false && useCache == true) {
            setBgList(cache);
          }
        });
    if (refresh == false) {
      setBgList(model);
    }
  }

  setBgList(SessionBgModel? model) {
    if (model == null) {
      return;
    }
    bgModel = model;
    List list = model.list ?? [];
    for (SessionBgModelListItem element in list) {
      if (element.selected == true) {
        selectedIndex.value = list.indexOf(element) + 1;
        scrollIndex.value = selectedIndex.value;
        break;
      }
    }
    initTabbarCtl(list.length);
    bgList.assignAll(list);
  }

  initTabbarCtl(int length) {
    tabController = TabController(
        length: length,
        vsync: this,
        initialIndex: selectedIndex.value > 0 ? selectedIndex.value - 1 : 0);
    tabController?.addListener(
      () {
        sessionChatBgSet(index: tabController!.index + 1, bigItemPositionsListener: true);
      },
    );
  }

  //选择图片
  selecImage() async {
    if (bgModel.originGems == null) {
      return;
    }
    // bgItemScrollCtl.scrollTo(index: 0, duration: const Duration(milliseconds: 200), alignment: 0.4);
    String? path = await SelectMediaUtil.selectImage(
      ratioX: 1.sw,
      ratioY: 1.sh - 80.w,
      source: ImageSource.gallery,
      noticePermissions: false,
    );

    if (path == 'no permissions') {
      noPermissions();
      return;
    }
    if (path != null) {
      if (path.isEmpty) {
        Loading.toast('Please try again later');
        return;
      }
      Loading.show();
      String? url = await uploadImage(path);
      Loading.dismiss();
      if (url != null) {
        selectedBg(url: url, type: 'custom', filePath: path);
      }
    }
  }

  //缺少相册权限
  noPermissions() async {
    int? result = await Get.dialog(const PermissionsDialogWidget());
    if (result == 1) {
      openAppSettings();
    }
  }

  //上传图片
  Future<String?> uploadImage(String filePath) async {
    Loading.show();
    String? imageUrl =
        await UploadOss.upload(path: filePath, format: 'png', servicePath: ossServicePathChat);
    Loading.dismiss();
    return imageUrl;
  }

  //选择背景 预览
  selectedBg(
      {required String url,
      required String type,
      String? filePath,
      String? bgId,
      int? originGems,
      int? saleGems}) async {
    await Get.dialog(
      SetBgPreview(
        imagePath: filePath,
        imageUrl: url,
        purchase: () {
          confirmPurchase(
              originGems: bgModel.originGems ?? 0,
              saleGems: bgModel.saleGems ?? 0,
              discount: bgModel.discount ?? 0,
              type: type,
              url: url);
        },
      ),
      useSafeArea: false,
      barrierDismissible: false,
    );
  }

  //购买背景
  bgCountomPurchase({String? url, required String type, String? bgId, required int gems}) async {
    Loading.show();
    Map? purchaseResult = await SessionApis.bgPurchase(
        modelId: int.parse(modelId!), type: type, url: url, bgId: bgId);
    if (purchaseResult != null) {
      ReportUtil.reportEvents(page: ReportUtil.cards, action: ReportUtil.pay, value: '$gems,$bgId');
      if (purchaseResult['state'] == 1) {
        Loading.toast('Insufficient Balance');
      }
      if (purchaseResult['state'] == 0 && purchaseResult['url'] != null) {
        getBgList(useCache: false);
        //刷新聊天页余额和背景
        if (Get.isRegistered<SessionPageController>()) {
          SessionPageController.to.getSessionConfig(updatePage: false);
        }
        Loading.dismiss();
        if (url != null) {
          Get.back();
        }
      }
    }
  }

  //确认购买弹窗
  confirmPurchase(
      {required int originGems,
      required int saleGems,
      required int discount,
      String? url,
      required String type,
      String? bgId}) async {
    int? result = await Get.dialog(ChatBgConfirmPurchase(
      originGems: originGems,
      saleGems: saleGems,
      discount: discount,
    ));
    if (result == 1) {
      bgCountomPurchase(
          type: type, bgId: bgId, url: url, gems: UserService.to.isVip ? saleGems : originGems);
    }
    if (result == 2) {
      ReportUtil.reportEvents(page: ReportUtil.cards, action: ReportUtil.goHot);
      ReportUtil.reportViews(page: ReportUtil.cards, action: ReportUtil.go, value: ReportUtil.hot);
      Analytics().logEvent(Analytics.tailorcardHot,
          screen: Analytics.pageCards, charID: SessionPageController.to.state.modelId);
      Analytics().logEvent(
        Analytics.view,
        sourceEvent: Analytics.tailorcardHot,
        sourceChar: SessionPageController.to.state.modelId.toString(),
        sourceScreen: Analytics.pageCards,
        screen: Analytics.pageHot,
      );
      PurchaseSheet.show(page: ReportUtil.chatwin, modelId: SessionPageController.to.state.modelId);
    }
    if (result == null) {
      ReportUtil.reportEvents(page: ReportUtil.cards, action: ReportUtil.cancel);
    }
  }

  //选择背景
  sessionChatBgSet(
      {SessionBgModelListItem? model,
      required int index,
      bool bigItemPositionsListener = false}) async {
    if (model?.selected == true) {
      return;
    }
    bgItemScrollCtl.scrollTo(
        index: index, duration: const Duration(milliseconds: 200), alignment: 0.4);
    if (bigItemPositionsListener == false) {
      // tabController?.animateTo(index - 1, duration: Duration.zero, curve: Curves.linear);
    }
    scrollIndex.value = index;
    List<SessionBgModelListItem> list = bgModel.list ?? [];
    bgList.assignAll(
      list.map((e) {
        e.selected = list.indexOf(e) == scrollIndex.value - 1;
        return e;
      }).toList(),
    );
  }

  //保存背景
  bgSave() async {
    SessionBgModelListItem model = bgList.elementAt(scrollIndex.value - 1);
    //选择默认图片
    if (model.id == '-1') {}
    //选择官方非免费、未购买图片
    if (model.type == 'official' && model.paymentStatus == 'unpaid') {
      if (model.url != null && model.url!.isNotEmpty) {
        confirmPurchase(
            saleGems: model.saleGems ?? 0,
            originGems: model.originGems ?? 0,
            discount: model.discount ?? 0,
            type: 'official',
            bgId: model.bgId);
      }
      return;
    }
    if (model.id == null) {
      return;
    }
    bool result = await SessionApis.bgActivate(modelId: int.parse(modelId!), id: model.id!);
    if (result) {
      //刷新聊天页余额
      if (Get.isRegistered<SessionPageController>()) {
        SessionPageController.to.getSessionConfig(updatePage: false);
      }
      selectedIndex.value = scrollIndex.value;
      getBgList(refresh: true, useCache: false);
    }
  }

  @override
  void dispose() {
    super.dispose();
    tabController?.dispose();
    ReportUtil.reportViews(page: ReportUtil.cards, action: ReportUtil.quit);
  }
}
