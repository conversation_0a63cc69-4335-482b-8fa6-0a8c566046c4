import 'dart:io';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import '../controller.dart';

class SetBgPreview extends StatelessWidget {
  final String imageUrl;
  final VoidCallback purchase;
  final String? imagePath;

  const SetBgPreview({
    super.key,
    required this.imageUrl,
    required this.imagePath,
    required this.purchase,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: imagePath != null
                ? Image.file(
                    File(imagePath!),
                    fit: BoxFit.cover,
                  )
                : CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.cover,
                  ),
          ),
          Positioned(
            top: CommonUtil.statusBarHeight(context) + 8,
            left: 16,
            child: const CustomBackButton(),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => purchase.call(),
              child: Container(
                width: 234.w,
                height: 45.w,
                margin: EdgeInsets.only(bottom: CommonUtil.bottomBarHeight() + 45.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(45.w / 2),
                  gradient: LinearGradient(
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  'All Set'.tr,
                  style:
                      TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
