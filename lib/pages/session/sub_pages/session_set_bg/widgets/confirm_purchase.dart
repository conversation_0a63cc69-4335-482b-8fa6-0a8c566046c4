import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

//确认购买弹窗
class ChatBgConfirmPurchase extends StatelessWidget {
  final int originGems;
  final int saleGems;
  final int discount;
  const ChatBgConfirmPurchase(
      {super.key, required this.originGems, required this.saleGems, required this.discount});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.w,
        height: UserService.to.isVip ? 311.w : 292.w,
        decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage(UserService.to.isVip
                    ? Assets.assetsImagesSessionBottomDialogBgVip
                    : Assets.assetsImagesSessionBottomDialogBg))),
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        margin: EdgeInsets.only(bottom: 100.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            30.verticalSpaceFromWidth,
            SizedBox(
              width: 76.w,
              height: 76.w,
              child: Stack(
                children: [
                  Positioned.fill(
                    child: Image.asset(
                      UserService.to.isVip
                          ? Assets.assetsImagesSessionBgPurchaseVip
                          : Assets.assetsImagesSessionBgPurchaseUser,
                      fit: BoxFit.contain,
                      width: 76.w,
                      height: 76.w,
                    ),
                  ),
                  if (UserService.to.isVip == false)
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Text(
                        '-$discount%',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.colorsUtil('#C48012'),
                          fontWeight: FontWeight.w600,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    )
                ],
              ),
            ),
            20.verticalSpace,
            Text(
              UserService.to.isVip ? 'Confirm Payment'.tr : 'Amor HOT',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 20.sp, color: AppColor.primaryText, fontWeight: FontWeight.w700),
            ),
            10.verticalSpace,
            Text(
              UserService.to.isVip
                  ? 'vip buy card'.trArgs(['$saleGems'])
                  : 'user buy card'.trArgs(['$discount']),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.primaryText.withValues(alpha: 0.8),
              ),
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: UserService.to.isVip
                      ? customButton(
                          onTap: () {
                            Get.back(result: 0);
                          },
                          title: 'Cancel'.tr,
                          colors: ['#3D3D43'],
                          gems: null)
                      : customButton(
                          onTap: () {
                            //支付
                            Get.back(result: 1);
                          },
                          title: 'Pay'.tr,
                          colors: ['#3D3D43'],
                          gems: originGems),
                ),
                16.horizontalSpace,
                Expanded(
                  child: UserService.to.isVip
                      ? customButton(
                          onTap: () {
                            //支付
                            Get.back(result: 1);
                          },
                          title: 'Pay'.tr,
                          colors: ['#FFDCA4', '#C8984A'],
                          fontWeight: FontWeight.w600,
                          gems: saleGems)
                      : customButton(
                          onTap: () {
                            //充值
                            Get.back(result: 2);
                          },
                          colors: ['#FFDCA4', '#C8984A'],
                          fontWeight: FontWeight.w600,
                          title: '${'GO'.tr} HOT'),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget customButton(
      {required VoidCallback onTap,
      required String title,
      required List<String> colors,
      FontWeight? fontWeight,
      int? gems}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        height: 42.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(42.w / 2),
          color: AppColor.colorsUtil(colors.first),
          gradient: colors.length > 1
              ? LinearGradient(colors: colors.map((e) => AppColor.colorsUtil(e)).toList())
              : null,
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 16.sp, fontWeight: fontWeight),
            ),
            if (gems != null)
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 4),
                child: Image.asset(
                  Assets.assetsImagesSessionGems,
                  width: 16.w,
                ),
              ),
            if (gems != null)
              Text(
                '$gems',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
