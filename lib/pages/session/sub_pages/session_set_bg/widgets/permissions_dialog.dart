import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PermissionsDialogWidget extends StatelessWidget {
  const PermissionsDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.w,
        height: 292.w,
        decoration: const BoxDecoration(
          image: DecorationImage(image: AssetImage(Assets.assetsImagesSessionBottomDialogBg)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            30.verticalSpace,
            Image.asset(
              Assets.assetsImagesSessionSetBgPermission,
              width: 76.w,
            ),
            20.verticalSpace,
            Text(
              "Can't Access Album?".tr,
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w700, color: Colors.white),
            ),
            10.verticalSpace,
            Text(
              "Please grant access in System Setting".tr,
              style: TextStyle(fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.8)),
            ),
            30.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buttonWidget(onTap: () => Get.back(), title: 'Cancel'.tr),
                  16.horizontalSpace,
                  buttonWidget(onTap: () => Get.back(result: 1), title: 'Sure'.tr),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buttonWidget({required VoidCallback onTap, required String title}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        height: 42.w,
        width: 122.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(42.w / 2),
          color: AppColor.colorsUtil('#3D3D43'),
          gradient: title == 'Cancel'.tr
              ? null
              : LinearGradient(
                  colors: [
                    AppColor.colorsUtil('#FFDCA4'),
                    AppColor.colorsUtil('#C8984A'),
                  ],
                ),
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
              fontSize: 16.sp,
              fontWeight: title == 'Cancel'.tr ? FontWeight.w400 : FontWeight.w600),
        ),
      ),
    );
  }
}
