import 'dart:async';
import 'dart:convert';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/logic/session_socket.dart';
import 'package:amor_app/pages/session/logic/session_voice_play.dart';
import 'package:amor_app/pages/session/sub_pages/call/state.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:vibration/vibration.dart';
import 'package:video_player/video_player.dart';

class VoiceCallPageController extends GetxController {
  static VoiceCallPageController get to => Get.find();
  final state = VoiceCallPageControllerState();
  SessionSocket? socket;
  late AudioPlayer player;
  VideoPlayerController? videoPlayController;

  //接听超时 计时器
  Timer? answerTimer;
  //通话时长 计时器
  Timer? callTimer;
  //逐字输出计时器
  Timer? textOutputTimer;
  @override
  onInit() {
    super.onInit();
    state.voiceType = int.parse(Get.parameters['voiceType'] ?? '0');
    state.callType = Get.parameters['callType'] == 'video' ? 1 : 0;
    state.sessionNo = Get.parameters['sessionNo'];
    state.modelId = int.parse(Get.parameters['modelId'] ?? '0');
    state.avatarUrl = Get.parameters['avatarUrl'];
    // '${Get.parameters['avatarUrl']}?x-oss-process=image/quality,q_50/resize,w_200';
    // state.videoUrl = Get.parameters['videoUrl'] ?? '';
    state.bgImgUrl = Get.parameters['bgImgUrl'];
    state.nickname = Get.parameters['nickname'];
    state.defaultVoice = Get.parameters['defaultVoice'];
    player = AudioPlayer();
    player.playerStateStream.listen((playState) {
      if (playState.processingState == ProcessingState.completed) {
        player.stop();
        state.socketText = '';
        startRecordVoice();
      }
    });
    if (state.sessionNo == null) {
      Loading.toast('try again later');
      return;
    }
    state.consume = RemoteConfigUtil.getConfig<int>(key: callGems);
    //开发环境 消耗1宝石
    if (kDebugMode) {
      state.consume = 1;
    }
    if (state.voiceType == 1) {
      //来电震动
      vibration();
      //来电设置超时
      answerTimeout();
    } else {
      //语音通话先检查余额
      if (state.callType == 0) {
        checkBalance();
      } else {
        //视频通话先播放视频
        getModelProfile(modelId: '${state.modelId}');
        //查询用户余额
        getUserGems();
      }
    }
  }

  //来电时 超过10秒未接听则退出
  answerTimeout() {
    answerTimer = Timer(Duration(seconds: 10), () {
      if (state.callState.value == 0 && Get.currentRoute.startsWith(Routes.voiceCall)) {
        Get.back();
      }
    });
  }

  //震动
  vibration() async {
    List<int> patternList = List.generate(1000, (index) => index % 2 == 0 ? 700 : 400);
    if (await Vibration.hasCustomVibrationsSupport() == true) {
      await Vibration.vibrate(pattern: patternList);
    }
  }

  //去电时先检查余额
  checkBalance() async {
    deductGem().then((value) {
      if (value == true) {
        startCall();
      }
    });
  }

  //获取视频配置
  getModelProfile({required String modelId}) async {
    AmorsFeedModel? model;
    SessionApis.getModelProfile(
      modelId: modelId.toString(),
      cacheCallBack: (cache) {
        if (cache != null) {
          model = cache;
          setupVideoPlayer(cache);
        }
      },
    ).then((value) {
      if (model == null) {
        setupVideoPlayer(value);
      }
    });
  }

  //设置视频播放器
  setupVideoPlayer(AmorsFeedModel? detailModel) async {
    state.videoUrl = detailModel?.videoChat?['url'] ?? '';
    // 背景取人物动态gif
    if (detailModel?.videoChat?['gifUrl'] != null && UserService.to.isVip == true) {
      state.bgImgUrl = detailModel?.videoChat?['gifUrl'];
      DefaultCacheManager().getSingleFile(state.bgImgUrl ?? '').then((value) {
        // print('gif 下载完成');
      });
    }
    if (state.videoUrl?.isNotEmpty == true) {
      // print('视频：${state.videoUrl}');
      videoPlayController = VideoPlayerController.networkUrl(
        Uri.parse(state.videoUrl!),
      );
      videoPlayController!.addListener(() {
        // print('加载中:${videoPlayController!.value.isBuffering}');
        // print(videoPlayController!.value.buffered);
        if (state.videoPlayCompleted == false) {
          state.videoLoading.value = videoPlayController!.value.isBuffering;
        }
        if (videoPlayController!.value.isCompleted == true && state.videoPlayCompleted == false) {
          state.videoPlayCompleted = true;
          state.videoLoading.value = false;
          //视频播放完成，会员检查余额 进入语音通话 ，非会员展示提示
          if (UserService.to.isVip == true) {
            checkBalance();
          } else {
            state.callState.value = 6;
          }
        }
      });
      await videoPlayController!.initialize();
      videoPlayController!.play();
      state.callState.value = 1;
    } else {
      Loading.toast('video is empty');
      Get.back();
    }
  }

  startCall() async {
    if (Get.currentRoute.startsWith(Routes.voiceCall) == false) {
      return;
    }
    Vibration.cancel();
    if (UserService.to.isVip == false) {
      backAndShowPurchaseSheet(source: Analytics.tpayvipacceptcall);
      return;
    }
    //接通后开始计时
    state.callTime.value = 1;
    if (callTimer != null) {
      callTimer!.cancel();
      callTimer = null;
    }
    callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      state.callTime.value++;
      if (state.callTime.value >= 3600) {
        Get.back();
      } else if (state.callTime.value % 60 == 0) {
        deductGem();
      }
    });
    connectSocket();
  }

//连接socket
  connectSocket() async {
    socket = SessionSocket();
    await socket!.initSocket(sessionNum: state.sessionNo!, type: 'call');
    state.callState.value = 2;
  }

  //接收消息
  receiveMessage(Map<String, dynamic> socketResult) async {
    if (socketResult['code'] != 0 ||
        socketResult.containsKey('data') == false ||
        socketResult['data']['sessionNo'] != state.sessionNo) {
      state.callState.value = 2;
      return;
    }
    //服务器已收到发送的消息
    if (socketResult['data']['ack'] == 1) {
      return;
    }
    SessionModel model = SessionModel.fromJson(socketResult['data']);
    //结束标志 连续收到多条消息时 只处理第一条消息
    if (socketResult['data']['end'] == true && state.socketText.isEmpty) {
      //判断文本是否为空
      if ((model.content ?? '').isNotEmpty &&
          model.contentType == 'sounds' &&
          model.msgIgnoreErr == false &&
          CommonUtil.extractEmojis(model.content ?? '').isNotEmpty) {
        state.socketText = CommonUtil.extractEmojis(model.content ?? '');
        if (state.socketText.startsWith(',')) {
          state.socketText = state.socketText.substring(1);
        }
        //开始文字转语音
        textToVoice();
      } else {
        //文本为空时跳过 直接开始用户语音输入
        state.callState.value = 2;
      }
    }
  }

  //文字转语音
  textToVoice() async {
    debugPrint('开始文字转语音');
    try {
      final response = await Dio().postUri(
        Uri.parse('${AppService.configModel.voiceUrl}${Api.ttsStream}'),
        data: jsonEncode({
          'content': state.socketText,
          'character': state.defaultVoice,
          'speed': 1,
          'model_id': state.modelId,
          'original_content': state.socketText
        }),
        options: Options(headers: {
          'Content-Type': 'application/json',
          'appSource': 'amor',
          'token': UserService.to.token
        }, responseType: ResponseType.bytes),
      );
      debugPrint('文字转语音结果:${response.statusCode}');
      //开始展示文字动画 如果文字转语音成功就同步播放语音
      if (response.statusCode == 200 && response.data != null) {
        //开始播放语音、展示文字
        player.setAudioSource(BytesSource(response.data)).then((value) {
          player.play();
          textShow();
        });
      } else {
        state.callState.value = 2;
      }
    } catch (e) {
      state.callState.value = 2;
    }
  }

  //展示文字
  textShow() {
    state.callState.value = 5;
    state.showTextIndex = 0;
    textOutputTimer ??= Timer.periodic(
      //展示速度
      Duration(milliseconds: 20),
      (timer) {
        if (state.showTextIndex < state.socketText.length) {
          state.showTextIndex++;
          String showStr = state.socketText.toString().substring(0, state.showTextIndex);
          String lastStr = showStr.substring(showStr.length - 1);
          //是字母 遇到空格才展示，输出单词
          if (lastStr == ' ') {
            state.animateString.value = showStr;
          }
        } else {
          state.animateString.value = state.socketText.toString();
          textOutputTimer!.cancel();
          textOutputTimer = null;
        }
      },
    );
  }

  //开始录音
  startRecordVoice() async {
    state.callState.value = 3;
    state.recognizedWords = '';
    SttUtil().startSpeech(
      textCallBack: (onResult) {
        state.recognizedWords = onResult.recognizedWords;
        if (onResult.finalResult == true) {
          endRecordVoice();
        }
      },
      levelCallBack: (level) {},
      errCallBack: (error) {
        endRecordVoice();
      },
    );
  }

  //结束录音
  endRecordVoice() async {
    debugPrint('stt result:${state.recognizedWords}');
    //识别录音成功
    if (state.recognizedWords.isNotEmpty) {
      //发送消息
      Map<String, dynamic> param = {};
      param['contentType'] = 'text';
      param['sessionNo'] = state.sessionNo;
      param['content'] = state.recognizedWords.toString();
      state.recognizedWords = '';
      if (socket!.sendData(param: param)) {
        state.callState.value = 4;
        return;
      }
    }
    //识别录音失败或发送失败
    state.callState.value = 2;
  }

  //扣除宝石
  Future<bool> deductGem() async {
    if (Get.currentRoute.startsWith(Routes.voiceCall) == false) {
      callTimer?.cancel();
      callTimer = null;
      return false;
    }
    if (state.userGems == null) {
      await getUserGems();
    }
    if ((state.userGems ?? 0) < state.consume) {
      //余额不足
      Loading.toast('Not enough gems, call ended');
      Future.delayed(const Duration(milliseconds: 2000), () {
        backAndShowPurchaseSheet();
      });
      return false;
    }
    SessionApis.voiceCallGems(gems: state.consume).then((value) {
      if (value == true) {
        state.userGems = (state.userGems ?? 0) - state.consume;
      } else {
        Loading.toast('Not enough gems, call ended');
        Future.delayed(const Duration(milliseconds: 2000), () {
          backAndShowPurchaseSheet();
        });
      }
    });
    return true;
  }

  //获取用户余额
  Future getUserGems() async {
    await UserService.to.getUserInfo();
    state.userGems = UserService.to.userinfo.value.gems ?? 0;
  }

  //返回并展示会员开通页面
  backAndShowPurchaseSheet({String? source}) {
    Get.back();
    Future.delayed(Duration(milliseconds: 500), () {
      PurchaseSheet.show(page: ReportUtil.chatwin, modelId: state.modelId, source: source);
    });
  }

  //移除播放器
  removePlayer() async {
    await player.stop();
    player.dispose();
    if (videoPlayController != null) {
      videoPlayController!.dispose();
    }
  }

  @override
  onClose() {
    super.onClose();
    Vibration.cancel();
    SttUtil().stopListening();
    if (socket != null) {
      socket!.disconnect();
    }
    if (answerTimer != null) {
      answerTimer!.cancel();
      answerTimer = null;
    }
    if (callTimer != null) {
      callTimer!.cancel();
      callTimer = null;
    }
    if (textOutputTimer != null) {
      textOutputTimer!.cancel();
      textOutputTimer = null;
    }
    removePlayer();
  }
}
