import 'package:get/get.dart';

class VoiceCallPageControllerState {
  String? sessionNo;
  int? modelId;
  //0:语音 1:视频
  int callType = 0;
  //来电1、去电0
  int voiceType = 0;
  //通话状态  0：未接通、未接听 1:播放视频 2：准备录音 3: 正在录音 4:等待角色回复  5:展示回复的文字和播放语音 6:视频播放结束 非会员展示提示开通会员
  var callState = 0.obs;
  //设备是否支持自定义震动
  bool? vibrationEnable;
  //通话时长
  var callTime = 0.obs;
  //逐一展示文字时的下标
  int showTextIndex = 0;
  //逐一展示文字时的消息ID
  String? showTextMsgId;
  //逐一展示的文字
  var animateString = "".obs;
  //socket收到的一条完整的消息文本
  String socketText = '';
  //角色发音人
  String? defaultVoice;
  //头像
  String? avatarUrl;
  //背景
  String? bgImgUrl;
  //视频
  String? videoUrl;
  //昵称
  String? nickname;
  //语音识别的文本
  String recognizedWords = '';
  //每分钟消耗的宝石数量
  int consume = 300;
  //视频是否播放完成
  bool videoPlayCompleted = false;
  //视频是否在加载
  var videoLoading = false.obs;
  //用户宝石余额
  int? userGems;
}
