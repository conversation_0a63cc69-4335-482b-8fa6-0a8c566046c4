import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:lottie/lottie.dart';
import 'package:video_player/video_player.dart';
import 'controller.dart';

class VoiceCallPage extends GetView<VoiceCallPageController> {
  const VoiceCallPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<VoiceCallPageController>(
        builder: (controller) => Stack(
          children: [
            //背景图片
            Positioned.fill(
              child: Obx(
                () => controller.state.callState.value != 1
                    ? CachedNetworkImage(
                        imageUrl: controller.state.bgImgUrl!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => CachedNetworkImage(imageUrl: chatDefaultBg),
                        errorWidget: (context, url, error) {
                          return CachedNetworkImage(imageUrl: chatDefaultBg);
                        },
                      )
                    : Container(),
              ),
            ),
            //视频
            Align(
              alignment: Alignment.center,
              child: Obx(
                () => controller.state.callState.value == 1
                    ? AspectRatio(
                        aspectRatio: controller.videoPlayController!.value.aspectRatio,
                        child: VideoPlayer(controller.videoPlayController!),
                      )
                    : Container(),
              ),
            ),
            //视频加载
            Align(
              alignment: Alignment.center,
              child: Obx(
                () => controller.state.videoLoading.value == true
                    ? Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          backgroundColor: Colors.transparent,
                        ),
                      )
                    : Container(),
              ),
            ),
            //阴影
            Align(
              alignment: Alignment.topCenter,
              child: Container(
                width: 1.sw,
                height: 170.h + CommonUtil.statusBarHeight(context),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#150E0E'),
                      CommonUtil.colorsUtil('#707070').withValues(alpha: 0),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
            //阴影
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: 1.sw,
                height: 330.h + CommonUtil.bottomBarHeight(),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#707070').withValues(alpha: 0),
                      CommonUtil.colorsUtil('#150E0E'),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
            //头像、倒计时
            Positioned(
              left: 12.w,
              right: 0,
              top: CommonUtil.statusBarHeight(context) + 5.h,
              child: Obx(
                () => controller.state.callState.value != 1 && controller.state.callState.value != 6
                    ? Row(
                        children: [
                          Container(
                            width: 36.w,
                            height: 36.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: GradientBoxBorder(
                                gradient: LinearGradient(
                                  colors: [
                                    CommonUtil.colorsUtil('#C69351'),
                                    CommonUtil.colorsUtil('#EAC282'),
                                  ],
                                ),
                              ),
                            ),
                            // padding: EdgeInsets.all(1.w),
                            child: ClipOval(
                              child: controller.state.avatarUrl != null
                                  ? CachedNetworkImage(
                                      imageUrl: controller.state.avatarUrl!,
                                      alignment: Alignment.topCenter,
                                      fit: BoxFit.cover,
                                    )
                                  : Container(),
                            ),
                          ),
                          8.horizontalSpace,
                          Expanded(
                            child: Text(
                              controller.state.nickname ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w600),
                            ),
                          ),
                          20.horizontalSpace,
                          Obx(
                            () => controller.state.callTime.value > 0
                                ? Container(
                                    width: 54.w,
                                    padding: EdgeInsets.symmetric(vertical: 4.w),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20.r),
                                      color: Colors.white.withValues(alpha: 0.2),
                                    ),
                                    alignment: Alignment.center,
                                    child: Text(
                                      CommonUtil.durationTransformPro(
                                          controller.state.callTime.value),
                                      style:
                                          TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400),
                                    ),
                                  )
                                : Container(),
                          ),
                          12.horizontalSpace,
                        ],
                      )
                    : Align(
                        alignment: Alignment.centerLeft,
                        child: CustomBackButton(
                          img: Assets.assetsImagesNavCloseBlackCircle,
                          width: 26.w,
                        ),
                      ),
              ),
            ),
            //按钮、文本
            Positioned(
              left: 0,
              right: 0,
              bottom: CommonUtil.bottomBarHeight() + 40.h,
              child: Obx(
                () => controller.state.callState.value != 1 && controller.state.callState.value != 6
                    //非视频播放状态
                    ? Column(
                        children: [
                          if (controller.state.callState.value == 5)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(40.w / 2),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                                child: Container(
                                  width: 100.w,
                                  height: 40.w,
                                  decoration: BoxDecoration(
                                    color: CommonUtil.colorsUtil('#333333').withValues(alpha: 0.2),
                                  ),
                                  child: Lottie.asset(
                                    'assets/lottie/voice_call_play.json',
                                    animate: controller.state.callState.value == 5,
                                    fit: BoxFit.contain,
                                    width: 34.w,
                                  ),
                                ),
                              ),
                            ),
                          20.verticalSpaceFromWidth,
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Text(
                              controller.state.animateString.value,
                              style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w400),
                            ),
                          ),
                          Text(
                            controller.state.callState.value == 0 && controller.state.voiceType == 0
                                ? (controller.state.callType == 0
                                    ? 'Waiting response...'.tr
                                    : 'Invites You to video call...'.tr)
                                : '',
                            style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w400),
                          ),
                          24.verticalSpaceFromWidth,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              buildCallButton(
                                icon: Assets.assetsImagesCallDecline,
                                text: controller.state.callState.value == 0
                                    ? 'Decline'.tr
                                    : 'Hang up'.tr,
                                onTap: () {
                                  Get.back();
                                },
                              ),
                              if (controller.state.callState.value != 0 ||
                                  controller.state.voiceType == 1)
                                90.horizontalSpace,
                              //未接听时展示接听按钮
                              if (controller.state.callState.value == 0 &&
                                  controller.state.voiceType == 1)
                                buildCallButton(
                                  icon: Assets.assetsImagesCallAccept,
                                  text: 'Accept'.tr,
                                  onTap: () {
                                    controller.startCall();
                                  },
                                ),
                              //已接通后展示录音状态
                              if (controller.state.callState.value != 0)
                                buildCallButton(
                                  icon: (controller.state.callState.value == 5 ||
                                          controller.state.callState.value == 4)
                                      ? Assets.assetsImagesCallMicroDisable
                                      : controller.state.callState.value == 3
                                          ? 'assets/lottie/voice_call_record.json'
                                          : Assets.assetsImagesCallMicroEnable,
                                  text: (controller.state.callState.value == 5 ||
                                          controller.state.callState.value == 4)
                                      ? 'Microphone is off'.tr
                                      : 'Microphone is on'.tr,
                                  onTap: () {
                                    if (controller.state.callState.value == 2) {
                                      controller.startRecordVoice();
                                      //开始录音
                                    }
                                    if (controller.state.callState.value == 3) {
                                      //结束录音
                                    }
                                  },
                                  onLongPress: () {
                                    if (controller.state.callState.value == 2) {
                                      //开始录音
                                      controller.startRecordVoice();
                                    }
                                  },
                                  onLongPressUp: () {
                                    if (controller.state.callState.value == 3) {
                                      //结束录音
                                      SttUtil().speechToText.cancel();
                                      Future.delayed(Duration(milliseconds: 200),
                                          () => controller.endRecordVoice());
                                    }
                                  },
                                ),
                            ],
                          ),
                        ],
                      )
                    : controller.state.callState.value == 6
                        // 视频播放结束 非会员展示提示开通会员
                        ? Column(
                            children: [
                              Text(
                                'Activate Benefits'.tr,
                                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                              ),
                              8.verticalSpaceFromWidth,
                              Text(
                                'video chat tip'.tr,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                  color: CommonUtil.colorsUtil('#949494'),
                                ),
                              ),
                              24.verticalSpaceFromWidth,
                              InkWell(
                                onTap: () {
                                  controller.backAndShowPurchaseSheet(
                                      source: Analytics.devsucvipvideochat);
                                },
                                child: Container(
                                  width: 234.w,
                                  height: 45.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(45.w / 2),
                                    gradient: LinearGradient(colors: [
                                      AppColor.colorsUtil('#FFDCA4'),
                                      AppColor.colorsUtil('#C8984A')
                                    ]),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    'CONTINUE'.tr,
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                            ],
                          )
                        //视频播放状态
                        : Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildCallButton(
      {required String icon,
      required String text,
      required Function() onTap,
      Function()? onLongPress,
      Function()? onLongPressUp}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      onLongPressUp: onLongPressUp,
      onLongPress: onLongPress,
      child: Column(
        children: [
          if (icon.contains('json') == true)
            Lottie.asset(
              icon,
              animate: controller.state.callState.value == 3,
              fit: BoxFit.contain,
              width: 72.w,
            ),
          if (icon.contains('json') == false) Image.asset(icon, width: 72.w, height: 72.w),
          4.verticalSpaceFromWidth,
          Text(
            text,
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}
