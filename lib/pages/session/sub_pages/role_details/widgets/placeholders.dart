import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';

/// 骨架屏

class RoleDetailPagePlaceholder extends StatelessWidget {
  const RoleDetailPagePlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // width: double.infinity,
      height: 320.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          8.verticalSpace,
          Center(
            child: Container(
              width: 62.w,
              height: 4.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.w / 2),
                color: AppColor.colorsUtil('#3B3C3E'),
              ),
            ),
          ),
          20.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customContainer(width: 100.w, height: 26.w),
                  10.verticalSpace,
                  Row(
                    children: [
                      customContainer(width: 20.w, height: 20.w),
                      8.horizontalSpace,
                      customContainer(width: 138.w, height: 20.w),
                    ],
                  ),
                ],
              ),
              customContainer(width: 105.w, height: 50.w, radius: 14),
            ],
          ),
          30.verticalSpace,
          customContainer(width: 100.w, height: 20.w),
          10.verticalSpace,
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customContainer(width: 20.w, height: 20.w),
              8.horizontalSpace,
              Expanded(
                child: Column(
                  children: [
                    customContainer(width: double.infinity, height: 20.w),
                    10.verticalSpace,
                    customContainer(width: double.infinity, height: 20.w),
                  ],
                ),
              )
            ],
          ),
          30.verticalSpace,
          customContainer(width: 100.w, height: 20.w),
          10.verticalSpace,
          Row(
            children: [
              customContainer(width: 20.w, height: 20.w, radius: 20.w / 2),
              7.horizontalSpace,
              customContainer(width: 73.w, height: 20.w),
            ],
          ),
        ],
      ),
    );
  }

  Widget customContainer({
    required double width,
    required double height,
    double radius = 5,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColor.colorsUtil('313135'),
        borderRadius: BorderRadius.circular(radius.r),
      ),
    );
  }
}
