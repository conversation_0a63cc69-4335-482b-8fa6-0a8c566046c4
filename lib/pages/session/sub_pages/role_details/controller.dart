import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/amors/widgets/clothing_list/controller.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

//滚动停止位置
enum ScrollEndPosition {
  scrollEndPositionTop,
  scrollEndPositionCenter,
  scrollEndPositionBottom,
  scrollEndPositionUnknow,
}

class RoleDetailsController extends GetxController {
  static RoleDetailsController get to => Get.find();

  late String modelId;
  AmorsFeedModel? roleDetailmodel;
  var hasLike = false.obs;
  var likeCount = 0.obs;
  double contentHeight = 0;
  double topHeight = 0;
  double coverImageHeight = 0;
  var scrollToTop = false.obs;
  ScrollEndPosition scrollEndPosition = ScrollEndPosition.scrollEndPositionCenter;
  var showNav = false.obs;
  late ScrollController scrollController;

  @override
  void onInit() {
    super.onInit();
    contentHeight = 1.sh - (CommonUtil.statusBarHeight(Get.context!) + 56);
    topHeight = 200.h + (CommonUtil.statusBarHeight(Get.context!) + 56);
    coverImageHeight = 300.h + topHeight - CommonUtil.bottomBarHeight();
    scrollController = ScrollController(initialScrollOffset: 180.h);
    modelId = Get.parameters["modelId"] ?? '';
    getModelProfile();
  }

  getModelProfile() async {
    AmorsFeedModel? model = await SessionApis.getModelProfile(
      modelId: modelId.toString(),
      cacheCallBack: (cache) {
        setModelProfile(cache: true, model: cache);
      },
    );
    setModelProfile(cache: false, model: model);
  }

  setModelProfile({AmorsFeedModel? model, required bool cache}) async {
    if (model == null) {
      return;
    }
    roleDetailmodel = model;
    likeCount.value = model.likeCount ?? 0;
    hasLike.value = model.hasLike ?? false;
    update();
  }

  //点赞
  doLike() async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    hasLike.toggle();
    if (hasLike.value) {
      likeCount.value += 1;
    } else {
      if (likeCount.value >= 1) {
        likeCount.value -= 1;
      }
    }
    await AmorsApis.doLike(modelId: int.tryParse(modelId));
    //刷新其他页面列表 like数据
    if (Get.isRegistered<InnerPageController>() == true) {
      InnerPageController.to.refreshIndexTabbarList(index: 1);
    }
    if (Get.isRegistered<AmorsPageController>() == true) {
      AmorsPageController.to.likeOperation(modelId: int.tryParse(modelId) ?? 0);
    }
    if (Get.isRegistered<HomeClothListWidgetController>() == true) {
      HomeClothListWidgetController.to.likeOperation(modelId: int.tryParse(modelId) ?? 0);
    }
  }

  ///分享
  share() async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    Map? shareParam = await CommonApis.commonShare(modelId: int.tryParse(modelId));
    if (shareParam != null) {
      Share.share(shareParam['text']);
    }
  }

  void handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      // _scrollDistance += notification.scrollDelta!;
      if (notification.metrics.pixels >
          coverImageHeight - CommonUtil.statusBarHeight(Get.context!) - 60) {
        showNav.value = true;
      } else {
        showNav.value = false;
      }
      scrollEndPosition = ScrollEndPosition.scrollEndPositionUnknow;
    }
    if (notification is ScrollEndNotification) {
      if (notification.metrics.pixels > 380.h &&
          scrollEndPosition != ScrollEndPosition.scrollEndPositionTop) {
        scrollEndPosition = ScrollEndPosition.scrollEndPositionTop;
        scrollToTop.value = false;
        Future.delayed(
          Duration.zero,
          () {
            scrollController.animateTo(
              scrollController.position.maxScrollExtent,
              curve: Curves.ease,
              duration: const Duration(milliseconds: 100),
            );
          },
        );
      }
      if (notification.metrics.pixels < 380.h &&
          notification.metrics.pixels > 80.h &&
          scrollEndPosition != ScrollEndPosition.scrollEndPositionCenter) {
        scrollEndPosition = ScrollEndPosition.scrollEndPositionCenter;
        scrollToTop.value = false;
        Future.delayed(
          Duration.zero,
          () {
            scrollController.animateTo(
              180.h,
              curve: Curves.ease,
              duration: const Duration(milliseconds: 100),
            );
          },
        );
      }
      if (notification.metrics.pixels < 80.h &&
          scrollEndPosition != ScrollEndPosition.scrollEndPositionBottom) {
        scrollEndPosition = ScrollEndPosition.scrollEndPositionBottom;
        scrollToTop.value = true;
        Future.delayed(
          Duration.zero,
          () {
            scrollController.animateTo(
              0,
              curve: Curves.ease,
              duration: const Duration(milliseconds: 100),
            );
          },
        );
      }
    }
  }

  //预览图片
  imageBrowse() {
    // AppSimpleDialog.imageBrowse(
    //   [roleDetailmodel?.cover ?? ''],
    //   0,
    //   scaleCallback: (p0) {
    //     if (p0 == PhotoViewScaleState.zoomedIn) {
    //       ReportUtil.report(page: ReportUtil.pageChBgPage, action: ReportUtil.zoomIn);
    //     }
    //   },
    // );
  }
  //屏蔽、举报作者
  authorReportBlock({required int type}) async {
    bool? result = await Get.dialog(
      CustomDialogWidget(
        title: type == 0 ? "Report this Author?" : "Block this Author",
        subTitle: type == 0 ? 'Inappropriate behaviors.' : "don't wanna see all his content",
        confirmTitle: 'Yes',
        cancelTitle: 'No',
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      bool result = await CommonApis.authorReportBlock(modelId: modelId, type: type);
      if (result) {
        Loading.toast('Thank you for your contribution!');
        //屏蔽作者后刷新首页、inner
        if (type == 1) {
          if (Get.isRegistered<AmorsPageController>() == true) {
            AmorsPageController.to.refreshPage();
          }
          if (Get.isRegistered<InnerPageController>() == true) {
            InnerPageController.to.refreshPage();
          }
        }
        Future.delayed(const Duration(milliseconds: 2000), () {
          Get.until((route) => route.settings.name == Routes.tabs);
        });
      }
    }
  }

  //查看相册图片
  showModelPhoto(int index) async {
    Map imageData = roleDetailmodel!.images!.elementAt(index);
    if (imageData['unlockGems'] > UserService.to.userinfo.value.gems &&
        imageData['unlock'] != true) {
      PurchaseSheet.show(
          buyType: UserService.to.isVip ? 1 : 0,
          page: ReportUtil.chatwin,
          modelId: SessionPageController.to.state.modelId,
          source: Analytics.pageChatwin);
      return;
    }
    //解锁
    if (imageData['unlock'] == true ||
        await SessionApis.unlockPhoto(
                modelId: int.tryParse(modelId) ?? 0, imgId: imageData['id']) ==
            true) {
      Get.dialog(
          InkWell(
              onTap: () => Get.back(),
              child: CachedNetworkImage(
                  imageUrl: imageData['img'],
                  placeholder: (context, url) =>
                      const Center(child: CircularProgressIndicator(color: Colors.white)))),
          barrierColor: Colors.black);
      if (imageData['unlock'] == false) {
        //刷新余额
        UserService.to.getUserInfo();
        //刷新相册
        getModelProfile();
        imageData['unlock'] = true;
        roleDetailmodel!.images!.replaceRange(index, index + 1, [imageData]);
        //刷新聊天页
        if (Get.isRegistered<SessionPageController>()) {
          SessionPageController.to.state.photos.replaceRange(index, index + 1, [imageData]);
        }
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    scrollController.dispose();
  }
}
