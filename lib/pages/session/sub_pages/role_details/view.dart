import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:shimmer/shimmer.dart';
import 'controller.dart';
import 'widgets/placeholders.dart';

class RoleDetailsPage extends StatelessWidget {
  const RoleDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RoleDetailsController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.black,
          extendBodyBehindAppBar: true,
          body: NotificationListener<OverscrollIndicatorNotification>(
            onNotification: (OverscrollIndicatorNotification? overscroll) {
              overscroll!.disallowIndicator();
              return true;
            },
            child: NotificationListener<ScrollNotification>(
              onNotification: (notification) {
                controller.handleScrollNotification(notification);
                return true;
              },
              child: Stack(
                children: [
                  //背景图
                  SizedBox(
                    width: 1.sw,
                    height: 1.sh,
                    child: CachedNetworkImage(
                      imageUrl: controller.roleDetailmodel?.cover ?? '',
                      fit: BoxFit.cover,
                      width: 1.sw,
                      height: controller.coverImageHeight,
                      errorWidget: (context, url, error) {
                        return Container();
                      },
                      placeholder: (context, url) {
                        return Align(
                          alignment: Alignment.topCenter,
                          child: SizedBox(
                            height: controller.topHeight + 100.h,
                            width: 1.sw,
                            child: Container(),
                          ),
                        );
                      },
                    ),
                  ),
                  SingleChildScrollView(
                    controller: controller.scrollController,
                    physics: const ClampingScrollPhysics(),
                    child: SizedBox(
                      // color: Colors.amber,
                      height: controller.coverImageHeight + controller.contentHeight,
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () => controller.imageBrowse(),
                            child: Obx(
                              () => Container(
                                height: controller.showNav.value == true
                                    ? controller.coverImageHeight -
                                        (1.sh - controller.contentHeight)
                                    : controller.coverImageHeight,
                                color: Colors.transparent,
                              ),
                            ),
                          ),
                          Obx(
                            () => ClipRRect(
                              clipBehavior: Clip.antiAlias,
                              borderRadius: BorderRadius.circular(
                                  controller.showNav.value == true ? 0 : 20.r),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                                child: Container(
                                  height: controller.showNav.value == true
                                      ? 1.sh
                                      : controller.contentHeight, // - 20.h,
                                  decoration: BoxDecoration(
                                    color: AppColor.colorsUtil('#2D2D34').withValues(alpha: 0.9),
                                  ),
                                  child: Padding(
                                    padding: REdgeInsets.symmetric(horizontal: 16),
                                    child: controller.roleDetailmodel == null
                                        ? Shimmer.fromColors(
                                            baseColor: AppColor.colorsUtil('#313135'),
                                            highlightColor: AppColor.colorsUtil('#313135')
                                                .withValues(alpha: 0.6),
                                            child: const RoleDetailPagePlaceholder())
                                        : Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              if (controller.showNav.value == true)
                                                SizedBox(
                                                  height: 1.sh - controller.contentHeight,
                                                ),
                                              8.verticalSpace,
                                              Center(
                                                child: Container(
                                                  width: 62.w,
                                                  height: 4.w,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(4.w / 2),
                                                    color: controller.showNav.value == false
                                                        ? AppColor.colorsUtil('#3B3C3E')
                                                        : Colors.transparent,
                                                  ),
                                                ),
                                              ),
                                              14.verticalSpace,
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Text(
                                                          controller.roleDetailmodel?.modelName ??
                                                              "",
                                                          overflow: TextOverflow.ellipsis,
                                                          maxLines: 1,
                                                          style: TextStyle(
                                                              color: AppColor.primaryText,
                                                              fontWeight: FontWeight.w700,
                                                              fontStyle: FontStyle.italic,
                                                              fontSize: 26.sp),
                                                        ),
                                                        7.verticalSpace,
                                                        Row(
                                                          children: [
                                                            Image.asset(
                                                              Assets.assetsImagesRoleDetailCahtted,
                                                              width: 20.w,
                                                              fit: BoxFit.cover,
                                                            ),
                                                            5.horizontalSpace,
                                                            Text(
                                                              'have chatted'.trArgs([
                                                                CommonUtil.numberUnits(controller
                                                                        .roleDetailmodel
                                                                        ?.useCount ??
                                                                    0)
                                                              ]),
                                                              style: TextStyle(
                                                                  color: AppColor.primaryText
                                                                      .withValues(alpha: 0.7),
                                                                  fontSize: 14.sp),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  10.horizontalSpace,
                                                  InkWell(
                                                    onTap: () {
                                                      controller.doLike();
                                                    },
                                                    child: Obx(
                                                      () => Container(
                                                        decoration: BoxDecoration(
                                                          color: controller.hasLike.value == false
                                                              ? Colors.white
                                                              : Colors.white.withValues(alpha: 0.2),
                                                          borderRadius: BorderRadius.circular(14.r),
                                                          gradient: controller.hasLike.value ==
                                                                  false
                                                              ? LinearGradient(colors: [
                                                                  AppColor.colorsUtil('#EAC282'),
                                                                  AppColor.colorsUtil('#C69A51')
                                                                ])
                                                              : null,
                                                        ),
                                                        padding: REdgeInsets.symmetric(
                                                            horizontal: 16, vertical: 14),
                                                        child: Row(
                                                          children: [
                                                            Image.asset(
                                                              controller.hasLike.value
                                                                  ? Assets.assetsImagesAmorLiked
                                                                  : Assets.assetsImagesAmorsLike,
                                                              width: 21.w,
                                                            ),
                                                            8.horizontalSpace,
                                                            Text(
                                                              CommonUtil.numberUnits(
                                                                  controller.likeCount.value),
                                                              style: TextStyle(
                                                                color: AppColor.primaryText,
                                                                fontWeight: FontWeight.w500,
                                                                fontSize: 15.sp,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              if ((controller.roleDetailmodel?.tags ?? [])
                                                  .isNotEmpty)
                                                10.verticalSpaceFromWidth,
                                              if ((controller.roleDetailmodel?.tags ?? [])
                                                  .isNotEmpty)
                                                tags(),
                                              10.verticalSpace,
                                              Divider(
                                                  height: 1,
                                                  color: Colors.white.withValues(alpha: 0.1)),
                                              _title(child: Text("Intro".tr)),
                                              RichText(
                                                text: TextSpan(
                                                  style: TextStyle(
                                                      color: AppColor.primaryText,
                                                      fontSize: 14.sp,
                                                      height: 1.3),
                                                  children: [
                                                    TextSpan(
                                                      text: (controller.roleDetailmodel?.intro ??
                                                                  "")
                                                              .endsWith('\n')
                                                          ? (controller.roleDetailmodel?.intro ??
                                                                  "")
                                                              .replaceRange(
                                                                  (controller.roleDetailmodel
                                                                                  ?.intro ??
                                                                              "")
                                                                          .length -
                                                                      1,
                                                                  (controller.roleDetailmodel
                                                                              ?.intro ??
                                                                          "")
                                                                      .length,
                                                                  '')
                                                          : (controller.roleDetailmodel?.intro ??
                                                              ""),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              if ((controller.roleDetailmodel?.images ?? [])
                                                      .isNotEmpty &&
                                                  AppService.audit == false)
                                                _title(child: Text("Hot photo".tr)),
                                              if ((controller.roleDetailmodel?.images ?? [])
                                                      .isNotEmpty &&
                                                  AppService.audit == false)
                                                GridView.builder(
                                                  shrinkWrap: true,
                                                  physics: const NeverScrollableScrollPhysics(),
                                                  itemCount:
                                                      controller.roleDetailmodel!.images!.length,
                                                  gridDelegate:
                                                      SliverGridDelegateWithFixedCrossAxisCount(
                                                    crossAxisCount: 4,
                                                    mainAxisSpacing: 8.w,
                                                    crossAxisSpacing: 8.w,
                                                  ),
                                                  padding: EdgeInsets.zero,
                                                  itemBuilder: (BuildContext context, int index) {
                                                    return modelPhotosItem(index,
                                                        controller.roleDetailmodel!.images![index]);
                                                  },
                                                ),
                                              _title(child: Text("Author".tr)),
                                              Row(
                                                children: [
                                                  ClipOval(
                                                    child: CachedNetworkImage(
                                                      imageUrl:
                                                          controller.roleDetailmodel?.avatar ?? "",
                                                      fit: BoxFit.cover,
                                                      width: 28.w,
                                                      height: 28.w,
                                                      memCacheWidth: Get.width.toInt(),
                                                      placeholder: (context, url) => ColoredBox(
                                                          color: AppColor.colorsUtil('#303133')),
                                                      errorWidget: (context, url, error) =>
                                                          const ColoredBox(color: AppColor.mainBg),
                                                    ),
                                                  ),
                                                  7.horizontalSpace,
                                                  Text(
                                                    controller.roleDetailmodel?.createUser ?? "",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      letterSpacing: GetPlatform.isIOS ? -0.0 : 0,
                                                      fontSize: 14.sp,
                                                      color: AppColor.primaryText,
                                                    ),
                                                  ),
                                                  4.horizontalSpace,
                                                  if (AppService.audit == true)
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(horizontal: 4.w),
                                                      child: ImageBtn(
                                                          iconSting:
                                                              Assets.assetsImagesAuthorReport,
                                                          onPressed: () {
                                                            controller.authorReportBlock(type: 0);
                                                          },
                                                          width: 20.w,
                                                          height: 20.w),
                                                    ),
                                                  if (AppService.audit == true)
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(horizontal: 4.w),
                                                      child: ImageBtn(
                                                          iconSting: Assets.assetsImagesAuthorBlock,
                                                          onPressed: () {
                                                            controller.authorReportBlock(type: 1);
                                                          },
                                                          width: 20.w,
                                                          height: 20.w),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  //顶部按钮
                  Align(
                    alignment: Alignment.topCenter,
                    child: Container(
                      width: 1.sw,
                      alignment: Alignment.bottomCenter,
                      height: 1.sh - controller.contentHeight,
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomBackButton(
                            back: () {
                              // ReportUtil.report(
                              //     page: ReportUtil.pageChBgPage,
                              //     action: ReportUtil.clickBk,
                              //     model: controller.modelId);
                              Get.back();
                            },
                          ),
                          Padding(
                            padding: AmorTraService.ar
                                ? const EdgeInsets.only(left: 16)
                                : const EdgeInsets.only(right: 16),
                            child: ImageBtn(
                              iconSting: Assets.assetsImagesRoleDetailShare,
                              onPressed: () => controller.share(),
                              width: 24.w,
                              height: 24.w,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  //底部渐变层
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Obx(
                      () => Container(
                        width: 1.sw,
                        height: CommonUtil.bottomBarHeight() + 34.w,
                        decoration: controller.showNav.value == false
                            ? BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [Colors.black.withValues(alpha: 0), Colors.black],
                                ),
                              )
                            : null,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _title({required Widget child}) {
    return Padding(
      padding: REdgeInsets.only(bottom: 8, top: 16),
      child: DefaultTextStyle(
        style: TextStyle(
            fontSize: 16.sp,
            letterSpacing: GetPlatform.isIOS ? -0.0 : 0,
            color: AppColor.primaryText,
            fontWeight: FontWeight.w500),
        child: child,
      ),
    );
  }

  Widget tags() {
    List tags = List.from(RoleDetailsController.to.roleDetailmodel?.tags ?? []);
    if (RoleDetailsController.to.roleDetailmodel?.contentCategory != null) {
      tags.insert(0, RoleDetailsController.to.roleDetailmodel?.contentCategory!.toUpperCase());
    }
    return Wrap(
      spacing: 7.w,
      runSpacing: 7.w,
      children: List.generate(tags.length, (index) {
        return Container(
          height: 14.w,
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          margin: EdgeInsets.only(bottom: 4.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(4.r),
              bottomLeft: Radius.circular(4.r),
              bottomRight: Radius.circular(4.r),
            ),
            border: (tags[index] == 'NSFW' || tags[index] == 'BDSM')
                ? Border.all(color: Colors.white.withValues(alpha: 0.7), width: 0.5)
                : GradientBoxBorder(
                    gradient: LinearGradient(colors: [
                      Colors.white.withValues(alpha: 0.8),
                      Colors.transparent,
                    ]),
                  ),
            color: AppColor.colorsUtil('#00B0F9'),
            gradient: tags[index] == 'BDSM'
                ? null
                : LinearGradient(
                    colors: tags[index] == 'NSFW'
                        ? [
                            AppColor.colorsUtil('#FF68B9'),
                            AppColor.colorsUtil('#FF3D62'),
                          ]
                        : [
                            AppColor.colorsUtil('#3E2D12').withValues(alpha: 0.8),
                            AppColor.colorsUtil('#1A1000').withValues(alpha: 0.6),
                          ]),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                tags[index],
                style: TextStyle(
                    fontSize: 8.sp,
                    color: (tags[index] == 'NSFW' || tags[index] == 'BDSM')
                        ? AppColor.primaryText
                        : AppColor.colorsUtil('#FFDEAE'),
                    height: 9 / 8),
              )
            ],
          ),
        );
      }),
    );
  }

  Widget modelPhotosItem(int index, Map imageData) {
    return InkWell(
      onTap: () {
        RoleDetailsController.to.showModelPhoto(index);
      },
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.all(1),
              clipBehavior: Clip.antiAlias,
              child: CachedNetworkImage(
                imageUrl: imageData['img'],
                fit: BoxFit.cover,
                alignment: Alignment.topCenter,
              ),
            ),
          ),
          if (imageData['unlock'] == false) ...[
            //未解锁阴影
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  color: Colors.black.withValues(alpha: 0.2),
                ),
              ),
            ),

            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    Assets.assetsImagesSessionGems,
                    width: 16.w,
                  ),
                  2.horizontalSpace,
                  Text(
                    '${imageData['unlockGems'] ?? 0}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            )
          ],
        ],
      ),
    );
  }
}
