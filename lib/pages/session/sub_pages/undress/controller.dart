import 'dart:async';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'logic.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class UndressPageController extends GetxController {
  static UndressPageController get to => Get.find();
  var sliderValue = 0.88.obs;
  var stepIndex = 0.obs;
  //用户选择的图片
  String? userSelectedImage;
  //获取结果需要的task ID
  String? undressServiceTaskid;
  //选中的模式
  var selectedMode = 0.obs;
  //循环请求结果计时
  Timer? countDownTimer;
  //结果图片
  String? resultImage;
  //配置信息
  Map? undressConfig;
  List templateConfigList = [];
  //模版滚动控制器
  late ItemScrollController templateScrollCtl;
  //聊天页进入时带入的角色ID
  String? modelId;

  @override
  void onInit() {
    super.onInit();
    modelId = Get.parameters['modelId'];
    templateScrollCtl = ItemScrollController();
    getUnDressConfig();
  }

  //获取配置信息
  getUnDressConfig() async {
    undressConfig = await AmorsApis.getUnDressConfig();
    if (undressConfig != null) {
      Map priceConfig = undressConfig!['price'];
      Map imgConfig = undressConfig!['noDressImg'];
      for (var i = 0; i < priceConfig.keys.length; i++) {
        String name = priceConfig.keys.toList()[i];
        String style = priceConfig.keys.toList()[i];
        if (name.contains('underwear_')) {
          name = style.replaceAll('underwear_', '');
        }
        templateConfigList.add({
          'name': name,
          'style': style,
          'price': priceConfig[style],
          'img': imgConfig[style],
        });
      }
    }
  }

  //选择模式
  selectUndressMode(int index) {
    selectedMode.value = index;
    templateScrollCtl.scrollTo(
        index: index,
        duration: const Duration(milliseconds: 200),
        alignment: 0.4);
  }

  onTapBtn() {
    if (stepIndex.value == 0) {
      selectImage();
      return;
    }
    if (stepIndex.value == 1) {
      startUndress();
      return;
    }
    if (stepIndex.value == 3) {
      stepIndex.value = 0;
      resultImage = null;
      userSelectedImage = null;
      undressServiceTaskid = null;
      selectImage();
    }
  }

  //操作当前角色
  undressCharacter() {
    Analytics().logEvent(Analytics.cundrchar);
    startUndress(isCharacter: true);
  }

  //选择照片
  selectImage() async {
    Analytics().logEvent(Analytics.cunupload);
    String? result = await UndressLogic.selectImage();
    if (result != null && undressConfig != null) {
      userSelectedImage = result;
      //上传图片后展示选择模式
      stepIndex.value = 1;
    } else {}
  }

  //检查用户信息，开始脱衣操作
  startUndress({bool isCharacter = false}) async {
    Analytics().logEvent(Analytics.cungenerate);
    //非会员弹出购买页
    if (UserService.to.isVip == false) {
      PurchaseSheet.show(
          source: isCharacter == true
              ? Analytics.devsucvipundrchar
              : Analytics.devsucvipundrphoto,
          page: Analytics.undress);
      return;
    }
    //获取最新的余额信息
    Loading.show();
    await UserService.to.getUserInfo();
    Loading.dismiss();
    if (templateConfigList.isEmpty) {
      Loading.toast('try again later');
      return;
    }
    //检查余额 使用角色时，价格取第一个
    int price = isCharacter
        ? templateConfigList.first['price']
        : templateConfigList[selectedMode.value]['price'];
    if ((UserService.to.userinfo.value.gems ?? 0) < price) {
      Loading.toast('Insufficient Balance');
      Future.delayed(const Duration(milliseconds: 2000), () {
        PurchaseSheet.show(
            source: Analytics.tvipundress, page: Analytics.undress);
      });
      return;
    }
    startUndressRequest(isCharacter: isCharacter);
  }

  //请求后端脱衣接口
  startUndressRequest({bool isCharacter = false}) async {
    Map? result = await AmorsApis.unDressLaunchTask(
      sourceImage: userSelectedImage,
      modelId: isCharacter ? modelId : null,
      style: templateConfigList[selectedMode.value]['style'],
    );
    if (result?['taskId'] != null) {
      undressServiceTaskid = result?['taskId'];
      //开始loading
      stepIndex.value = 2;
      requestCountDown();
    }
  }

  //请求脱衣结果图片倒计时
  requestCountDown() {
    //30秒倒计时间隔10
    int awaitCountDownNum = 120;
    countDownTimer ??= Timer.periodic(const Duration(seconds: 1), (timer) {
      awaitCountDownNum--;
      if (awaitCountDownNum % 20 == 0) {
        requestImage(showError: awaitCountDownNum == 0);
      }
      if (awaitCountDownNum <= 0 || resultImage != null) {
        removeTimer();
      }
    });
  }

  //请求结果图片
  requestImage({bool showError = false}) async {
    if (undressServiceTaskid != null) {
      Map? result =
          await AmorsApis.unDressTaskStatus(taskId: undressServiceTaskid!);
      if (result?['picUrl'] != null) {
        resultImage = result?['picUrl'];
        DefaultCacheManager().getSingleFile(resultImage!).then((value) async {
          stepIndex.value = 3;
        });
        Analytics().logEvent(Analytics.ungensuc);
        removeTimer();
      } else if (showError == true) {
        Loading.toast('Generation failed.');
        stepIndex.value = 0;
      }
    }
  }

  removeTimer() {
    if (countDownTimer != null) {
      countDownTimer!.cancel();
      countDownTimer = null;
    }
  }

  @override
  void onClose() {
    super.onClose();
    removeTimer();
  }
}
