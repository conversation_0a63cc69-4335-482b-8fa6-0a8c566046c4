import 'dart:convert';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:before_after/before_after.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'controller.dart';

class UndressPage extends StatelessWidget {
  const UndressPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const CustomBackButton(),
        title: Text('Undress'.tr),
      ),
      body: GetBuilder<UndressPageController>(
        builder: (controller) => Obx(
          () => Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.verticalSpaceFromWidth,
                    Container(
                      width: double.infinity,
                      constraints: controller.stepIndex.value == 3
                          ? BoxConstraints(
                              minHeight: 1.sh -
                                  CommonUtil.bottomBarHeight() -
                                  CommonUtil.statusBarHeight(context) -
                                  160.w)
                          : null,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      clipBehavior: Clip.antiAlias,
                      alignment: controller.stepIndex.value == 3 ? Alignment.center : null,
                      child: Stack(
                        children: [
                          if ((controller.stepIndex.value == 0 ||
                                  controller.userSelectedImage == null) &&
                              controller.stepIndex.value != 3)
                            BeforeAfter(
                              value: controller.sliderValue.value,
                              direction: SliderDirection.horizontal,
                              width: double.infinity,
                              after: CachedNetworkImage(
                                fit: BoxFit.cover,
                                imageUrl: undressBefore,
                              ),
                              before: CachedNetworkImage(
                                fit: BoxFit.cover,
                                imageUrl: undressAfter,
                              ),
                              onValueChanged: (value) {
                                if (value < 0.99) {
                                  controller.sliderValue.value = value;
                                }
                              },
                              trackColor: Colors.white,
                              trackWidth: 2,
                              thumbHeight: 30.w,
                              thumbWidth: 30.w,
                              thumbPosition: 0.5,
                              thumbDecoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(Assets.assetsImagesUndressThumbIcon),
                                ),
                              ),
                            ),
                          if ((controller.stepIndex.value == 1 ||
                                  controller.stepIndex.value == 2) &&
                              controller.userSelectedImage != null)
                            Image.memory(
                              base64Decode(controller.userSelectedImage!),
                              width: double.infinity,
                              fit: BoxFit.cover,
                              alignment: Alignment.topCenter,
                            ),
                          if (controller.stepIndex.value == 3)
                            Align(
                              alignment: Alignment.center,
                              child: CachedNetworkImage(
                                fit: BoxFit.cover,
                                imageUrl: controller.resultImage!,
                              ),
                            ),
                        ],
                      ),
                    ),
                    16.verticalSpaceFromWidth,
                    if (controller.stepIndex.value == 0)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'undressTip1'.tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            6.verticalSpaceFromWidth,
                            Text(
                              'undressTip2'.tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            6.verticalSpaceFromWidth,
                            Text(
                              'undressTip3'.tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            14.verticalSpaceFromWidth,
                            Center(
                              child: Text(
                                'undressTip4'.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (controller.stepIndex.value == 1) undressTemplate(),
                    SizedBox(
                        height: CommonUtil.bottomBarHeight() +
                            (controller.stepIndex.value == 0 ? 140.w : 80.w)),
                  ],
                ),
              ),
              //底部阴影
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 1.sw,
                  height: 80.w + CommonUtil.bottomBarHeight(),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        CommonUtil.colorsUtil('#020204').withValues(alpha: 0),
                        CommonUtil.colorsUtil('#020204').withValues(alpha: 0.5),
                        CommonUtil.colorsUtil('#020204').withValues(alpha: 0.8),
                        CommonUtil.colorsUtil('#020204'),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  alignment: Alignment.topCenter,
                ),
              ),
              //底部按钮
              Align(
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () {
                        controller.onTapBtn();
                      },
                      child: Container(
                        height: 45.w,
                        width: 240.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(45.w / 2),
                          gradient: controller.stepIndex.value == 0
                              ? null
                              : LinearGradient(
                                  colors: [
                                    AppColor.colorsUtil('#FFDCA4'),
                                    AppColor.colorsUtil('#C8984A'),
                                  ],
                                ),
                          color: CommonUtil.colorsUtil('#4E4E4E'),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          controller.stepIndex.value == 0
                              ? 'Upload a photo'.tr
                              : controller.stepIndex.value == 3
                                  ? 'Generate another one'.tr
                                  : 'Generate'.tr,
                          style: TextStyle(
                              fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                        ),
                      ),
                    ),
                    if (controller.stepIndex.value == 0) 16.verticalSpaceFromWidth,
                    if (controller.stepIndex.value == 0)
                      //使用当前角色
                      InkWell(
                        onTap: () {
                          controller.undressCharacter();
                        },
                        child: Container(
                          height: 45.w,
                          width: 240.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(45.w / 2),
                            gradient: LinearGradient(
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A'),
                              ],
                            ),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            'Undress the character'.tr,
                            style: TextStyle(
                                fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                    SizedBox(
                      height: 10.w + CommonUtil.bottomBarHeight(),
                    )
                  ],
                ),
              ),
              //生成中 遮罩
              if (controller.stepIndex.value == 2)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.45),
                    ),
                  ),
                ),
              //生成中 动画
              if (controller.stepIndex.value == 2)
                Positioned.fill(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Lottie.asset(
                          'assets/lottie/undress_loading.json',
                          animate: true,
                          repeat: true,
                        ),
                        Text(
                          'AI Generating...'.tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  //模版
  Widget undressTemplate() {
    return SizedBox(
      width: 1.sw,
      height: 116.w,
      child: NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (OverscrollIndicatorNotification? overscroll) {
          overscroll!.disallowIndicator();
          return true;
        },
        child: ScrollablePositionedList.separated(
          physics: const ClampingScrollPhysics(),
          itemScrollController: UndressPageController.to.templateScrollCtl,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          scrollDirection: Axis.horizontal,
          itemCount: UndressPageController.to.templateConfigList.length,
          itemBuilder: (BuildContext context, int index) {
            Map data = UndressPageController.to.templateConfigList[index];
            return InkWell(
              onTap: () {
                UndressPageController.to.selectUndressMode(index);
              },
              child: SizedBox(
                width: 76.w,
                child: Column(
                  children: [
                    Obx(
                      () => Container(
                        width: 76.w,
                        height: 76.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.r),
                          border: UndressPageController.to.selectedMode.value == index
                              ? Border.all(color: CommonUtil.colorsUtil('#EAC282'), width: 2)
                              : null,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: data['img'] ?? '',
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Image.asset(
                                Assets.assetsImagesMessageImagePlaceholder,
                                fit: BoxFit.cover,
                              );
                            },
                            placeholder: (context, url) {
                              return Image.asset(
                                Assets.assetsImagesMessageImagePlaceholder,
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${data['name']}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w600),
                    ),
                    2.verticalSpaceFromWidth,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.assetsImagesSessionGems,
                          width: 15.w,
                        ),
                        4.horizontalSpace,
                        Text(
                          '${data['price']}',
                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 8.horizontalSpace;
          },
        ),
      ),
    );
  }
}
