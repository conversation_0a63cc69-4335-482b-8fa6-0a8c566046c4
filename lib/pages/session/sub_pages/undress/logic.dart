import 'dart:convert';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:image/image.dart' as img;

class UndressLogic {
  //用户选择的图片
  static Uint8List? originalImage;
  //mask图片filename
  static String? curMaskFileName;
  //选择图片
  static Future<String?> selectImage() async {
    String? path = await SelectMediaUtil.selectImage(crop: false);
    if (path == null || path.isEmpty) {
      // Loading.toast('try again later');
      return null;
    }
    Loading.show();
    await Future.delayed(const Duration(milliseconds: 500));
    //转换图片
    Uint8List? imageData = await imageToJpeg(path);
    if (imageData == null) {
      return null;
    }
    originalImage = imageData;
    Loading.dismiss();
    return base64Encode(originalImage!);
    /*
    //上传用户选择的图片 获取mask filename
    Loading.show();
    String? maskFileName = await uploadMaskImage(imageData);
    if (maskFileName == null || maskFileName.isEmpty) {
      Loading.toast('try again later');
      return null;
    }
    curMaskFileName = maskFileName;
    Loading.show();
    //查询mask图片状态
    bool status = await checkMaskImageStatus(fileName: maskFileName);
    if (status == false) {
      Loading.toast('try again later');
      return null;
    }
    //获取mask图片
    Loading.show();
    String? maskImageString = await getMaskImage(fileName: maskFileName);
    if (maskImageString == null || maskImageString.isEmpty) {
      Loading.toast('try again later');
      return null;
    }
    Loading.dismiss();
    return maskImageString;
    */
  }

  // 图片转成jpegr然后再转换成base64
  static Future<Uint8List?> imageToJpeg(String imagePath) async {
    final File imageFile = File(imagePath);
    final Uint8List imageBytes = await imageFile.readAsBytes();
    // print('初始文件大小:${imageBytes.length / (1024)} KB');
    if (imageBytes.length / 1024 > (1024 * 4)) {
      Loading.toast('Try a smaller size pic');
      return null;
    }
    // 将图片转换为Image对象
    final img.Image? image = img.decodeImage(imageBytes);
    if (image == null) {
      Loading.toast('try again later');
      return null;
    }
    if (image.width < 512 || image.height < 512) {
      Loading.toast('Try a larger sized pic');
      return null;
    }
    // 初始质量设为最高
    int quality = 100;
    // compute：在隔离的 isolate 中执行耗时的操作，从而避免阻塞主线程。
    Uint8List jpegBytes = await compute(encodeJpg, [image, quality]);
    // 计算图片大小
    final double sizeInKB = jpegBytes.length / 1024;
    if (sizeInKB > (1024 * 4)) {
      Loading.toast('Try a smaller size pic');
      return null;
    }
    await Future.doWhile(() async {
      // 使用当前质量压缩图片
      jpegBytes = await compute(encodeJpg, [image, quality]);
      // 减少质量
      quality -= 1;
      return jpegBytes.length > 400 * 1024 && quality > 0;
    });
    // print('压缩后文件大小:${jpegBytes.length / (1024)} KB');
    return jpegBytes;
  }

  static Uint8List encodeJpg(List<dynamic> args) {
    img.Image image = args[0];
    int quality = args[1];
    return img.encodeJpg(image, quality: quality);
  }
}
