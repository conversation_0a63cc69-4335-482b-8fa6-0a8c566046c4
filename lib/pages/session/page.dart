import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/widgets/chat_bottom/session_bottom.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:amor_app/pages/session/widgets/session_suspend/session_video_menu.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'controller.dart';
import 'widgets/session_message_content/session_message.dart';
import 'widgets/session_suspend/session_nav.dart';
import 'widgets/session_suspend/session_photos.dart';
import 'widgets/session_suspend/session_tight_astrict.dart';
import 'widgets/session_suspend/session_unlock_anim.dart';

class SessionPage extends StatefulWidget {
  const SessionPage({super.key});
  @override
  State<SessionPage> createState() => _SessionPageState();
}

class _SessionPageState extends State<SessionPage> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 路由订阅
    // AppRouteObserver().routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

// 当前页pop：
  @override
  void didPop() {
    super.didPop();
    // SessionWidgetsController.to.resetPlayIndex();
    // SessionPageController.to.voicePlay.stopPlayVoice();
    // Get.delete<SessionPageController>(force: true);
  }

// 当Pop到该页面时：
  @override
  void didPopNext() {
    super.didPopNext();
    // SessionPageController.to.getSessionConfig();
  }

  @override
  void didPushNext() {
    super.didPushNext();
    // SessionWidgetsController.to.resetPlayIndex();
    // if (Get.isRegistered<SessionPageController>()) {
    //   SessionPageController.to.voicePlay.stopPlayVoice();
    // }
  }

  @override
  void dispose() {
    super.dispose();
    // 取消路由订阅
    // AppRouteObserver().routeObserver.unsubscribe(this);
  }

  @override
  void initState() {
    super.initState();
    // Get.put<SessionPageController>(SessionPageController(), permanent: true);
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: GetBuilder<SessionPageController>(builder: (controller) {
        return Stack(
          children: [
            //背景图片
            Positioned.fill(
              child: Obx(
                () => Container(
                  decoration: BoxDecoration(
                    image: (controller.state.bgImg.value.isNotEmpty)
                        ? DecorationImage(
                            image: CachedNetworkImageProvider(
                              controller.state.bgImg.value,
                            ),
                            fit: BoxFit.cover,
                          )
                        : null,
                    color: AppColor.mainBg,
                  ),
                ),
              ),
            ),
            Scaffold(
              extendBodyBehindAppBar: true,
              // resizeToAvoidBottomInset: !SessionWidgetsController.to.state.editMessage,
              // ignore: prefer_const_constructors
              appBar: SessionAppBarWidget(),
              //  AppBar(
              //   scrolledUnderElevation: 0,
              //   titleSpacing: 0,
              //   backgroundColor: Colors.transparent,
              //   title: InkWell(
              //     onTap: controller.state.modelId == -1
              //         ? null
              //         : () {
              //             // controller.clickAvatar();
              //           },
              //     child: _title(
              //         img: controller.state.sessionAvatarUrl, title: controller.state.sessionTitle),
              //   ),
              //   leading: CustomBackButton(
              //     back: () {
              //       Get.back();
              //     },
              //   ),
              //   actions: _actions(controller),
              // ),
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(
                    width: 1.sw,
                    height: CommonUtil.statusBarHeight(context) + 56 + 32.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColor.mainBg.withValues(alpha: 0.5),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  //聊天列表
                  Column(
                    children: [
                      Obx(
                        () => SizedBox(
                          height: CommonUtil.statusBarHeight(context) +
                              CommonUtil.appBarHeight() +
                              (controller.state.photos.isNotEmpty
                                  ? (controller.state.photosExpanded.value ? 102.w : 24.w)
                                  : 0) +
                              (controller.state.sessionLevelInfo.isNotEmpty ? 36.w : 10.w),
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                            SessionPageController.to.msgTipLogic.hideTip();
                            //隐藏底部菜单
                            if (SessionWidgetsController.to.state.bottomMenuShow.value == true) {
                              SessionWidgetsController.to.setBottomMwnu(clickBlank: true);
                            }
                          },
                          child: ShaderMask(
                            blendMode: BlendMode.dstIn,
                            shaderCallback: (rect) {
                              return const LinearGradient(
                                colors: [Colors.transparent, Colors.white],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                stops: [0, 0.1],
                              ).createShader(rect);
                            },
                            child: EasyRefresh.builder(
                              controller: controller.refreshController,
                              onLoad: () => controller.loadMoreRequest(),
                              footer: RefreshWidget.transparencyFooter(),
                              childBuilder: (context, physics) {
                                return GetBuilder<SessionPageController>(
                                    id: 'listView',
                                    builder: (controller) {
                                      for (var i = 0;
                                          i < controller.state.messageList.length;
                                          i++) {
                                        if (controller.state.messageList[i].msgType == 1) {
                                          SessionWidgetsController.to.state.newMessageFromUser = i;
                                          break;
                                        }
                                      }
                                      //监听键盘
                                      return KeyboardVisibilityBuilder(
                                        builder: (p0, isKeyboardVisible) =>
                                            Get.isRegistered<SessionPageController>() == false
                                                ? Container()
                                                : ListView.builder(
                                                    // shrinkWrap: true,
                                                    physics:
                                                        controller.state.messageList.length > 40
                                                            ? physics
                                                            : const AlwaysScrollableScrollPhysics(),
                                                    controller: controller.scrollController,
                                                    padding: EdgeInsets.fromLTRB(
                                                      12.w,
                                                      20.w,
                                                      12.w,
                                                      10.w +
                                                          (AppService.audit ? 0 : 24.w) +
                                                          62.w +
                                                          //适配有底部安全区域的机型
                                                          (isKeyboardVisible
                                                              ? SessionWidgetsController
                                                                      .to.state.bottomMenuShow.value
                                                                  ? 100
                                                                  : 0
                                                              : CommonUtil.bottomBarHeight() +
                                                                  (SessionWidgetsController.to.state
                                                                          .bottomMenuShow.value
                                                                      ? 113.w
                                                                      : 0)),
                                                    ),
                                                    reverse: true,
                                                    itemCount: controller.state.messageList.length,
                                                    itemBuilder: (BuildContext context, int index) {
                                                      return GetBuilder<SessionPageController>(
                                                        id: controller.state.messageList
                                                                .elementAt(index)
                                                                .msgId ??
                                                            controller.state.messageList
                                                                .elementAt(index)
                                                                .requestId ??
                                                            '',
                                                        builder: (controller) {
                                                          SessionModel model = SessionModel();
                                                          SessionModel lastModel = SessionModel();
                                                          model = controller.state.messageList
                                                              .elementAt(index);
                                                          try {
                                                            lastModel = controller.state.messageList
                                                                .elementAt(index + 1);
                                                            // ignore: empty_catches
                                                          } catch (e) {}
                                                          return SessionMessageWidget(
                                                            chatMessageModel: model,
                                                            listIndex: index,
                                                            lastModel: lastModel,
                                                          );
                                                        },
                                                      );
                                                    },
                                                  ),
                                      );
                                    });
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  //输入框
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: SessionBottom(
                      sessionTitle: controller.state.sessionTitle,
                    ),
                  ),
                ],
              ),
            ),
            //视频菜单
            Obx(
              () => controller.state.showVideoMenu.value == true
                  ? Positioned(
                      right: 13.w,
                      top: CommonUtil.statusBarHeight(context) + 180.h,
                      child: SessionVideoMenuWidget(
                        videoThumbnailUrl:
                            controller.state.videoGifUrl ?? controller.state.sessionAvatarUrl ?? '',
                      ),
                    )
                  : Container(),
            ),
            //相册
            Positioned(
              top: CommonUtil.statusBarHeight(context) + CommonUtil.appBarHeight(),
              left: 0,
              right: 0,
              child: const SessionPhotosWidget(),
            ),
            //锁定
            Obx(() => controller.state.unlock.value == false && controller.state.lockModel == 2
                ? const SessionTightAstrict()
                : Container()),
            //解锁动画
            Obx(() => controller.state.showUnlockAnim.value == true
                ? const SessionUnlockAnim()
                : Container()),
          ],
        );
      }),
    );
  }
}
