import 'dart:async';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/loading/loading.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'session_sqlite.dart';

class SessionSocketRecived {
  ///收到socket消息
  static receiveMessage(Map<String, dynamic> socketResult) async {
    if (Get.isRegistered<SessionPageController>() == false) {
      return;
    }
    SessionPageController controller = Get.find<SessionPageController>();
    if (socketResult['code'] != 0) {
      sendMessageBackError(socketResult: socketResult);
      return;
    }
    if (socketResult.containsKey('data') == false) {
      return;
    }
    //完成任务提示
    if ((socketResult['data'] as Map).containsKey('msgType') &&
        socketResult['data']['msgType'] == 3) {
      controller.taskClaimDialog(data: socketResult['data']);
      return;
    }
    if (socketResult['data']['sessionNo'] != controller.state.sessionNo) {
      return;
    }
    //服务器已收到发送的消息
    if (socketResult['data']['ack'] == 1) {
      controller.state.socketText.clear();
      controller.setAwaitReply(true);
      controller.refreshListView(scrollToBottom: true, updatePage: false);
      //获取刚刚发送的消息的msgid
      for (var i = 0; i < controller.state.messageList.length; i++) {
        SessionModel model = controller.state.messageList.elementAt(i);
        //根据requestId判断是否为刚刚发送的消息  然后填充msgId
        if (model.requestId == socketResult['data']['requestId']) {
          model.msgId = socketResult['data']['msgId'];
          controller.state.messageList.replaceRange(i, i + 1, [model]);
          break;
        }
      }
      return;
    }
    SessionModel model = SessionModel.fromJson(socketResult['data']);
    //图片、语音、视频
    if (model.contentType == 'image' ||
        model.contentType == 'audio' ||
        model.contentType == 'video') {
      //没有正在输出的消息，直接展示
      if (controller.textOutputTimer == null && controller.state.newMsgContent.isEmpty) {
        controller.state.newMsgContent.add(model);
        insertMessage();
      } else {
        //当前正在输出消息，等待结束后再展示
        controller.state.newMsgContent.add(model);
      }
      SessionQlite.insertChatMsg(model: model, sessionNo: controller.state.sessionNo);
      return;
    }
    //结束标志
    if (socketResult['data']['end'] == true) {
      SessionQlite.insertChatMsg(
          model: SessionModel.fromJson(socketResult['data']),
          sessionNo: controller.state.sessionNo);
      if (controller.textOutputTimer == null && controller.state.newMsgContent.isEmpty) {
        controller.state.newMsgContent.add(model);
        addNewMsssage(model);
      } else {
        //输出中 收到第二条消息
        controller.state.newMsgContent.add(model);
      }
      //升级奖励
      if (socketResult['data']['upgrade'] == true &&
          controller.state.sessionLevelInfo['rewards'] != 0) {
        SessionWidgetsController.to.showLevelReward(controller.state.sessionLevelInfo['rewards']);
        controller.refreshSessionLevel();
      } else if (socketResult['data']['appUserChatLevel'] != null) {
        controller.state.sessionLevelInfo.value = socketResult['data']['appUserChatLevel'];
      }

      return;
    }
  }

  //判断是否有缓存消息
  static insertMessage() {
    SessionPageController controller = Get.find<SessionPageController>();
    if (controller.state.newMsgContent.isNotEmpty) {
      if (controller.state.newMsgContent.first.contentType == 'image' ||
          controller.state.newMsgContent.first.contentType == 'audio' ||
          controller.state.newMsgContent.first.contentType == 'video') {
        insertMediaMessage(controller.state.newMsgContent.first);
      } else {
        addNewMsssage(controller.state.newMsgContent.first);
      }
    }
  }

  //加入媒体消息
  static insertMediaMessage(SessionModel imageMessage) {
    SessionPageController controller = Get.find<SessionPageController>();
    Future.delayed(Duration(milliseconds: controller.state.maxServiceWaitSeconds), () {
      HapticFeedback.selectionClick();
      controller.state.messageList.insert(0, imageMessage);
      controller.refreshListView(scrollToBottom: true);
      controller.state.newMsgContent.removeWhere((element) => element.msgId == imageMessage.msgId);
      if (imageMessage.unlock == 0) {
        Analytics().logEvent(Analytics.showLockedpic,
            screen: Analytics.pageChatwin, charID: controller.state.modelId);
      }
    });
  }

  //展示新消息
  static addNewMsssage(SessionModel model) {
    SessionPageController controller = Get.find<SessionPageController>();
    Future.delayed(Duration(milliseconds: controller.state.maxServiceWaitSeconds), () {
      controller.setAwaitReply(false, needRefresh: false);
      model.sendDate = CommonUtil.currentTimeMillis();
      String? content = model.content;
      //重置最后一条消息的评分
      controller.state.msgRating.value = 0;
      controller.state.msgRatingShow.value = true;
      //全是旁白或星号为单数 展示为text消息
      if (CommonUtil.extractEmojis(content ?? '').isEmpty || model.msgIgnoreErr == true) {
        model.contentType = 'text';
      }
      controller.state.showTextMsgId = model.msgId;
      controller.state.animateString.value = '';
      setFirstMsg(model: SessionModel.fromJson(model.toJson()));
      textShow();
      //收到语音消息 重置是否自动请求语音的判断
      if (model.contentType == 'sounds') {
        //上一条语音消息点击了播放 ，这条消息自动转语音
        if (Get.find<SPService>().get(spOntapPlayMsgVoice) == true) {
          //文字转语音
          controller.msgTextToVoice(model: model);
        }
        //设置为未点击状态
        Get.find<SPService>().set(spOntapPlayMsgVoice, false);
      }
    });
    //刷新余额
    UserService.to.getUserInfo();
  }

  //列表插入消息
  static setFirstMsg({required SessionModel model}) {
    SessionPageController controller = Get.find<SessionPageController>();
    controller.state.socketText.clear();
    controller.state.socketText.write(model.content);
    controller.state.showTextIndex = 0;
    model.content =
        (model.content != null && model.content!.length > 1) ? model.content!.substring(0, 1) : '';
    controller.state.messageList.insert(0, model);
    controller.refreshListView(scrollToBottom: true);
    controller.msgTipLogic.hideTip();
  }

  //逐一展示文字
  static textShow() {
    SessionPageController controller = Get.find<SessionPageController>();
    // ignore: prefer_conditional_assignment
    if (controller.textOutputTimer == null) {
      controller.textOutputTimer = Timer.periodic(
        Duration(milliseconds: controller.state.contentDisplayRate),
        (timer) {
          if (controller.state.showTextIndex < controller.state.socketText.length) {
            controller.state.showTextIndex++;
            if (controller.state.showTextIndex % 30 == 0 &&
                controller.state.vibrationEnable == true) {
              HapticFeedback.selectionClick();
            }
            String showStr =
                controller.state.socketText.toString().substring(0, controller.state.showTextIndex);
            String lastStr = showStr.substring(showStr.length - 1);
            controller.state.messageList.first.content = showStr;
            //是字母 遇到空格才展示，输出单词
            if (lastStr == ' ') {
              controller.state.animateString.value = showStr;
            }
          } else {
            //没有可输出的文字
            controller.cancelTimer();
            controller.refreshListView(
                id: controller.state.messageList.first.msgId, scrollToBottom: true);
            controller.state.newMsgContent.removeWhere(
                (element) => element.msgId == controller.state.messageList.first.msgId);
            insertMessage();
          }
        },
      );
    }
  }

  //提示领取宝石
  static showGemsDialog({bool saveFailedMsg = true}) {
    if (Get.isRegistered<SessionPageController>() == false) {
      return;
    }
    //保存发送失败的消息
    if (saveFailedMsg == true) {
      SessionQlite.saveFailMessage(
          model: SessionPageController.to.state.messageList.elementAt(0),
          sessionNo: SessionPageController.to.state.sessionNo);
      SessionQlite.removeChatMsg(index: 0, sessionNo: SessionPageController.to.state.sessionNo);
      SessionWidgetsController.to.chatInputTextFieldCtl.text =
          SessionPageController.to.state.messageList.elementAt(0).content ?? '';
      SessionPageController.to.state.messageList.removeAt(0);
    }
    int delayedSec = 0;
    if (FocusManager.instance.primaryFocus!.hasFocus) {
      delayedSec = 500;
      FocusManager.instance.primaryFocus?.unfocus();
    }
    Future.delayed(
      Duration(milliseconds: delayedSec),
      () async {
        SessionPageController.to.showGemsShort();
      },
    );
  }

  ///消息发送失败或成功后返回错误 （敏感词或者发送太频繁）
  static sendMessageBackError({required Map<String, dynamic> socketResult}) async {
    SessionPageController controller = Get.find<SessionPageController>();
    controller.setAwaitReply(false);
    if (socketResult['code'] == -1) {
      if (socketResult.containsKey('msg') && socketResult['msg'].toString().isNotEmpty) {
        Loading.toast(socketResult['msg']);
      }
      return;
    }
    //直接展示购买页
    if (socketResult['code'] == -7) {
      ReportUtil.reportViews(
          page: ReportUtil.chatwin,
          action: ReportUtil.go,
          value: UserService.to.isVip ? 'gem' : 'hot');

      //弹出购买页
      if (FocusManager.instance.primaryFocus!.hasFocus) {
        FocusManager.instance.primaryFocus?.unfocus();
      }
      Loading.toast('Out of gems tip');
      Future.delayed(
        const Duration(milliseconds: 2000),
        () async {
          Analytics().logEvent(
            Analytics.view,
            sourceEvent: Analytics.toast,
            sourceChar: SessionPageController.to.state.modelId.toString(),
            sourceScreen: Analytics.pageOutofgem,
            screen: Analytics.pageHot,
          );
          PurchaseSheet.show(
              buyType: UserService.to.isVip ? 1 : 0,
              page: ReportUtil.chatwin,
              modelId: SessionPageController.to.state.modelId);
        },
      );

      return;
    }
    if (socketResult['code'] == -3) {
      //余额不足
      SessionPageController.to.showGemsShort();
      return;
    }
    //领取宝石
    if (socketResult['code'] == -6) {
      showGemsDialog();
      return;
    }
    if (socketResult.containsKey('data') == false) {
      return;
    }
    String? requestId = socketResult['data']['requestId'];
    if (requestId == null) {
      return;
    }
    for (var i = 0; i < controller.state.messageList.length; i++) {
      var element = controller.state.messageList.elementAt(i);
      if (element.requestId == requestId) {
        if (socketResult['code'] == -2) {
          //展示错误消息
          element.status = -2;
          element.errorMsg = socketResult['msg'];
          controller.state.messageList.replaceRange(i, i + 1, [element]);
        } else {
          if (socketResult.containsKey('msg') && socketResult['msg'].toString().isNotEmpty) {
            Loading.toast(socketResult['msg']);
          }
          controller.state.messageList.remove(element);
        }
        controller.refreshListView(scrollToBottom: true, updatePage: false);
        return;
      }
    }
  }
}
