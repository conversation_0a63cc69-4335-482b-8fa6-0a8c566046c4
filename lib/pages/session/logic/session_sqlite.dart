import 'dart:convert';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/db_util/session_entity.dart';
import 'package:amor_app/common/utils/db_util/http_entity.dart';
import 'package:amor_app/common/values/values.dart';

class SessionQlite {
  //手动新增聊天数据到数据库
  static insertChatMsg({
    required SessionModel model,
    int? index,
    required String sessionNo,
  }) async {
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = await httpEntity.find(where: {
      'param': jsonEncode({
        'page': 1,
        'pageSize': appPageSize,
        'sessionNo': sessionNo,
      }),
      'url': '${Api.baseUrl}${Api.chatRefresh}'
    });
    if (cacheData.isNotEmpty) {
      Map response = jsonDecode(cacheData.first['response']);
      List dataList = response['data'];
      if (index != null) {
        try {
          dataList.replaceRange(index, index + 1, [model.toJson()]);
        } catch (e) {
          dataList.insert(0, model.toJson());
        }
      } else {
        dataList.insert(0, model.toJson());
      }
      response['data'] = dataList;
      Map<String, dynamic> rafreshData = {
        'param': jsonEncode({
          'page': 1,
          'pageSize': appPageSize,
          'sessionNo': sessionNo,
        }),
        'url': '${Api.baseUrl}${Api.chatRefresh}',
        'response': jsonEncode(response),
        'id': cacheData.first['id'],
      };
      httpEntity.update(rafreshData, cacheData.first['id']);
    }
  }

  //手动删除聊天记录
  static removeChatMsg({
    String? msgId,
    required String sessionNo,
    int? index,
  }) async {
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = await httpEntity.find(where: {
      'param': jsonEncode({
        'page': 1,
        'pageSize': appPageSize,
        'sessionNo': sessionNo,
      }),
      'url': '${Api.baseUrl}${Api.chatRefresh}'
    });
    if (cacheData.isNotEmpty) {
      Map response = jsonDecode(cacheData.first['response']);
      List dataList = response['data'];
      if (index != null) {
        dataList.removeAt(index);
      }
      if (msgId != null) {
        dataList.removeWhere((element) => element['msgId'] == msgId);
      }
      response['data'] = dataList;
      Map<String, dynamic> rafreshData = {
        'param': jsonEncode({
          'page': 1,
          'pageSize': appPageSize,
          'sessionNo': sessionNo,
        }),
        'url': '${Api.baseUrl}${Api.chatRefresh}',
        'response': jsonEncode(response),
        'id': cacheData.first['id'],
      };
      httpEntity.update(rafreshData, cacheData.first['id']);
    }
  }

  //手动更新某一条聊天记录
  static changeChatMsg({
    required SessionModel model,
    required String sessionNo,
  }) async {
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = await httpEntity.find(where: {
      'param': jsonEncode({
        'page': 1,
        'pageSize': appPageSize,
        'sessionNo': sessionNo,
      }),
      'url': '${Api.baseUrl}${Api.chatRefresh}'
    });
    if (cacheData.isNotEmpty) {
      Map response = jsonDecode(cacheData.first['response']);
      List dataList = response['data'];
      for (var i = 0; i < dataList.length; i++) {
        if (dataList[i]['msgId'] == model.msgId) {
          dataList[i] = model.toJson();
        }
      }
      response['data'] = dataList;
      Map<String, dynamic> rafreshData = {
        'param': jsonEncode({
          'page': 1,
          'pageSize': appPageSize,
          'sessionNo': sessionNo,
        }),
        'url': '${Api.baseUrl}${Api.chatRefresh}',
        'response': jsonEncode(response),
        'id': cacheData.first['id'],
      };
      httpEntity.update(rafreshData, cacheData.first['id']);
    }
  }

  //获取缓存的聊天消息列表
  static Future<List<SessionModel>> getChatMsg({required String sessionNo}) async {
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = await httpEntity.find(where: {
      'param': jsonEncode({
        'page': 1,
        'pageSize': appPageSize,
        'sessionNo': sessionNo,
      }),
      'url': '${Api.baseUrl}${Api.chatRefresh}'
    });
    if (cacheData.isNotEmpty) {
      Map response = jsonDecode(cacheData.first['response']);
      try {
        return (response['data'] as List).map((e) {
          return SessionModel.fromJson(e);
        }).toList();
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  //存储发送失败的消息
  static saveFailMessage({required SessionModel model, required String sessionNo}) async {
    SessionEntity chatEntity = SessionEntity.instan();
    //直接全部插入 取最后一条
    Map<String, dynamic> addData = {
      'content': model.content,
      'sessionNo': sessionNo,
    };
    chatEntity.insert(addData);
  }

  //删除发送失败的消息
  static removeFailMessage({required String sessionNo}) async {
    SessionEntity chatEntity = SessionEntity.instan();
    List<Map<String, dynamic>> cacheData = await chatEntity.find(where: {'sessionNo': sessionNo});
    if (cacheData.isNotEmpty) {
      for (var element in cacheData) {
        chatEntity.remove(element['id']);
      }
    }
  }
}
