import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:get/get.dart';

class SessionActionTipLogic {
  late Map msgTipData;
  bool isShowTip = false;
  List tipTextList = [];
  //提示剩余次数
  int tipCount = 0;
  //提示倒计时
  int tipCountDown = 0;
  //正在请求提示
  var loadTip = false.obs;

  showTip() async {
    if (isShowTip == true) {
      hideTip(showSale: false);
      return;
    }
    //展示提示
    if (SessionPageController.to.state.modelId != null) {
      loadTip.value = true;
      //非VIP用户 判断剩余次数
      if (UserService.to.isVip == false) {
        Map? surplus = await SessionApis.chatActionTipsGet();
        tipCount = surplus['count'] ?? 0;
        tipCountDown = surplus['remainResetTime'] ?? 0;
        if (tipCount == 0) {
          SessionPageController.to.state.messageList
              .insert(0, SessionModel(contentType: 'msgTip', actionTips: tipTextList));
          SessionPageController.to.refreshListView(scrollToBottom: true);
          loadTip.value = false;
          isShowTip = true;
          Future.delayed(const Duration(milliseconds: 1500), () => showAd());
          return;
        }
      }
      //先展示骨架屏
      if (isShowTip == true) {
        SessionPageController.to.state.messageList
            .removeWhere((element) => element.contentType == 'msgTip');
      } else {
        isShowTip = true;
      }
      SessionPageController.to.state.messageList
          .insert(0, SessionModel(contentType: 'msgTip', actionTips: []));
      SessionPageController.to.refreshListView(scrollToBottom: true);
      //请求数据
      tipTextList = await SessionApis.chatActionTips(
          modelId: SessionPageController.to.state.modelId!,
          contentCategory: SessionPageController.to.state.contentCategory,
          sessionNo: SessionPageController.to.state.sessionNo);
      loadTip.value = false;
      if (Get.isRegistered<SessionPageController>()) {
        if (tipTextList.isEmpty) {
          hideTip();
          return;
        }
        if (isShowTip == true) {
          SessionPageController.to.state.messageList
              .removeWhere((element) => element.contentType == 'msgTip');
        } else {
          isShowTip = true;
        }
        SessionPageController.to.state.messageList
            .insert(0, SessionModel(contentType: 'msgTip', actionTips: tipTextList));
        SessionPageController.to.refreshListView(scrollToBottom: true);
      }
      ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.idea);
    }
  }

  hideTip({bool showSale = true}) {
    if (isShowTip) {
      isShowTip = false;
      SessionPageController.to.state.messageList
          .removeWhere((element) => element.contentType == 'msgTip');
      SessionPageController.to.refreshListView();
      if (tipCount == 0 && showSale == true && UserService.to.isVip == false) {
        Future.delayed(
            const Duration(milliseconds: 200),
            () => PurchaseSheet.show(
                page: ReportUtil.chatwin, modelId: SessionPageController.to.state.modelId));
      }
    }
  }

  //展示广告
  showAd() {
    if (isShowTip == true) {
      /*
      ApplovinUtil.loadInterstitialAd(
        adUnitId: ApplovinUtil.interstitialAdUnitIdClone,
        callBack: (success) {
          if (success) {
            addTipsCount();
          } else {
            hideTip(showSale: false);
          }
        },
        displayCallBack: () {
          hideTip(showSale: false);
        },
      );
      */
    }
  }

  //广告看完后增加次数
  addTipsCount() async {
    bool result = await SessionApis.chatActionTipsSet();
    if (result == true) {
      showTip();
    }
  }
}
