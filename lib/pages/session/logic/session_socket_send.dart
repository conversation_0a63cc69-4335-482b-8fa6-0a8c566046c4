import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_sqlite.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionSocketSend {
  //发送消息
  //type 1：文字 2：语音
  static bool sendMessage({
    String? content,
    required int type,
    int? duration = 0,
    String? contentAnalysis,
    String? requestId,
    bool resend = false,
  }) {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return false;
    }
    SessionPageController controller = Get.find<SessionPageController>();
    //停止播放
    controller.voicePlay.stopPlayVoice();
    //删除发送失败的消息
    SessionQlite.removeFailMessage(sessionNo: controller.state.sessionNo);
    if (controller.textOutputTimer != null || controller.state.awaitReply.value == true) {
      Loading.toast('Waiting for the reply...');
      return false;
    }
    //文字消息
    if (type == 1 && content == null) {
      return false;
    }
    Map<String, dynamic> param = {};
    param['duration'] = duration;
    param['contentType'] = type == 1 ? 'text' : 'sounds';
    param['sessionNo'] = controller.state.sessionNo;
    if (content != null) {
      param['content'] = content;
    }
    if (requestId != null) {
      param['requestId'] = requestId;
    }
    if (contentAnalysis != null) {
      param['contentAnalysis'] = contentAnalysis;
    }
    //自动翻译
    if (UserService.to.isVip &&
        UserService.to.isAutoTranslation &&
        AmorTraService.locale != AmorTraService.fallbackLocale) {
      param['translate'] = true;
    }
    param['sourceLanguage'] = 'en_US';
    param['targetLanguage'] = (Get.deviceLocale ?? const Locale('en', 'US')).toString();
    //socket发送消息
    bool result = controller.socket.sendData(param: param);
    SessionModel sendMsgModel = SessionModel.fromJson({
      'avatarUrl': UserService.to.userinfo.value.avatarUrl,
      'content': content,
      'contentType': type == 1 ? 'text' : 'sounds',
      'duration': duration,
      'contentAnalysis': contentAnalysis,
      'sendDate': CommonUtil.currentTimeMillis(),
      'msgType': 1,
      // 'status': -2,
      'requestId': requestId,
      'contentCategory': SessionPageController.to.state.contentCategory,
    });
    if (result == false) {
      Loading.toast('Network error, try again later');
      sendMsgModel.status = -2;
      //发送失败时保存数据库，下次再次进入当前会话时填充到输入框
      // ChatQlite.saveFailMessage(model: sendMsgModel, sessionNo: controller.state.sessionNo);
    } else {
      //数据库插入
      SessionQlite.insertChatMsg(model: sendMsgModel, sessionNo: controller.state.sessionNo);
      ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.send, value: content);
      int count = (Get.find<SPService>().get(spChatMessageCount) ?? 0) as int;
      count++;
      Get.find<SPService>().set(spChatMessageCount, count);
      if (count == 10 && Get.find<SPService>().get(spChatShowScore) == null) {
        SessionPageController.to.showAppScore();
      }
      //展示undress按钮和弹窗
      if (SessionWidgetsController.to.state.showUndress == false &&
          count >= controller.state.undressShowCount &&
          AppService.audit == false) {
        SessionWidgetsController.to.state.showUndress = true;
        AppService.sp.set(spChatShowUndress, true);
        controller.refreshListView(updatePage: true);
        SessionWidgetsController.to.showUndress();
      }
    }
    //替换 重发的消息
    if (resend == true) {
      controller.state.messageList.removeWhere((element) {
        return element.requestId == sendMsgModel.requestId;
      });
    }
    controller.state.messageList.insert(0, sendMsgModel);
    controller.refreshListView(scrollToBottom: true);
    return result;
  }
}
