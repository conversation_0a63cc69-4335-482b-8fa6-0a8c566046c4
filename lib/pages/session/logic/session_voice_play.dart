import 'dart:async';
import 'dart:convert' as convert;
import 'dart:io';
import 'dart:typed_data';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/db_util/audio_entity.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_sqlite.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

class SessionVoicePlay {
  late AudioPlayer player;
  //正在进行中的生成语音的消息
  List underWayList = [];
  //重新生成的消息（文字转语音时点击了重新生成，语音生成完成后消息已经销毁了，所以不做处理）
  List regenerateList = [];
  late String chatSessionNo;
  SessionModel chatMsgModel = SessionModel();
  int playIndex = 0;
  //每个语音的时长
  int voiceDuration = 0;
  init() {
    player = AudioPlayer();
    player.playerStateStream.listen((state) {
      // debugPrint(state.processingState.toString());
      if (state.processingState == ProcessingState.completed) {
        player.stop();
      }
    });
    player.playingStream.listen((event) {
      // debugPrint('是否在播放$event');
      if (event == true) {
        SessionWidgetsController.to.state.voicesPlayIndex = playIndex;
      } else {
        SessionWidgetsController.to.state.voicesPlayIndex = -1;
      }
    });

    underWayList.clear();
  }

  dispose() async {
    // debugPrint('移除播放器');
    await player.stop();
    player.dispose();
  }

  Future play({
    required SessionModel model,
    required int index,
    required String sessionNo,
    bool userClick = false,
  }) async {
    if (chatMsgModel.msgId != model.msgId && userClick) {
      await stopPlayVoice();
    }
    chatSessionNo = sessionNo;
    chatMsgModel = model;
    if (userClick) {
      playIndex = index;
    }
    SessionPageController.to.state.messageList.replaceRange(index, index + 1, [model]);
    //点击播放新消息 已接收完所有流数据，但是没有上传完成
    if (model.audioBuffer != null && userClick == true) {
      player.setAudioSource(BytesSource(Uint8List.fromList(model.audioBuffer!))).then((value) {
        if (value != null) {
          voiceDuration = value.inSeconds;
        }
        if (Get.currentRoute.startsWith(Routes.session)) {
          player.play();
        }
      });
      return;
    }
    String? voiceUrl = model.contentAnalysis ?? '';
    if (voiceUrl.isNotEmpty && voiceUrl.contains('http') == true) {
      //已有URL 直接下载播放
      debugPrint('播放url');
      playUrl(url: voiceUrl, model: model, index: index, clickPlay: userClick);
    } else {
      debugPrint('文字转语音');
      //文字转语音
      ttsRequest(model: model, index: index, clickPlay: userClick);
    }
  }

  //播放url
  playUrl(
      {required String url,
      required SessionModel model,
      required int index,
      required bool clickPlay}) async {
    AudioEntity audioEntity = AudioEntity.instan();
    List<Map<String, dynamic>> cacheData = await audioEntity.find(where: {'url': url});
    //已缓存了音频
    if (cacheData.isNotEmpty && cacheData.first['audio'] != null) {
      var audio = cacheData.first['audio'];
      Uint8List? fromDataBuffer;
      if (audio.runtimeType == String) {
        //缓存的是音频文件地址
        try {
          fromDataBuffer = await File(audio).readAsBytes();
        } catch (e) {
          fromDataBuffer = null;
        }
      } else {
        fromDataBuffer = cacheData.first['audio'];
      }
      if (fromDataBuffer != null) {
        // debugPrint('播放缓存的音频');
        player
            .setAudioSource(
          BytesSource(fromDataBuffer),
          preload: true,
        )
            .then((value) {
          if (value != null) {
            voiceDuration = value.inSeconds;
          }
          if (Get.currentRoute.startsWith(Routes.session)) {
            player.play();
          }
        });
        return;
      }
    }

    underWayList.add(model.msgId);
    SessionPageController.to.refreshListView(id: model.msgId);
    //下载和缓存音频
    // debugPrint('开始下载音频');
    ApiRequest.downloadVoice(url).then((value) {
      if (value != null) {
        playBytes(model: model, clickPlay: clickPlay, buffer: value, chatSessionNo: chatSessionNo);
        return;
      }
    });
  }

  //停止播放
  Future stopPlayVoice() async {
    debugPrint('停止播放');
    SessionWidgetsController.to.resetPlayIndex();
    await player.stop();
  }

  //文字转语音请求
  ttsRequest({required SessionModel model, required int index, required bool clickPlay}) async {
    String text = CommonUtil.extractEmojis(model.content ?? '');
    // debugPrint('文字转语音：$text');
    String voice = SessionPageController.to.state.defaultVoice;
    // debugPrint('发音人:$voice');
    if (underWayList.contains(model.msgId)) {
      Loading.toast('Loading...');
      return;
    }
    if (voice.isEmpty || text.isEmpty) {
      Loading.toast('Playback Error');
      return;
    }

    int duration = (CommonUtil.extractEmojis(text).split(' ')).length ~/ 2.5;
    model.duration = duration > 0 ? duration : 1;
    SessionPageController.to.state.messageList.replaceRange(index, index + 1, [model]);
    underWayList.add(model.msgId);
    SessionPageController.to.refreshListView(id: model.msgId);
    //微软文字转语音
    if (voice.contains('azure')) {
      return;
    }
    try {
      Uint8List? stream = await _fetch(
          text, voice, model.content ?? '', SessionPageController.to.state.modelId ?? 0);
      if (Get.isRegistered<SessionPageController>() == false) {
        return;
      }
      if (stream == null) {
        underWayList.removeWhere((element) => element == model.msgId);
        SessionPageController.to.refreshListView(id: model.msgId);
        Loading.toast('Playback Error');
        return;
      }
      int? msgIndex = getMsgIndex(model);
      if (msgIndex != null) {
        if (!regenerateList.contains(model.msgId)) {
          model.audioBuffer = stream;
          SessionPageController.to.state.messageList.replaceRange(msgIndex, msgIndex + 1, [model]);
          playBytes(model: model, clickPlay: clickPlay, chatSessionNo: chatSessionNo);
        }
      }
    } catch (ex) {
      debugPrint('Error: $ex');
      if (Get.isRegistered<SessionPageController>()) {
        underWayList.removeWhere((element) => element == model.msgId);
        SessionPageController.to.refreshListView(id: model.msgId);
      }
    }
  }

  //请求音频流
  Future<Uint8List?> _fetch(String text, String voice, String originalText, int modelId) async {
    // Loading.show();
    // print('${AppService.configModel.voiceUrl}${Api.ttsStream}');
    // print(convert.jsonEncode({
    //   'content': text,
    //   'character': voice,
    //   'speed': 1,
    //   'model_id': modelId,
    //   'original_content': originalText,
    // }));
    // print({'Content-Type': 'application/json', 'appSource': 'amor', 'token': UserService.to.token});
    final response = await Dio().postUri(
      Uri.parse('${AppService.configModel.voiceUrl}${Api.ttsStream}'),
      data: convert.jsonEncode({
        'content': text,
        'character': voice,
        'speed': 1,
        'model_id': modelId,
        'original_content': originalText,
      }),
      options: Options(headers: {
        'Content-Type': 'application/json',
        'appSource': 'amor',
        'token': UserService.to.token
      }, responseType: ResponseType.bytes),
    );
    // Loading.dismiss();
    debugPrint('api:${response.statusCode}');
    // print(response.data);
    // ResponseBody responseBody = response.data;
    if (response.statusCode != 200) {
      return null;
    }
    return response.data;
  }

  //如果播放流错误就切换源
  playBytes({
    Uint8List? buffer,
    required SessionModel model,
    required bool clickPlay,
    required String chatSessionNo,
  }) async {
    if (regenerateList.contains(model.msgId) ||
        Get.isRegistered<SessionPageController>() == false) {
      return;
    }
    int? index = getMsgIndex(model) ?? 0;
    underWayList.removeWhere((element) => element == model.msgId);
    SessionPageController.to.refreshListView(id: model.msgId);
    //切换数据源，重新播放
    if (clickPlay) {
      await player.stop();
      player
          .setAudioSource(
        BytesSource(Uint8List.fromList(buffer ?? model.audioBuffer ?? [])),
        preload: true,
      )
          .then((value) {
        if (value != null) {
          voiceDuration = value.inSeconds;
          playIndex = index;
        }
        if (Get.currentRoute.startsWith(Routes.session)) {
          player.play();
        }
      });
    }
    //buffer是下载的语音数据，播放即可，不用再上传
    if (buffer == null) {
      //音频流接收完成后加入缓存和上传服务器
      uploadVoice(msgId: model.msgId!, voiceData: model.audioBuffer!, chatSessionNo: chatSessionNo);
    } else {
      model.audioBuffer == buffer;
      SessionPageController.to.state.messageList.replaceRange(index, index + 1, [model]);
    }
  }

//上传语音
  uploadVoice(
      {required String msgId,
      String? voiceUrl,
      required Uint8List voiceData,
      required String chatSessionNo}) async {
    debugPrint('开始上传语音');
    String? voiceUrl = await UploadOss.upload(
        fileBytes: voiceData, format: 'mp3', servicePath: ossServicePathChat);
    debugPrint('语音上传完成');
    if (voiceUrl != null) {
      String contentAnalysis = '${AppService.configModel.ossUrl}$voiceUrl';
      List<SessionModel> chatMsgList = [];
      AudioEntity audioEntity = AudioEntity.instan();
      audioEntity.insert({'url': '${AppService.configModel.ossUrl}$voiceUrl', 'audio': voiceData});
      //已退出页面
      if (Get.isRegistered<SessionPageController>() == false) {
        chatMsgList = await SessionQlite.getChatMsg(sessionNo: chatSessionNo);
      } else {
        chatMsgList = SessionPageController.to.state.messageList;
      }
      for (var i = 0; i < chatMsgList.length; i++) {
        SessionModel element = chatMsgList.elementAt(i);
        if (element.msgId == msgId) {
          if (element.contentType != 'sounds') {
            return;
          }
          element.duration ??= voiceDuration;
          // element.contentType = 'sounds';
          element.contentAnalysis = contentAnalysis;
          if (Get.isRegistered<SessionPageController>() == true) {
            SessionPageController.to.state.messageList.replaceRange(i, i + 1, [element]);
          }
          //替换数据库数据
          SessionQlite.insertChatMsg(model: element, index: i, sessionNo: chatSessionNo);
          //上传语音地址到服务器
          SessionApis.setMsgVoice(voice: voiceUrl, msgId: msgId, duration: element.duration);
          return;
        }
      }
    }
  }

  //获取消息下标
  int? getMsgIndex(SessionModel model) {
    for (var i = 0; i < SessionPageController.to.state.messageList.length; i++) {
      SessionModel element = SessionPageController.to.state.messageList.elementAt(i);
      if (element.msgId == model.msgId) {
        return i;
      }
    }
    return null;
  }
}

class BytesSource extends StreamAudioSource {
  final Uint8List _buffer;
  BytesSource(this._buffer) : super(tag: 'BytesSource');
  @override
  Future<StreamAudioResponse> request([int? start, int? end]) async {
    // Returning the stream audio response with the parameters
    return StreamAudioResponse(
      sourceLength: _buffer.length,
      contentLength: (end ?? _buffer.length) - (start ?? 0),
      offset: start ?? 0,
      stream: Stream.fromIterable([_buffer.sublist(start ?? 0, end)]),
      contentType: 'audio/mpeg',
    );
  }
}
