import 'dart:async';
import 'dart:convert';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/http_util/header_builder.dart';
import 'package:amor_app/common/utils/socket_util/socket_util.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_socket_recived.dart';
import 'package:amor_app/pages/session/sub_pages/call/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionSocket {
  WebSocketManager socketManager = WebSocketManager();
  final packageModel =
      PackageInfoModel.fromJson(Get.find<SPService>().get(spAppInfo) as Map<String, dynamic>);
  String sessionNo = '';
  //call/chat
  String callType = '';

  Future<bool> initSocket({required String sessionNum, required String type}) async {
    sessionNo = sessionNum;
    callType = type;
    bool connect = await socketManager.connect(
      getSocketUri(connectionType: 'start'),
      //收到消息
      messageCallback: (String msg) {
        receivedData(msg);
      },
    );

    return connect;
  }

  //获取url
  //start/reconnect
  String getSocketUri({required String connectionType}) {
    Map params = HttpHeaderBuilder.setHeader();
    String header = base64.encode(utf8.encode(jsonEncode(params)));
    return '${Api.modelChatSocketUrl}?authorization=${UserService.to.token}&header=$header&sessionNo=$sessionNo&callType=$callType&connectionType=$connectionType';
  }

  //接收到数据
  receivedData(String value) {
    debugPrint("socket $callType 收到数据 = $value");
    if (Get.isRegistered<SessionPageController>() == false && callType == 'chat') {
      disconnect();
      return;
    }
    if (Get.isRegistered<VoiceCallPageController>() == false && callType == 'call') {
      disconnect();
      return;
    }
    if (value == 'heartbeat') {
      return;
    }
    final data = jsonDecode(value);
    if (data != null && data['code'] != null) {
      if (callType == 'chat') {
        SessionSocketRecived.receiveMessage(data);
      }
      if (callType == 'call') {
        VoiceCallPageController.to.receiveMessage(data);
      }
    }
  }

  //发送消息
  bool sendData({required Map param}) {
    String socketJson = jsonEncode(param);
    if (socketManager.sendMsg(socketJson) == true) {
      debugPrint('socket 发送数据$socketJson');
      return true;
    } else {
      debugPrint('socket 发送数据失败');
      socketManager.reconnect(getSocketUri(connectionType: 'reconnect'));
      return false;
    }
  }

  //手动断开
  disconnect() {
    socketManager.dispose();
  }

  //重连
  reconnection() {
    //已关闭页面就取消重连
    if ((callType == 'chat') && Get.isRegistered<SessionPageController>() == false) {
      return;
    }
    debugPrint('$callType 重连');
    socketManager.reconnect(getSocketUri(connectionType: 'reconnect'));
  }
}
