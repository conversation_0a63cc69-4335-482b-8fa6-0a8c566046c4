import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionWidgetsState {
  //语音播放下标
  final _voicesPlayIndex = 100000000.obs;
  set voicesPlayIndex(value) => _voicesPlayIndex.value = value;
  get voicesPlayIndex => _voicesPlayIndex.value;

  //简介展开、收起
  var introUnfold = false.obs;
  //底部菜单展开、收起
  var bottomMenuShow = false.obs;
  //选中的反馈信息
  var selectedReport = [].obs;
  //用户发送的最新的消息的下标
  int newMessageFromUser = -1;
  //编辑消息的字符串
  String editMessageText = '';
  //是否展示undress按钮
  var showUndress = false;
  final GlobalKey nsfwSwitchWidgetKey = GlobalKey();
  //统计翻译按钮点击（一分钟内连续点击3次）
  DateTime? transLastClickTime;
  int transClickCount = 0;
  //当前角色可以赠送的服装id
  List sessionClothingId = [];
}
