import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionInputBox extends StatelessWidget {
  final String? hintText;
  final int? maxLength;
  final VoidCallback? onEditingComplete;
  final VoidCallback? onTap;
  final ValueChanged<String>? onSubmitted;
  final ValueChanged<String>? onChanged;
  final EdgeInsetsGeometry? contentPadding;
  final TextEditingController? controller;
  final String? errorText;
  final Widget? prefixIcon;
  final TextInputType? keyboardType;
  final BoxConstraints? prefixIconConstraints;
  final BoxDecoration? decoration;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final FocusNode? focusNode;
  final bool? autofocus;
  final SpecialTextSpanBuilder? specialTextSpanBuilder;
  const SessionInputBox({
    super.key,
    this.maxLength = 20,
    this.controller,
    this.errorText,
    this.prefixIcon,
    this.prefixIconConstraints,
    this.onEditingComplete,
    this.onSubmitted,
    this.contentPadding = EdgeInsets.zero,
    this.decoration,
    this.keyboardType,
    this.onChanged,
    this.style,
    this.hintStyle,
    this.focusNode,
    this.hintText,
    this.autofocus = true,
    this.specialTextSpanBuilder,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        minHeight: 42.w,
        maxHeight: 100.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.2),
      ),
      padding: EdgeInsets.only(left: 15.w),
      child: Row(
        children: [
          Expanded(
            child: ExtendedTextField(
              // maxLength: maxLength,
              specialTextSpanBuilder: specialTextSpanBuilder,
              focusNode: focusNode,
              maxLines: null,
              maxLength: null,
              autofocus: autofocus ?? true,
              // cursorColor: AppColor.mainColor,
              controller: controller,
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.send,
              // keyboardType: TextInputType.visiblePassword,
              onEditingComplete: onEditingComplete,
              // autocorrect: false,
              // enableSuggestions: false,
              onSubmitted: onSubmitted,
              // inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r"\n"))],
              onChanged: onChanged,
              onTap: onTap,
              // selectionControls: CupertinoTextSelectionControls().,
              style: style ??
                  TextStyle(
                    fontSize: 15.sp,
                    color: AppColor.primaryText,
                    fontFamily: fontBeVietnamPro,
                    height: 1.3,
                  ),
              // inputFormatters: inputFormatters,
              decoration: InputDecoration(
                // isCollapsed: true, // 取消flutter的奇怪高度
                border: InputBorder.none,
                hintText: hintText,
                prefixIcon: prefixIcon,
                prefixIconConstraints: prefixIconConstraints,
                hintStyle: hintStyle ??
                    TextStyle(
                      fontSize: 13.sp,
                      color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.5),
                      fontWeight: FontWeight.w400,
                      fontFamily: fontBeVietnamPro,
                      height: 1.6,
                    ),
                hintMaxLines: 1,
                counterText: '', //取消文字计数器
                //主要是这个参数设置为true，减少垂直高度消耗，用于编辑框对齐
                isDense: true,
                errorText: errorText,
                // contentPadding: const EdgeInsets.only(bottom: 2, top: 2),
              ),
            ),
          ),
          8.horizontalSpace,
          if (AppService.audit == false)
            GestureDetector(
              onTap: () {
                SessionPageController.to.msgTipLogic.showTip();
              },
              child: Padding(
                padding: REdgeInsets.symmetric(horizontal: 10),
                child: Obx(
                  () => SessionPageController.to.msgTipLogic.loadTip.value == true
                      ? Container(
                          width: 28,
                          height: 28,
                          padding: const EdgeInsets.all(7),
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            backgroundColor: Colors.white.withValues(alpha: 0.3),
                          ),
                        )
                      : Image.asset(
                          Assets.assetsImagesSessionActionTip,
                          width: 28.w,
                        ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
