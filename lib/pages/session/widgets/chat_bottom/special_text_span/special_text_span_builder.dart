import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class GroupChatSpecialTextSpanBuilder extends SpecialTextSpanBuilder {
  final VoidCallback textOnTap;
  GroupChatSpecialTextSpanBuilder({required this.textOnTap});

  @override
  SpecialText? createSpecialText(
    String flag, {
    TextStyle? textStyle,
    SpecialTextGestureTapCallback? onTap,
    int? index,
  }) {
    if (flag == '') {
      return null;
    }
    if (isStart(flag, GroupChatSpecialText.startflag)) {
      return GroupChatSpecialText(
        textStyle!,
        (value) {
          textOnTap();
        },
        start: index! - (GroupChatSpecialText.startflag.length - 1),
      );
    }

    return null;
  }
}

class GroupChatSpecialText extends SpecialText {
  static const String startflag = '@';
  static const String endflag = ' ';
  GroupChatSpecialText(
    TextStyle textStyle,
    SpecialTextGestureTapCallback? onTap, {
    this.start,
  }) : super(
          startflag,
          endflag,
          textStyle,
          onTap: onTap,
        );
  final int? start;
  @override
  InlineSpan finishText() {
    final String text = toString();
    return SpecialTextSpan(
        text: text,
        actualText: text,
        start: start!,
        style: textStyle,
        recognizer: (TapGestureRecognizer()
          ..onTap = () {
            if (onTap != null) {
              onTap!(text);
            }
          }));
  }
}
