import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../controller.dart';
import '../controller/controller.dart';

class ChatBottomMenu extends GetView<SessionWidgetsController> {
  const ChatBottomMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        width: 1.sw,
        height: controller.state.bottomMenuShow.value ? 113.w : 0,
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: REdgeInsets.symmetric(vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                buttonItem(
                  img: Assets.assetsImagesSessionBottomReset,
                  title: 'Reset'.tr,
                  onTap: () => controller.bottomMenuReset(),
                ),
                buttonItem(
                  img: Assets.assetsImagesSessionBottomCard,
                  title: 'Cards'.tr,
                  onTap: () => controller.bottomMenuCards(),
                ),
                buttonItem(
                  img: Assets.assetsImagesSessionBottomShare,
                  title: 'Share'.tr,
                  onTap: () => controller.bottomMenuShare(),
                ),
                buttonItem(
                  img: Assets.assetsImagesSessionBottomReport,
                  title: 'Report'.tr,
                  onTap: () => controller.bottomMenuReport(
                      source: '${SessionPageController.to.state.modelId}',
                      modelName: SessionPageController.to.state.sessionTitle,
                      type: 'direct'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  buttonItem({required String img, required String title, required VoidCallback onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(11.r),
            ),
            alignment: Alignment.center,
            child: Image.asset(img, width: 30.w, height: 30.w),
          ),
          6.verticalSpace,
          Text(
            title,
            style: TextStyle(fontSize: 12.sp, color: Colors.white),
          ),
        ],
      ),
    );
  }
}
