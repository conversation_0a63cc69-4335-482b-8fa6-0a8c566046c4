import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:slide_countdown/slide_countdown.dart';

class SessionGemsShortWidget extends StatelessWidget {
  final SignInitModel model;
  final Function({required int result}) callBack;

  const SessionGemsShortWidget({super.key, required this.model, required this.callBack});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 343.w,
        height: UserService.to.isVip ? 260.w : 523.w,
        decoration: BoxDecoration(
          image: DecorationImage(
            fit: BoxFit.fill,
            image: AssetImage(UserService.to.isVip
                ? Assets.assetsImagesGemsShortBgVip
                : Assets.assetsImagesGemsShortBgUser),
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  30.verticalSpaceFromWidth,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        Assets.assetsImagesGemsShortGemsIcon,
                        width: 22.w,
                      ),
                      5.horizontalSpace,
                      Text(
                        'Out Of Gems!'.tr,
                        style: TextStyle(
                            fontSize: 20.sp,
                            height: 25 / 20,
                            fontWeight: FontWeight.w800,
                            fontStyle: FontStyle.italic),
                      ),
                    ],
                  ),
                  20.verticalSpaceFromWidth,
                  ...List.generate(2, (index) {
                    if (index == 0) {
                      return InkWell(
                        onTap: () {
                          callBack.call(result: 0);
                        },
                        child: amorHotWidget(),
                      );
                    }

                    return InkWell(
                      onTap: () {
                        callBack.call(result: 2);
                      },
                      child: questsToEarnWidget(),
                    );
                  }),
                  if (UserService.to.isVip == false) signWidget(),
                ],
              ),
            ),
            Positioned(
              top: 16.w,
              right: 16.w,
              child: ImageBtn(
                  iconSting: Assets.assetsImagesDialogCloseCircle,
                  onPressed: () => Get.back(),
                  width: 24.w,
                  height: 24.w),
            )
          ],
        ),
      ),
    );
  }

  //开通会员、会员领取宝石
  Widget amorHotWidget() {
    if (UserService.to.isVip) {
      return Container(
        height: 76.w,
        margin: EdgeInsets.only(bottom: 10.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.2),
          ),
          color: AppColor.colorsUtil('#202021'),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Text(
                        'Amor Hot',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: AppColor.colorsUtil('#F0BE72'),
                          fontWeight: FontWeight.w600,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      5.horizontalSpace,
                      Image.asset(
                        Assets.assetsImagesGemsShortArrow,
                        width: 16.w,
                      ),
                    ],
                  ),
                  5.verticalSpaceFromWidth,
                  Row(
                    children: [
                      Text(
                        '+',
                        style: TextStyle(
                            fontSize: 16.sp,
                            height: 20 / 16,
                            color: AppColor.colorsUtil('#F0BE72'),
                            fontWeight: FontWeight.w600),
                      ),
                      2.horizontalSpace,
                      Image.asset(
                        Assets.assetsImagesSessionGems,
                        width: 12.w,
                      ),
                      2.horizontalSpace,
                      Text(
                        NumberFormat('###,000').format(model.signGems),
                        style: TextStyle(
                            fontSize: 15.sp,
                            height: 19 / 15,
                            color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.9),
                            fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            model.status == 0
                ?
                //会员 未领取
                GradientColorBtn(
                    height: 32.w,
                    width: 74.w,
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                    text: 'Claim'.tr,
                    textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
                    onTap: () {
                      callBack.call(result: 0);
                    },
                  )
                :
                //会员 已领取
                Column(
                    children: [
                      8.verticalSpaceFromWidth,
                      //倒计时
                      SlideCountdownSeparated(
                        duration: Duration(seconds: model.countDown ?? 0),
                        separatorPadding: const EdgeInsets.symmetric(horizontal: 1),
                        padding: EdgeInsets.zero,
                        separatorType: SeparatorType.symbol,
                        decoration: const BoxDecoration(color: Colors.transparent),
                        separatorStyle: TextStyle(
                          fontSize: 10.sp,
                          color: AppColor.primaryText,
                        ),
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.white,
                        ),
                        shouldShowDays: (p0) => false,
                        showZeroValue: true,
                        onDone: () {},
                      ),
                      Container(
                        width: 105.w,
                        height: 32.w,
                        decoration: BoxDecoration(
                          color: AppColor.colorsUtil('#2E2B35'),
                          borderRadius: BorderRadius.circular(32.w / 2),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'Refreshing'.tr,
                          style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white.withValues(alpha: 0.3)),
                        ),
                      )
                    ],
                  ),
          ],
        ),
      );
    }
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 16.w, 0),
      margin: EdgeInsets.only(bottom: 10.w),
      height: 140.w,
      decoration: const BoxDecoration(
        image:
            DecorationImage(image: AssetImage(Assets.assetsImagesOutOfGemsHotBg), fit: BoxFit.fill),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Amor Hot Membership'.tr,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: AppColor.colorsUtil('#874600'),
                  fontWeight: FontWeight.w900,
                  fontStyle: FontStyle.italic,
                ),
              ),
              5.horizontalSpace,
              Image.asset(
                Assets.assetsImagesArrowRight,
                width: 16.w,
              ),
            ],
          ),
          8.verticalSpaceFromWidth,
          Text(
            'HotDrop Everyday'.tr,
            style: TextStyle(
              fontSize: 15.sp,
              color: AppColor.colorsUtil('#997544'),
              fontWeight: FontWeight.w600,
            ),
          ),
          2.verticalSpaceFromWidth,
          Row(
            children: [
              Text(
                '+',
                style: TextStyle(
                  fontSize: 30.sp,
                  color: AppColor.colorsUtil('#874600'),
                  fontWeight: FontWeight.w600,
                  height: 38 / 30,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Image.asset(
                  Assets.assetsImagesSessionGems,
                  width: 26.w,
                ),
              ),
              Text(
                NumberFormat('###,000').format(model.vipGems),
                style: TextStyle(
                  fontSize: 30.sp,
                  color: AppColor.colorsUtil('#874600'),
                  height: 38 / 30,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          2.verticalSpaceFromWidth,
          Text(
            'Includes All other Hot features'.tr,
            style: TextStyle(
              fontSize: 11.sp,
              color: AppColor.colorsUtil('#874600').withValues(alpha: 0.5),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  //做任务
  Widget questsToEarnWidget() {
    return Container(
      height: UserService.to.isVip ? 78.w : 55.w,
      margin: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.2),
        ),
        color: AppColor.colorsUtil('#202021'),
      ),
      child: UserService.to.isVip
          ? Padding(
              padding: EdgeInsets.only(left: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Text(
                        'Quests To Earn'.tr,
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w600,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      5.horizontalSpace,
                      Image.asset(
                        Assets.assetsImagesGemsShortArrowWhite,
                        width: 16.w,
                      ),
                    ],
                  ),
                  5.verticalSpaceFromWidth,
                  Row(
                    children: [
                      Text(
                        '+',
                        style: TextStyle(
                            fontSize: 16.sp,
                            height: 20 / 16,
                            color: AppColor.colorsUtil('#F0BE72'),
                            fontWeight: FontWeight.w600),
                      ),
                      2.horizontalSpace,
                      Image.asset(
                        Assets.assetsImagesSessionGems,
                        width: 12.w,
                      ),
                      2.horizontalSpace,
                      Text(
                        NumberFormat('###,000').format(model.questsToEarn),
                        style: TextStyle(
                            fontSize: 15.sp,
                            height: 19 / 15,
                            color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.9),
                            fontWeight: FontWeight.w600),
                      ),
                      5.horizontalSpace,
                      Text(
                        '(${'Check out for more'.tr}...)',
                        style: TextStyle(
                          fontSize: 12.sp,
                          height: 15 / 12,
                          color: Colors.white.withValues(alpha: 0.5),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            )
          : Row(
              children: [
                15.horizontalSpace,
                Text(
                  'Quests To Earn'.tr,
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                5.horizontalSpace,
                Image.asset(
                  Assets.assetsImagesGemsShortArrowWhite,
                  width: 16.w,
                ),
                23.horizontalSpace,
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '+',
                          style: TextStyle(
                              fontSize: 16.sp,
                              height: 20 / 16,
                              color: AppColor.colorsUtil('#F0BE72'),
                              fontWeight: FontWeight.w600),
                        ),
                        2.horizontalSpace,
                        Image.asset(
                          Assets.assetsImagesSessionGems,
                          width: 12.w,
                        ),
                        2.horizontalSpace,
                        Text(
                          NumberFormat('###,000').format(model.questsToEarn),
                          style: TextStyle(
                              fontSize: 15.sp,
                              height: 19 / 15,
                              color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.9),
                              fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                    Text(
                      '(${'Check out for more'.tr}...)',
                      style: TextStyle(
                        fontSize: 8.sp,
                        height: 15 / 12,
                        color: Colors.white.withValues(alpha: 0.5),
                      ),
                    )
                  ],
                ),
              ],
            ),
    );
  }

  //签到
  Widget signWidget() {
    return Container(
      width: double.infinity,
      height: 212.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: AppColor.colorsUtil('#202021'),
        border: Border.all(color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            16.verticalSpaceFromWidth,
            AnimatedOpacity(
              opacity: model.status == 1 ? 0.2 : 1,
              duration: const Duration(milliseconds: 200),
              child: Row(
                children: [
                  Text(
                    '${'Daily Drop'.tr} ',
                    style: TextStyle(
                        fontSize: 15.sp, fontWeight: FontWeight.w600, fontStyle: FontStyle.italic),
                  ),
                  const Text('❤️'),
                  const Spacer(),
                  if (model.status != 1)
                    Text(
                      '${'Refreshing in'.tr} ',
                      style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withValues(alpha: 0.5)),
                    ),
                  if (model.status != 1)
                    SlideCountdownSeparated(
                      duration: Duration(seconds: model.countDown ?? 0),
                      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                      separatorType: SeparatorType.symbol,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      separatorStyle: TextStyle(
                        fontSize: 12.sp,
                        color: AppColor.primaryText,
                        fontWeight: FontWeight.w600,
                      ),
                      separatorPadding: EdgeInsets.symmetric(horizontal: 4.w),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      shouldShowDays: (p0) => false,
                      showZeroValue: true,
                      onDone: () {
                        Get.back();
                      },
                      onChanged: (value) {},
                    ),
                ],
              ),
            ),
            AnimatedOpacity(
              opacity: model.status == 1 ? 0.2 : 1,
              duration: const Duration(milliseconds: 200),
              child: SignBoardWidget(
                items: model.items ?? [],
                signInTimes: model.signInTimes ?? 0,
              ),
            ),
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 200),
              //未签到
              firstChild: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      //签到 直接领取 result = 3
                      callBack.call(result: 3);
                    },
                    child: Container(
                      width: 110.w,
                      height: 32.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32.w / 2),
                        border: Border.all(width: 1, color: AppColor.colorsUtil('#F0BE72')),
                      ),
                      alignment: Alignment.center,
                      child: CommonUtil.gradientText(
                          text: 'Claim',
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
                          textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500)),
                    ),
                  ),
                  /*
                  if (AppService.audit == false)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Text(
                        'or',
                        style: TextStyle(
                          fontSize: 14.sp,
                          height: 18 / 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  if (AppService.audit == false)
                    InkWell(
                      onTap: () {
                        Analytics().logEvent(Analytics.clickDouble,
                            screen: Analytics.pageOutofgem);
                        ApplovinUtil.loadInterstitialAd(
                          adUnitId: ApplovinUtil.interstitialAdUnitIdClone,
                          callBack: (success) {
                            if (success) {
                              callBack.call(result: 4);
                            }
                          },
                        );
                      },
                      child: Container(
                        width: 110.w,
                        height: 32.w,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32.w / 2),
                            gradient: LinearGradient(
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A')
                              ],
                            )),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              Assets.assetsImagesPlayVideoIcon,
                              width: 18.w,
                            ),
                            5.horizontalSpace,
                            Text(
                              'Double',
                              style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ),
                  */
                ],
              ),
              //已签到
              secondChild: Padding(
                padding: EdgeInsets.only(top: 7.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      Assets.assetsImagesGemsShortSelected,
                      width: 20.w,
                    ),
                    5.horizontalSpace,
                    Text(
                      'Come Back Tomorrow!',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              crossFadeState:
                  model.status == 0 ? CrossFadeState.showFirst : CrossFadeState.showSecond,
            ),
          ],
        ),
      ),
    );
  }
}
