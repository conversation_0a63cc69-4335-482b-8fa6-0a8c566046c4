import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetDialogWidget extends StatelessWidget {
  const ResetDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.w,
        height: 292.w,
        margin: REdgeInsets.only(bottom: 100.h),
        padding: REdgeInsets.symmetric(horizontal: 20),
        decoration: const BoxDecoration(
          image: DecorationImage(
            fit: BoxFit.fill,
            image: AssetImage(Assets.assetsImagesSessionBottomDialogBg),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            30.verticalSpace,
            Image.asset(
              Assets.assetsImagesResetDialogIcon,
              width: 76.w,
            ),
            20.verticalSpace,
            Text(
              'Reset?'.tr,
              style: TextStyle(fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w700),
            ),
            10.verticalSpace,
            Text(
              'Clear history and start over?'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: buttonWidget(
                    title: 'Sure'.tr,
                    color: '#795202',
                    onTap: () => Get.back(result: 1),
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                  child: buttonWidget(
                    title: 'Cancel'.tr,
                    color: '#3D3D43',
                    onTap: () => Get.back(result: 0),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  buttonWidget({required String title, required String color, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 42.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(42.w / 2),
          color: AppColor.colorsUtil(color),
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: title == 'Sure'.tr ? FontWeight.w600 : FontWeight.w400),
        ),
      ),
    );
  }
}
