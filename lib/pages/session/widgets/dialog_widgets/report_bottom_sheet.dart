import 'package:amor_app/common/models/session_model/session_report_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ReportBottomSheet extends StatelessWidget {
  final List<ReportInfoModel> reportInfoList;
  const ReportBottomSheet({super.key, required this.reportInfoList});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 160.w),
      decoration: BoxDecoration(
        color: AppColor.colorsUtil('#1F1F21'),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      padding: REdgeInsets.fromLTRB(16, 20, 16, 16 + CommonUtil.bottomBarHeight()),
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: listWidget(),
        ),
      ),
    );
  }

  List<Widget> listWidget() {
    List<Widget> list = [
      Text(
        'Report A Problem'.tr,
        style: TextStyle(fontSize: 17.sp, color: AppColor.primaryText, fontWeight: FontWeight.w600),
      ),
      10.verticalSpace,
      Text(
        'chat report des'.tr,
        style: TextStyle(fontSize: 15.sp, color: AppColor.primaryText, fontWeight: FontWeight.w400),
      ),
      12.verticalSpace,
      reportInputWidget(),
      15.verticalSpace,
      GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Get.back(result: 1);
        },
        child: Container(
          height: 45.w,
          width: 234.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(45.w / 2),
            gradient: LinearGradient(
              colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
            ),
          ),
          alignment: Alignment.center,
          child: Text(
            "Submit".tr,
            style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
          ),
        ),
      ),
    ];
    list.insertAll(4, reportInfoList.map((e) => reportListWidget(e)).toList());
    return list;
  }

  Widget reportListWidget(ReportInfoModel model) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          model.title ?? '',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColor.colorsUtil('#C59021'),
          ),
        ),
        SizedBox(
          width: double.infinity,
          height: 12.w,
        ),
        Obx(
          () => Wrap(
            spacing: 10.w,
            runSpacing: 10.w,
            children: List.generate(
              (model.dataList ?? []).length,
              (index) => InputChip(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                labelPadding: EdgeInsets.zero,
                backgroundColor: AppColor.colorsUtil('#2D2D31'),
                showCheckmark: false,
                selectedColor: AppColor.colorsUtil('#F0BE72'),
                onSelected: (isSelected) {
                  SessionWidgetsController.to.selectedReport(model.dataList!.elementAt(index));
                },
                selected: SessionWidgetsController.to.state.selectedReport
                    .contains(model.dataList?.elementAt(index).id),
                //设置圆角
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                side: const BorderSide(width: 1, color: Colors.transparent),
                label: Text(model.dataList?.elementAt(index).content ?? ''),
                labelStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        12.verticalSpaceFromWidth,
        /*
        ListView.builder(
          itemCount: model.dataList?.length,
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (BuildContext context, int index) {
            return GestureDetector(
              onTap: () {
                SessionWidgetsController.to.selectedReport(model.dataList!.elementAt(index));
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: REdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    // Obx(
                    //   () => Image.asset(
                    //     SessionWidgetsController.to.state.selectedReport
                    //             .contains(model.dataList!.elementAt(index).id)
                    //         ? Assets.assetsImages20ReportSelected
                    //         : Assets.assetsImages20ReportSelect,
                    //     width: 17.w,
                    //   ),
                    // ),
                    10.horizontalSpace,
                    Expanded(
                      child: Text(
                        model.dataList?.elementAt(index).content ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          // height: 1.1,
                          fontWeight: FontWeight.w400,
                          color: AppColor.primaryText.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      */
      ],
    );
  }

  Widget reportInputWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Other problems'.tr,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColor.colorsUtil('#C59021'),
          ),
        ),
        10.verticalSpace,
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: AppColor.colorsUtil('#2D2D31'),
          ),
          padding: REdgeInsets.symmetric(horizontal: 16, vertical: 12),
          alignment: Alignment.center,
          child: CustomTextField.textField(
            textCtl: SessionWidgetsController.to.reportTextFieldCtl,
            hintText: 'Tell us more'.tr,
            textColor: AppColor.primaryText,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
