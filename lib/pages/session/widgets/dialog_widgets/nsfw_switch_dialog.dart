import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/assets.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

final TextStyle textStyle = TextStyle(color: Colors.white.withValues(alpha: 0.8), fontSize: 14.sp);

class ModelSwitchDialogWidget extends StatelessWidget {
  final String contentCategory;

  const ModelSwitchDialogWidget({super.key, required this.contentCategory});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 324.w,
        height: contentCategory == 'SFW' ? 308.w : 370.w,
        margin: EdgeInsets.only(bottom: 80.h),
        decoration: BoxDecoration(
          image: DecorationImage(
            fit: BoxFit.fill,
            image: AssetImage(contentCategory == 'SFW'
                ? Assets.assetsImagesChatSfwDialogBg
                : contentCategory == 'NSFW'
                    ? Assets.assetsImagesChatNsfwDialogBg
                    : Assets.assetsImagesChatBdsmDialogBg),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 38.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            50.verticalSpaceFromWidth,
            Center(
              child: Text(
                '${'Turn On'.tr} $contentCategory?',
                style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
              ),
            ),
            16.verticalSpaceFromWidth,
            Padding(
              padding: EdgeInsets.only(left: 4.w),
              child: Text(
                '👉 ${'Please Be Sure:'.tr}',
                style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: contentCategory == 'NSFW'
                        ? AppColor.colorsUtil('#FF4D83')
                        : contentCategory == 'BDSM'
                            ? AppColor.colorsUtil('#4DBFFF')
                            : AppColor.colorsUtil('#F0BE72')),
              ),
            ),
            8.verticalSpaceFromWidth,
            EasyRichText(
              contentCategory == 'SFW'
                  ? '1. SFW mode costs less Gems'.tr
                  : "1. You're 18+ years old".trArgs(['18+ years old'.tr]),
              defaultStyle: textStyle,
              patternList: contentCategory == 'SFW'
                  ? null
                  : [
                      EasyRichTextPattern(
                        targetString: "18+ years old".tr,
                        hasSpecialCharacters: true,
                        style: TextStyle(
                          color: contentCategory == 'NSFW'
                              ? AppColor.colorsUtil('#D55A9C')
                              : AppColor.colorsUtil('#4DBFFF'),
                          decoration: TextDecoration.underline,
                          decorationColor: contentCategory == 'NSFW'
                              ? AppColor.colorsUtil('#D55A9C')
                              : AppColor.colorsUtil('#4DBFFF'),
                        ),
                      ),
                    ],
            ),
            EasyRichText(
              contentCategory == 'SFW'
                  ? '2. All your NSFW engagement will be erased'.tr
                  : "2. For non-HOT members, mode costs Double Gems"
                      .trArgs([contentCategory, 'Double Gems'.tr]),
              defaultStyle: textStyle,
              patternList: contentCategory == 'SFW'
                  ? null
                  : [
                      EasyRichTextPattern(
                        targetString: "Double Gems".tr,
                        style: TextStyle(
                          color: contentCategory == 'NSFW'
                              ? AppColor.colorsUtil('#D55A9C')
                              : AppColor.colorsUtil('#4DBFFF'),
                          decoration: TextDecoration.underline,
                          decorationColor: contentCategory == 'NSFW'
                              ? AppColor.colorsUtil('#D55A9C')
                              : AppColor.colorsUtil('#4DBFFF'),
                        ),
                      ),
                    ],
            ),
            if (contentCategory != 'SFW')
              Text(
                "3. The more you engage, the more intimate they get.".tr,
                style: textStyle,
              ),
            if (contentCategory != 'SFW')
              Text(
                "4. Convo history will be forgotten.".tr,
                style: textStyle,
              ),
            30.verticalSpaceFromWidth,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buttonWidget(
                  title: 'Pass'.tr,
                  colors: ['#3D3D43'],
                  onTap: () {
                    Get.back();
                  },
                ),
                buttonWidget(
                  title: 'Yes!'.tr,
                  colors: contentCategory == 'SFW'
                      ? ['#EAC282', '#C69A51']
                      : contentCategory == 'NSFW'
                          ? ['#FF68B9', '#FF3D62']
                          : ['#0085BD', '#00B0F9'],
                  onTap: () {
                    Get.back(result: 1);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

Widget buttonWidget({required String title, required List colors, required VoidCallback onTap}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: onTap,
    child: Container(
      width: 118.w,
      height: 42.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(42.w / 2),
        color: AppColor.colorsUtil(colors.first),
        gradient: colors.length > 1
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: colors.map((e) => AppColor.colorsUtil(e)).toList(),
              )
            : null,
      ),
      child: Text(
        title,
        style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: AppColor.primaryText),
      ),
    ),
  );
}
