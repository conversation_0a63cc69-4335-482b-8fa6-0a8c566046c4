import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:before_after/before_after.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'controller.dart';

class UndressDialog {
  static show({required int modelId}) async {
    bool? result = await Get.dialog(
      const UndressDialogWidget(),
      useSafeArea: false,
    );
    Get.delete<UndressDialogWidgetController>(force: true);
    if (result == true) {
      Get.toNamed(Routes.undress, parameters: {'modelId': '$modelId'});
    }
  }
}

class UndressDialogWidget extends StatelessWidget {
  const UndressDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 340.w,
        height: 486.w,
        decoration: BoxDecoration(
          color: CommonUtil.colorsUtil('#1F1F21'),
          border: GradientBoxBorder(
            gradient: LinearGradient(
              colors: [
                CommonUtil.colorsUtil('#F7D4A7'),
                CommonUtil.colorsUtil('#8C5D18'),
                CommonUtil.colorsUtil('#A77D3D'),
                CommonUtil.colorsUtil('#FFFFFF'),
                CommonUtil.colorsUtil('#FCC772'),
                CommonUtil.colorsUtil('#FFFFFF'),
                CommonUtil.colorsUtil('#F0BE72'),
              ],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(20.r),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: GetBuilder(
          init: UndressDialogWidgetController(),
          builder: (controller) => Column(
            mainAxisSize: MainAxisSize.min,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              20.verticalSpaceFromWidth,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Untie'.tr,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontStyle: FontStyle.italic,
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  4.horizontalSpace,
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: CommonUtil.colorsUtil('#FF68B9')),
                    child: Text(
                      'NSFW',
                      style: TextStyle(
                        fontSize: 8.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
              16.verticalSpaceFromWidth,
              Container(
                width: 246.w,
                height: 282.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                ),
                clipBehavior: Clip.antiAlias,
                child: Obx(
                  () => BeforeAfter(
                    value: controller.sliderValue.value,
                    direction: SliderDirection.horizontal,
                    width: double.infinity,
                    height: double.infinity,
                    after: CachedNetworkImage(
                      fit: BoxFit.cover,
                      imageUrl: undressBefore,
                      alignment: Alignment.topCenter,
                    ),
                    before: CachedNetworkImage(
                      fit: BoxFit.cover,
                      imageUrl: undressAfter,
                      alignment: Alignment.topCenter,
                    ),
                    onValueChanged: (value) {
                      if (value < 0.99 && controller.sliderEnable == true) {
                        controller.sliderValue.value = value;
                      }
                    },
                    trackColor: Colors.white,
                    trackWidth: 2,
                    thumbHeight: 30.w,
                    thumbWidth: 30.w,
                    thumbPosition: 0.5,
                    thumbDecoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(Assets.assetsImagesUndressThumbIcon),
                      ),
                    ),
                  ),
                ),
              ),
              14.verticalSpaceFromWidth,
              Text(
                'undress dialog title'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                  color: Colors.white,
                ),
              ),
              16.verticalSpaceFromWidth,
              InkWell(
                onTap: () {
                  Get.back(result: true);
                },
                child: Container(
                  height: 45.w,
                  width: 240.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(45.w / 2),
                    gradient: LinearGradient(
                      colors: [
                        AppColor.colorsUtil('#FFDCA4'),
                        AppColor.colorsUtil('#C8984A'),
                      ],
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    "Try now!".tr,
                    style: TextStyle(
                        fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
