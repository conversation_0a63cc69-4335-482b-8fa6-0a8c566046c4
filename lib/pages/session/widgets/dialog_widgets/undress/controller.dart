import 'package:get/get.dart';

class UndressDialogWidgetController extends GetxController {
  var sliderValue = 1.0.obs;
  bool sliderEnable = true;
  @override
  void onReady() {
    super.onReady();
    showAnimation();
  }

  //动画
  showAnimation() async {
    double count = sliderValue.value;
    while (count > 0) {
      count -= 0.02;
      sliderValue.value = count;
      await Future.delayed(const Duration(milliseconds: 20));
    }
    while (count < 0.45) {
      count += 0.05;
      sliderValue.value = count;
      await Future.delayed(const Duration(milliseconds: 20));
    }
    // sliderValue.value = 0.99;
    // while (count > 0) {
    //   count -= 0.02;
    //   sliderValue.value = count;
    //   await Future.delayed(const Duration(milliseconds: 20));
    // }
    // while (count < 0.99) {
    //   count += 0.05;
    //   sliderValue.value = count;
    //   await Future.delayed(const Duration(milliseconds: 10));
    // }
    // sliderValue.value = 0.99;
    // sliderEnable = false;
  }
}
