import 'dart:ui';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionNsfwSwitch extends GetView<SessionPageController> {
  final Offset position;
  const SessionNsfwSwitch({super.key, required this.position});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: position.dy + 26.w,
          left: position.dx,
          width: 71.w,
          child: Container(
            width: 71.w,
            height: (controller.state.contentCategorys.length * 34.w + 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(13.r),
              color: AppColor.colorsUtil('#343434').withValues(alpha: 0.5),
            ),
            clipBehavior: Clip.antiAlias,
            child: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.w),
                  child: Column(
                    children: List.generate(
                      controller.state.contentCategorys.length,
                      (index) => InkWell(
                        onTap: () {
                          Get.back(result: index);
                        },
                        child: SizedBox(
                          height: 34.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                controller.state.contentCategorys[index]['contentCategory'],
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    height: 15 / 12,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.white,
                                    fontStyle: FontStyle.italic),
                              ),
                              8.horizontalSpace,
                              RotatedBox(
                                quarterTurns: AmorTraService.ar ? 2 : 0,
                                child: Icon(
                                  Icons.keyboard_arrow_right_rounded,
                                  size: 16.w,
                                  color: Colors.white,
                                ),
                              ),
                              4.horizontalSpace,
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
