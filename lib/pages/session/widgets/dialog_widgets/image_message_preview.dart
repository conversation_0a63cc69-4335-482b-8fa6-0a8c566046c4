import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImageMessagePreviewWidget extends StatelessWidget {
  final SessionModel? chatMessageModel;
  final String? picUrl;
  const ImageMessagePreviewWidget({super.key, this.chatMessageModel, this.picUrl});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: InkWell(
            onTap: () => Get.back(),
            child: CachedNetworkImage(
              imageUrl: picUrl ?? chatMessageModel?.content ?? '',
              fit: BoxFit.contain,
            ),
          ),
        ),
        Positioned(
          top: CommonUtil.statusBarHeight(context) + 4,
          left: 4,
          child: const CustomBackButton(),
        ),
        if (chatMessageModel != null &&
            chatMessageModel?.content != SessionPageController.to.state.bgImg.value)
          Positioned(
            bottom: 50.h + CommonUtil.bottomBarHeight(),
            left: 50.w,
            right: 50.w,
            child: GradientColorBtn(
              height: 44.w,
              text: 'Set as Background'.tr,
              onTap: () {
                SessionWidgetsController.to.setBackgroundImage(model: chatMessageModel!);
              },
            ),
          ),
      ],
    );
  }
}
