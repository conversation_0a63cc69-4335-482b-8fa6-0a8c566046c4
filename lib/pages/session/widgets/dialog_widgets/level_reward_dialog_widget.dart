import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';

class SessionLevelRewardWidget extends StatelessWidget {
  final int gems;
  const SessionLevelRewardWidget({super.key, required this.gems});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 140.h + CommonUtil.statusBarHeight(context) + kToolbarHeight,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              width: 96.w,
              height: 54.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.black.withValues(alpha: 0.6),
              ),
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    Assets.assetsImagesSessionGems,
                    width: 24.w,
                  ),
                  2.horizontalSpace,
                  Text(
                    '+$gems',
                    style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
