import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

class LevelDialogWidget extends StatelessWidget {
  final List chatLevelConf;
  const LevelDialogWidget({super.key, required this.chatLevelConf});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: EdgeInsets.only(bottom: 50.w),
        width: 300.w,
        height: 350.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: AppColor.colorsUtil('#1F1F21'),
          image: const DecorationImage(
            fit: BoxFit.cover,
            image: AssetImage(
              Assets.assetsImagesDialogAlphBg,
            ),
          ),
          border: GradientBoxBorder(
              gradient: LinearGradient(
                colors: [
                  CommonUtil.colorsUtil('#F7D4A7'),
                  CommonUtil.colorsUtil('#8C5D18'),
                  CommonUtil.colorsUtil('#A77D3D'),
                  CommonUtil.colorsUtil('#FFFFFF'),
                  CommonUtil.colorsUtil('#FCC772'),
                  CommonUtil.colorsUtil('#FFFFFF'),
                  CommonUtil.colorsUtil('#F0BE72'),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              width: 2),
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            Positioned.fill(
              child: Column(
                children: [
                  24.verticalSpaceFromWidth,
                  Text(
                    'Level Up Intimacy'.tr,
                    style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w700),
                  ),
                  16.verticalSpaceFromWidth,
                  Expanded(
                    child: GridView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 45.w),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 24.w,
                          mainAxisSpacing: 16.w,
                          mainAxisExtent: 120.w),
                      itemCount: chatLevelConf.length,
                      itemBuilder: (BuildContext context, int index) {
                        Map levelInfo = chatLevelConf[index];
                        return Container(
                          height: 120.w,
                          color: Colors.transparent,
                          child: Column(
                            children: [
                              Container(
                                width: 76.w,
                                height: 76.w,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: CommonUtil.colorsUtil('#C58D38', alpha: 0.05),
                                ),
                                padding: EdgeInsets.all(8.w),
                                child: Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: CommonUtil.colorsUtil('#C58D38', alpha: 0.05),
                                  ),
                                  // padding: EdgeInsets.all(14.w),
                                  alignment: Alignment.center,
                                  child: Text(
                                    levelInfo['title'],
                                    style: TextStyle(fontSize: 26.sp),
                                  ),
                                ),
                              ),
                              4.verticalSpaceFromWidth,
                              Text(
                                'Level Reward'.trArgs(['${levelInfo['level']}']),
                                style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
                              ),
                              Spacer(),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(Assets.assetsImagesSessionGems, width: 16.w),
                                  2.horizontalSpace,
                                  Text(
                                    '+ ${levelInfo['reward']}',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w400,
                                      color: CommonUtil.colorsUtil('#EAC282'),
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topRight,
              child: InkWell(
                onTap: () => Get.back(),
                child: Padding(
                  padding: EdgeInsets.all(15.w),
                  child: Image.asset(
                    Assets.assetsImagesCloseWhite,
                    width: 24.w,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
