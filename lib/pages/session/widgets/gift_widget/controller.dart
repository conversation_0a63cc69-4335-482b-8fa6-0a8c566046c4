import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:get/get.dart';

class SeeionGiftWidgetController extends GetxController {
  final int modelId;
  final String sessionNo;
  final List clothingIds;
  SeeionGiftWidgetController(
      {required this.modelId, required this.sessionNo, required this.clothingIds});
  var typeIndex = 0.obs;
  var clothingIndex = 0.obs;
  var giftIndex = 0.obs;
  var clothingConfig = [].obs;
  var giftConfig = [].obs;

  @override
  void onInit() {
    super.onInit();
    //角色不支持赠送服装
    typeIndex.value = clothingIds.isEmpty ? 1 : 0;
    getGiftConfig();
    Analytics().logEvent(Analytics.cgift);
  }

  getGiftConfig() async {
    SessionApis.getClothingConf(cacheCallBack: (List cache) {
      clothingConfig.value = cache.where((value) => clothingIds.contains(value['id'])).toList();
    }).then((value) {
      clothingConfig.value = value.where((value) => clothingIds.contains(value['id'])).toList();
    });
    SessionApis.getGiftConf(cacheCallBack: (List cache) {
      giftConfig.value = cache;
    }).then((value) {
      giftConfig.value = value;
    });
  }

  //赠送
  giftGive() async {
    if (typeIndex.value == 0) {
      Map params = clothingConfig[clothingIndex.value];
      if ((UserService.to.userinfo.value.gems ?? 0) < params['price']) {
        PurchaseSheet.show(
            buyType: 1, page: ReportUtil.chatwin, modelId: modelId, source: Analytics.giftclo);
        return;
      }
      List<SessionModel> result = await SessionApis.clothesGive(
          clothingId: params['id'], modelId: modelId, sessionNo: sessionNo);
      if (result.isNotEmpty) {
        //刷新余额
        UserService.to.getUserInfo();
        Get.back(result: result);
      }
    }
    if (typeIndex.value == 1) {
      Map params = giftConfig[giftIndex.value];
      if ((UserService.to.userinfo.value.gems ?? 0) < params['price']) {
        PurchaseSheet.show(
            buyType: 1, page: ReportUtil.chatwin, modelId: modelId, source: Analytics.gifttoy);
        return;
      }
      List<SessionModel> result = await SessionApis.giftGive(
          clothingId: params['id'], modelId: modelId, sessionNo: sessionNo);
      if (result.isNotEmpty) {
        //刷新余额
        UserService.to.getUserInfo();
        Get.back(result: result);
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
