import 'package:amor_app/common/utils/utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class ClothingLoad extends StatefulWidget {
  final String sessionTitle;
  final String picUrl;
  const ClothingLoad({super.key, required this.sessionTitle, required this.picUrl});

  @override
  State<ClothingLoad> createState() => _ClothingLoadState();
}

class _ClothingLoadState extends State<ClothingLoad> {
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    loadImage();
  }

  loadImage() async {
    Future.delayed(const Duration(seconds: 2), () {
      DefaultCacheManager().getSingleFile(widget.picUrl).then((value) {
        setState(() {
          isLoading = false;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !isLoading,
      child: Stack(
        children: [
          if (isLoading)
            Align(
              alignment: Alignment.center,
              child: Container(
                width: 300.w,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  color: CommonUtil.colorsUtil('#1F1F21'),
                ),
                padding: EdgeInsets.symmetric(horizontal: 34.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    30.verticalSpaceFromWidth,
                    SizedBox(
                      width: 48.w,
                      height: 48.w,
                      child: Lottie.asset('assets/lottie/clothing_load.json', animate: true),
                    ),
                    10.verticalSpaceFromWidth,
                    Text(
                      'clothing load title'.trArgs([widget.sessionTitle, '🎁']),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 17.sp,
                        height: 1.2,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    6.verticalSpaceFromWidth,
                    Text(
                      'clothing load des'.trArgs(['📷']),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withValues(alpha: 0.8)),
                    ),
                    30.verticalSpaceFromWidth,
                    Divider(
                      height: 1,
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.w),
                      child: Text(
                        'clothing load tip'.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withValues(alpha: 0.5),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            Positioned.fill(
              child: InkWell(
                onTap: () => Get.back(),
                child: Center(
                  child: CachedNetworkImage(
                    imageUrl: widget.picUrl,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
