import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'controller.dart';

class SeeionGiftWidget extends StatelessWidget {
  final int modelId;
  final String sessionNo;
  final List clothingIds;
  const SeeionGiftWidget({
    super.key,
    required this.modelId,
    required this.sessionNo,
    required this.clothingIds,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: SeeionGiftWidgetController(
          modelId: modelId, sessionNo: sessionNo, clothingIds: clothingIds),
      builder: (controller) => Container(
        width: 1.sw,
        height: 600.h,
        decoration: BoxDecoration(
          borderRadius:
              BorderRadius.only(topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r)),
          color: CommonUtil.colorsUtil('#242424'),
        ),
        child: Column(
          children: [
            Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => Get.back(),
                      child: Padding(
                        padding: EdgeInsets.all(12.w),
                        child: Image.asset(Assets.assetsImagesNavCloseBlackCircle, width: 24.w),
                      ),
                    ),
                    InkWell(
                      onTap: () => controller.giftGive(),
                      child: Padding(
                        padding: EdgeInsets.all(12.w),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.r),
                            gradient: LinearGradient(
                              colors: [
                                CommonUtil.colorsUtil('#C69351'),
                                CommonUtil.colorsUtil('#EAC282')
                              ],
                            ),
                          ),
                          child: Text(
                            'Send'.tr,
                            style: TextStyle(
                                fontSize: 12.sp, fontWeight: FontWeight.w400, height: 1.2),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Positioned.fill(
                  child: Center(
                    child: Obx(
                      () => CommonUtil.gradientText(
                          text: controller.typeIndex.value == 0
                              ? 'Send a gift and get a picture'.tr
                              : '',
                          colors: [
                            CommonUtil.colorsUtil('#FF68B9'),
                            CommonUtil.colorsUtil('#FF3D62')
                          ],
                          textStyle: TextStyle(fontSize: 10.sp, fontStyle: FontStyle.italic),
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter),
                    ),
                  ),
                ),
              ],
            ),
            Obx(
              () => Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (controller.clothingConfig.isNotEmpty)
                    InkWell(
                      onTap: () => controller.typeIndex.value = 0,
                      child: Padding(
                        padding: EdgeInsets.only(right: 8.w, left: 8.w, bottom: 14.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'clothing'.tr,
                              style: TextStyle(
                                fontSize: controller.typeIndex.value == 0 ? 16.sp : 14.sp,
                                fontWeight: controller.typeIndex.value == 0
                                    ? FontWeight.w700
                                    : FontWeight.w400,
                                color: controller.typeIndex.value == 0
                                    ? CommonUtil.colorsUtil('#EAC282')
                                    : CommonUtil.colorsUtil('#C4C4C4', alpha: 0.8),
                              ),
                            ),
                            4.verticalSpaceFromWidth,
                            Container(
                              width: 14.w,
                              height: 2.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2.w / 2),
                                color: controller.typeIndex.value == 0
                                    ? CommonUtil.colorsUtil('#EAC282')
                                    : Colors.transparent,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  InkWell(
                    onTap: () => controller.typeIndex.value = 1,
                    child: Padding(
                      padding: EdgeInsets.only(right: 8.w, left: 8.w, bottom: 14.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'toys'.tr,
                            style: TextStyle(
                              fontSize: controller.typeIndex.value == 1 ? 16.sp : 14.sp,
                              fontWeight: controller.typeIndex.value == 1
                                  ? FontWeight.w700
                                  : FontWeight.w400,
                              color: controller.typeIndex.value == 1
                                  ? CommonUtil.colorsUtil('#EAC282')
                                  : CommonUtil.colorsUtil('#C4C4C4', alpha: 0.8),
                            ),
                          ),
                          4.verticalSpaceFromWidth,
                          Container(
                            width: 14.w,
                            height: 2.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2.w / 2),
                              color: controller.typeIndex.value == 1
                                  ? CommonUtil.colorsUtil('#EAC282')
                                  : Colors.transparent,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: Obx(
                  () => LazyLoadIndexedStack(
                    index: controller.typeIndex.value,
                    children: [
                      GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 7.w,
                            mainAxisSpacing: 7.w,
                            mainAxisExtent: 230.w),
                        itemCount: controller.clothingConfig.length,
                        shrinkWrap: true,
                        padding: EdgeInsets.only(bottom: CommonUtil.bottomBarHeight() + 10.h),
                        itemBuilder: (BuildContext context, int index) {
                          return Obx(
                            () => _buildGiftItem(
                              index: index,
                              data: controller.clothingConfig[index],
                              selected: controller.clothingIndex.value == index,
                              onTap: () => controller.clothingIndex.value = index,
                            ),
                          );
                        },
                      ),
                      GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 7.w,
                            mainAxisSpacing: 7.w,
                            mainAxisExtent: 230.w),
                        padding: EdgeInsets.only(bottom: CommonUtil.bottomBarHeight() + 10.h),
                        itemCount: controller.giftConfig.length,
                        shrinkWrap: true,
                        itemBuilder: (BuildContext context, int index) {
                          return Obx(
                            () => _buildGiftItem(
                              index: index,
                              data: controller.giftConfig[index],
                              selected: controller.giftIndex.value == index,
                              onTap: () => controller.giftIndex.value = index,
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildGiftItem(
      {required Map data, required int index, required bool selected, required Function() onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: selected ? CommonUtil.colorsUtil('#EAC282') : Colors.transparent,
                strokeAlign: BorderSide.strokeAlignInside)),
        clipBehavior: Clip.hardEdge,
        child: Stack(
          children: [
            Positioned.fill(
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: CachedNetworkImage(
                      imageUrl: data['img'] ?? '', fit: BoxFit.cover, width: double.infinity)),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                height: 60.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      CommonUtil.colorsUtil('#ffffff', alpha: 0),
                      CommonUtil.colorsUtil('#2B2B2B', alpha: 0.2),
                      CommonUtil.colorsUtil('#2B2B2B', alpha: 0.6)
                    ],
                  ),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(11), bottomRight: Radius.circular(11)),
                ),
                child: Column(
                  children: [
                    16.verticalSpaceFromWidth,
                    Text(
                      data['cname'] ?? data['gname'] ?? '',
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
                    ),
                    2.verticalSpaceFromWidth,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(Assets.assetsImagesSessionGems, width: 16.w),
                        4.horizontalSpace,
                        Text(
                          '${data['price'] ?? ''}',
                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (selected)
              Positioned.fill(
                  child: Container(color: CommonUtil.colorsUtil('#EAC282', alpha: 0.4))),
            if (selected)
              Positioned(
                top: 8.w,
                left: 8.w,
                child: Image.asset(Assets.assetsImagesClonePermissionSelected, width: 16.w),
              ),
          ],
        ),
      ),
    );
  }
}
