//会员专属角色限制
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionTightAstrict extends StatelessWidget {
  const SessionTightAstrict({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
              Colors.black,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Safe<PERSON>rea(
                  child: Container(
                    width: 56,
                    height: 56,
                    color: Colors.transparent,
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: CommonUtil.bottomBarHeight() + 50.w,
              left: 0,
              right: 0,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    Assets.assetsImagesSessionTightAstrictIcon,
                    width: 112.w,
                  ),
                  18.verticalSpace,
                  CommonUtil.gradientText(
                    text: 'Selected Character'.tr,
                    colors: [AppColor.colorsUtil('#FC680B'), AppColor.colorsUtil('#FBA000')],
                    textStyle: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  /*
                  Text(
                    'Watch Ads or Get HOT',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18.sp,
                      color: AppColor.primaryText,
                      fontWeight: FontWeight.w700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  */
                  30.verticalSpaceFromWidth,
                  Column(
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          Analytics().logEvent(Analytics.unlockHot,
                              screen: Analytics.pageChatwin,
                              charID: SessionPageController.to.state.modelId);
                          Analytics().logEvent(
                            Analytics.view,
                            sourceEvent: Analytics.unlockHot,
                            sourceChar: SessionPageController.to.state.modelId.toString(),
                            sourceScreen: Analytics.pageChatwin,
                            screen: Analytics.pageHot,
                          );
                          PurchaseSheet.show(
                              page: ReportUtil.chatwin,
                              modelId: SessionPageController.to.state.modelId);
                        },
                        child: Container(
                          height: 40.w,
                          width: double.infinity,
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(40.w / 2),
                            gradient: LinearGradient(
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A'),
                              ],
                            ),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            '${'GO'.tr} HOT',
                            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      16.verticalSpaceFromWidth,
                      /*
                      InkWell(
                        onTap: () {
                          SessionPageController.to.adUnlockRole();
                        },
                        child: Obx(
                          () => Container(
                            height: 40.w,
                            width: double.infinity,
                            margin: EdgeInsets.symmetric(horizontal: 16.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(40.w / 2),
                              border: SessionPageController.to.state.adUnlockCountDown.value > 0
                                  ? Border.all(
                                      color: CommonUtil.colorsUtil('#9C9C9C'),
                                    )
                                  : null,
                              color: SessionPageController.to.state.adUnlockCountDown.value > 0
                                  ? Colors.transparent
                                  : CommonUtil.colorsUtil('#575757'),
                            ),
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  Assets.assetsImagesUnlockModelAd,
                                  width: 24.w,
                                ),
                                13.horizontalSpace,
                                Text(
                                  'Watch',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w500,
                                    color: CommonUtil.colorsUtil('#CDCDCD'),
                                  ),
                                ),
                                if (SessionPageController.to.state.adUnlockCountDown.value > 0)
                                  Padding(
                                    padding: EdgeInsets.only(left: 12.w, top: 8.w, bottom: 8.w),
                                    child: SlideCountdownSeparated(
                                      duration: Duration(
                                          seconds: SessionPageController
                                              .to.state.adUnlockCountDown.value),
                                      separatorType: SeparatorType.symbol,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          AppColor.colorsUtil('#FBA000'),
                                          AppColor.colorsUtil('#FC680B'),
                                        ]),
                                        borderRadius: BorderRadius.circular(3),
                                      ),
                                      separatorStyle: TextStyle(
                                        fontSize: 15.sp,
                                        color: CommonUtil.colorsUtil('#FBA000'),
                                        fontWeight: FontWeight.w600,
                                      ),
                                      separatorPadding: EdgeInsets.symmetric(horizontal: 4.w),
                                      style: TextStyle(
                                          fontSize: 10.sp,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                          height: 1.2),
                                      shouldShowDays: (p0) => false,
                                      shouldShowHours: (p0) => false,
                                      showZeroValue: true,
                                      onDone: () {
                                        SessionPageController.to.state.adUnlockCountDown.value = 0;
                                      },
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      )
                      */
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
