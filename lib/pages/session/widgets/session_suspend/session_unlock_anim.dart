import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class SessionUnlockAnim extends StatelessWidget {
  const SessionUnlockAnim({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
              Colors.black,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        alignment: Alignment.center,
        child: Lottie.asset('assets/lottie/role_unlock_anim/role_unlock_anim.json',
            animate: true, fit: BoxFit.contain, repeat: false),
      ),
    );
  }
}
