import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionAppBarWidget extends GetView<SessionPageController>
    implements PreferredSizeWidget {
  const SessionAppBarWidget({super.key});
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
  @override
  Widget build(BuildContext context) {
    return AppBar(
      scrolledUnderElevation: 0,
      titleSpacing: 0,
      centerTitle: false,
      backgroundColor: Colors.transparent,
      title: titleWidget(
          img: controller.state.sessionAvatarUrl,
          title: controller.state.sessionTitle),
      leading: customBackBtn(),
      actions: [
        10.horizontalSpace,
        //等级
        if (controller.state.sessionLevelInfo.isNotEmpty) levelWidget(),
        //nsfw
        if (AppService.audit == false) 8.horizontalSpace,
        if (AppService.audit == false) nsfwWidget(),
        8.horizontalSpace,
        balanceWidget(),
        15.horizontalSpace,
      ],
    );
  }

  //返回按钮
  Widget customBackBtn() {
    return CustomBackButton(
      back: () {
        //判断是否是首次启动后进入聊天页
        if (controller.state.firstLaunchOpenChat == true) {
          Get.offAllNamed(Routes.tabs);
        } else {
          Get.back();
        }
      },
    );
  }

  //头像、昵称、语音通话
  Widget titleWidget({String? img, required String title}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (img != null)
          InkWell(
            onTap: () {
              Get.toNamed(Routes.roleDetail, parameters: {
                'modelId': '${SessionPageController.to.state.modelId}'
              });
            },
            child: Padding(
              padding: AmorTraService.ar
                  ? EdgeInsets.only(left: 5)
                  : REdgeInsets.only(right: 5),
              child: CircleAvatar(
                radius: 18,
                backgroundColor: Colors.transparent,
                backgroundImage: CachedNetworkImageProvider(img),
              ),
            ),
          ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              constraints: BoxConstraints(maxWidth: 150.w),
              child: InkWell(
                onTap: () {
                  Get.toNamed(Routes.roleDetail, parameters: {
                    'modelId': '${SessionPageController.to.state.modelId}'
                  });
                },
                child: Text(
                  title.length > 6 ? '${title.substring(0, 4)}...' : title,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      fontSize: 14,
                      color: AppColor.primaryText,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ],
        ),
        // if (AppService.audit == false)
        Padding(
          padding: AmorTraService.ar
              ? EdgeInsets.only(right: 8.w)
              : EdgeInsets.only(left: 8.w),
          child: ImageBtn(
              iconSting: Assets.assetsImagesSessionTitleCall,
              onPressed: () => SessionPageController.to.showCall(type: 'voice'),
              width: 24.w,
              height: 24.w),
        ),
      ],
    );
  }

  //nsfw
  Widget nsfwWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        SessionWidgetsController.to.showNsfwSwitch();
      },
      child: Container(
        width: 71.w,
        height: 26.w,
        key: SessionWidgetsController.to.state.nsfwSwitchWidgetKey,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(26.w / 2),
          border: Border.all(width: 1, color: Colors.white),
          gradient: LinearGradient(
              colors: controller.state.contentCategory == 'NSFW' ||
                      controller.state.contentCategory == 'HOT'
                  ? [
                      AppColor.colorsUtil('#FF3D62'),
                      AppColor.colorsUtil('#FF68B9'),
                    ]
                  : controller.state.contentCategory == 'BDSM'
                      ? [
                          AppColor.colorsUtil('#0085BD'),
                          AppColor.colorsUtil('#00B0F9'),
                        ]
                      : [
                          AppColor.colorsUtil('#EAC282'),
                          AppColor.colorsUtil('#C69A51'),
                        ]),
        ),
        child: Row(
          children: [
            6.horizontalSpace,
            Expanded(
              child: Text(
                controller.state.contentCategory,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 12.sp,
                    height: 15 / 12,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    fontStyle: FontStyle.italic),
              ),
            ),
            Image.asset(
              Assets.assetsImagesSessionNsfwArrow,
              width: 15.w,
            ),
            4.horizontalSpace,
          ],
        ),
      ),
    );
  }

  //余额
  Widget balanceWidget() {
    return InkWell(
      onTap: () {
        PurchaseSheet.show(
            buyType: UserService.to.isVip ? 1 : 0,
            page: ReportUtil.chatwin,
            modelId: SessionPageController.to.state.modelId,
            source: Analytics.pageChatwin);
      },
      child: Container(
        height: 26.w,
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(26.w / 2),
            color: CommonUtil.colorsUtil('#3D3531'),
            border: Border.all(color: CommonUtil.colorsUtil('#322A2A'))),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              Assets.assetsImagesSessionGems,
              width: 17.w,
            ),
            5.horizontalSpace,
            Obx(
              () => Text(
                CommonUtil.numberUnits(UserService.to.userinfo.value.gems ?? 0),
                style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500),
              ),
            )
          ],
        ),
      ),
    );
  }

  //等级
  Widget levelWidget() {
    return InkWell(
      onTap: () {
        SessionWidgetsController.to.showSessionLevelInfo();
      },
      child: Container(
        height: 24.w,
        width: 24.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: CommonUtil.colorsUtil('#C69351'),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white)),
        child: Obx(
          () => Text(
            (AppService.sessionLevelConfig.firstWhereOrNull((value) =>
                    value['level'] ==
                    controller.state.sessionLevelInfo['level'])?['title']) ??
                '',
            style: TextStyle(fontSize: 12.sp, height: 1.5),
          ),
        ),
      ),
    );
  }
}
