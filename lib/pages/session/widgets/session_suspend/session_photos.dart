import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionPhotosWidget extends GetView<SessionPageController> {
  const SessionPhotosWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return AnimatedContainer(
        width: 1.sw,
        height: (controller.state.photos.isNotEmpty
                ? (controller.state.photosExpanded.value ? 102.w : 24.w)
                : 0) +
            (controller.state.sessionLevelInfo.isNotEmpty ? 36.w : 10.w),
        duration: const Duration(milliseconds: 100),
        // color: Colors.amber,
        child: SingleChildScrollView(
          reverse: true,
          physics: const NeverScrollableScrollPhysics(),
          child: Column(
            children: [
              //相册
              if (controller.state.photos.isNotEmpty) ...[
                photosWidget(),
                8.verticalSpaceFromWidth,
                //分割线
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Divider(height: 1, color: CommonUtil.colorsUtil('#1C2F41', alpha: 0.3)),
                ),
                //展开收起按钮
                InkWell(
                  onTap: () => controller.state.photosExpanded.toggle(),
                  child: Container(
                    margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 8.w),
                    child: Image.asset(
                      controller.state.photosExpanded.value
                          ? Assets.assetsImagesSessionPhotosUp
                          : Assets.assetsImagesSessionPhotosDown,
                      width: 24.w,
                    ),
                  ),
                ),
                8.verticalSpaceFromWidth,
              ],
              if (controller.state.sessionLevelInfo.isNotEmpty) ...[
                //等级信息
                Container(
                  alignment: AmorTraService.ar ? Alignment.centerRight : Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Text(
                    'Lvl ${controller.state.sessionLevelInfo['level']} ${controller.state.sessionLevelInfo['progress']}%',
                    style: TextStyle(
                        fontSize: 10.sp, color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                ),
                Row(
                  children: [
                    15.horizontalSpace,
                    Container(
                      width: 106.w,
                      height: 6.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.w / 2),
                        color: CommonUtil.colorsUtil('#4E4E4E'),
                      ),
                      alignment: Alignment.centerLeft,
                      child: AnimatedContainer(
                        height: 6.w,
                        width: 106.w *
                            (controller.state.sessionLevelInfo['rewards'] == 0
                                ? 1
                                : ((controller.state.sessionLevelInfo['progress'] ?? 0) / 100)),
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.w / 2),
                          color: CommonUtil.colorsUtil('#EAC282'),
                        ),
                      ),
                    ),
                    4.horizontalSpace,
                    if (controller.state.sessionLevelInfo['rewards'] != 0) ...[
                      Image.asset(
                        Assets.assetsImagesSessionGems,
                        width: 12.w,
                      ),
                      2.horizontalSpace,
                      Text(
                        '+${controller.state.sessionLevelInfo['rewards']}',
                        style: TextStyle(
                            fontSize: 10.sp, color: Colors.white, fontWeight: FontWeight.w600),
                      ),
                    ] else
                      Image.asset(
                        Assets.assetsImagesClonePermissionSelected,
                        width: 16.w,
                      ),
                  ],
                )
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget photosWidget() {
    return Container(
      width: 1.sw,
      height: 64.w,
      margin: EdgeInsets.only(top: 8.w),
      //禁止滚动拉伸
      child: NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (OverscrollIndicatorNotification? overscroll) {
          overscroll!.disallowIndicator();
          return true;
        },
        child: ListView.separated(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) => Container(
            width: 64.w,
            height: 64.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
            ),
            clipBehavior: Clip.antiAlias,
            child: InkWell(
              onTap: () {
                controller.showPhoto(index);
              },
              child: Stack(
                children: [
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: controller.state.photos[index]['img'] ?? '',
                      fit: BoxFit.cover,
                      alignment: Alignment.topCenter,
                    ),
                  ),
                  //未解锁阴影
                  if (controller.state.photos[index]['unlock'] == false) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                        child: Container(
                          color: Colors.black.withValues(alpha: 0.2),
                        ),
                      ),
                    ),
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            Assets.assetsImagesSessionGems,
                            width: 12.w,
                          ),
                          2.horizontalSpace,
                          Text(
                            '${controller.state.photos[index]['unlockGems'] ?? 0}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ],
              ),
            ),
          ),
          shrinkWrap: true,
          separatorBuilder: (context, index) => 8.horizontalSpace,
          itemCount: controller.state.photos.length,
        ),
      ),
    );
  }
}
