import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/special_text_span/special_text_span_builder.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MessageEditWidget extends StatelessWidget {
  final int msgType;
  final String content;

  const MessageEditWidget({super.key, required this.msgType, required this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          borderRadius:
              BorderRadius.only(topLeft: Radius.circular(14.w), topRight: Radius.circular(14.w)),
          color: AppColor.colorsUtil('#27292D')),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 113.w,
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.2),
            ),
            child: ExtendedTextField(
              // maxLength: maxLength,
              specialTextSpanBuilder: AppSpecialTextSpan(),
              maxLines: null,
              autofocus: true,
              // cursorColor: AppColor.mainColor,
              controller: TextEditingController(text: content),
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.done,
              // keyboardType: TextInputType.visiblePassword,
              onEditingComplete: () {},
              onChanged: (value) {
                SessionWidgetsController.to.state.editMessageText = value;
              },
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColor.primaryText,
                fontFamily: fontBeVietnamPro,
                height: 1.3,
              ),
              decoration: InputDecoration(
                // isCollapsed: true, // 取消flutter的奇怪高度
                border: InputBorder.none,
                hintStyle: TextStyle(
                  fontSize: 13.sp,
                  color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.5),
                  fontWeight: FontWeight.w400,
                  fontFamily: fontBeVietnamPro,
                  height: 1.6,
                ),
                counterText: '', //取消文字计数器
                //主要是这个参数设置为true，减少垂直高度消耗，用于编辑框对齐
                isDense: true,
                // contentPadding: const EdgeInsets.only(bottom: 2, top: 2),
              ),
            ),
          ),
          10.verticalSpaceFromWidth,
          Row(
            children: [
              if (msgType == 2)
                buttonWidget(
                    onTap: () {
                      Get.back(result: 0);
                    },
                    text: 'Text Only'.tr,
                    color: '#F0BE72'),
              if (msgType == 1)
                buttonWidget(
                    onTap: () {
                      Get.back(result: 2);
                    },
                    text: 'Go and Regen'.tr,
                    color: '#F0BE72',
                    icon: Assets.assetsImagesSessionGems),
              const Spacer(),
              buttonWidget(
                  onTap: () {
                    Get.back();
                  },
                  text: 'Cancel'.tr),
            ],
          )
        ],
      ),
    );
  }

  Widget buttonWidget(
      {required VoidCallback onTap, required String text, String? icon, String? color}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 7.w),
        decoration: BoxDecoration(
          color: AppColor.colorsUtil('#494133'),
          borderRadius: BorderRadius.circular(30.w / 2),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w400,
                color: color != null
                    ? AppColor.colorsUtil(color)
                    : Colors.white.withValues(alpha: 0.6),
              ),
            ),
            if (icon != null)
              Padding(
                padding: EdgeInsets.only(left: 2.w),
                child: Image.asset(
                  icon,
                  width: 14.w,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
