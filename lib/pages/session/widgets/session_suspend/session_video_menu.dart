import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class SessionVideoMenuWidget extends StatelessWidget {
  final String videoThumbnailUrl;
  const SessionVideoMenuWidget({super.key, required this.videoThumbnailUrl});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        InkWell(
          onTap: () {
            SessionPageController.to.showCall(type: 'video');
          },
          child: SizedBox(
            width: 94.w,
            height: 94.w,
            child: Stack(
              children: [
                if (videoThumbnailUrl.isNotEmpty)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: CommonUtil.colorsUtil('#EAC282'),
                        ),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: videoThumbnailUrl,
                          // alignment: Alignment.topCenter,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => ClipRRect(
                            borderRadius: BorderRadius.circular(15.w),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 4.0, sigmaY: 4.0),
                              child: Container(
                                color: Colors.white.withValues(alpha: 0.2),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: Image.asset(
                    Assets.assetsImagesSessionVideoCallIcon,
                    width: 32.w,
                  ),
                )
              ],
            ),
          ),
        ),
        /*
          12.verticalSpaceFromWidth,
          creatMenuItem(onTap: () {}, title: '🔥Generate Pictures'),
          12.verticalSpaceFromWidth,
          creatMenuItem(onTap: () {}, title: '🔥Generate Videos'),
          */
      ],
    );
  }

  Widget creatMenuItem({required VoidCallback onTap, required String title}) {
    return InkWell(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15.w),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 4.0, sigmaY: 4.0, tileMode: TileMode.repeated),
          child: Container(
            width: 140.w,
            height: 30.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.w),
              border: Border.all(
                color: CommonUtil.colorsUtil('#EAC282'),
              ),
              color: Colors.white.withValues(alpha: 0.2),
            ),
            alignment: Alignment.center,
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
