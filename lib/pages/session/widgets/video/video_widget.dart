import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'controller.dart';

class SessionVideoPlayPage extends StatelessWidget {
  const SessionVideoPlayPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SessionVideoPlayController>(
      builder: (controller) => Container(
        width: 1.sw,
        height: 1.sh,
        color: CommonUtil.colorsUtil('#020204'),
        alignment: Alignment.center,
        child: Stack(
          children: [
            if (controller.videoPlayController.value.isInitialized == true)
              InkWell(
                onTap: () {
                  controller.playOrPauseVideo();
                },
                child: Center(
                  child: AspectRatio(
                    aspectRatio: controller.videoPlayController.value.aspectRatio,
                    child: VideoPlayer(controller.videoPlayController),
                  ),
                ),
              )
            else
              const Center(
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  backgroundColor: Colors.transparent,
                ),
              ),
            //播放按钮
            if (controller.videoPlayController.value.isPlaying == false &&
                controller.videoPlayController.value.isInitialized == true)
              // 点击视频 暂停、继续播放功能
              InkWell(
                onTap: () {
                  controller.playOrPauseVideo();
                },
                child: Center(
                  child: Image.asset(
                    Assets.assetsImagesSessionVideoPlay,
                    width: 36.w,
                  ),
                ),
              ),
            //视频加载
            Align(
              alignment: Alignment.center,
              child: Obx(
                () => controller.videoLoading.value == true
                    ? Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          backgroundColor: Colors.transparent,
                        ),
                      )
                    : Container(),
              ),
            ),
            Positioned(
              top: CommonUtil.statusBarHeight(context),
              left: 12.w,
              child: CustomBackButton(
                img: Assets.assetsImagesNavCloseBlackCircle,
                width: 28.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
