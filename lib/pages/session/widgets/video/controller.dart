import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class SessionVideoPlayController extends GetxController {
  final String videoUrl;
  SessionVideoPlayController({required this.videoUrl});
  late VideoPlayerController videoPlayController;
  //视频是否在加载
  var videoLoading = false.obs;
  @override
  void onInit() {
    super.onInit();
    setupVideoPlayer();
  }

  setupVideoPlayer() async {
    videoPlayController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
    videoPlayController.addListener(() {
      if (videoPlayController.value.isCompleted == false) {
        videoLoading.value = videoPlayController.value.isBuffering;
      }
      if (videoPlayController.value.isCompleted == true) {
        videoLoading.value = false;
        update();
      }
    });
    await videoPlayController.initialize();
    videoPlayController.play();
    update();
  }

  playOrPauseVideo() async {
    if (videoPlayController.value.isPlaying) {
      await videoPlayController.pause();
    } else {
      await videoPlayController.play();
    }
    update();
  }

  @override
  void onClose() {
    super.onClose();
    videoPlayController.dispose();
  }
}
