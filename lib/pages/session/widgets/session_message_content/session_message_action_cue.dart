import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:slide_countdown/slide_countdown.dart';

//提示动作
class ChatMessageActionCue extends StatelessWidget {
  final SessionModel chatMessageModel;

  const ChatMessageActionCue({super.key, required this.chatMessageModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.7),
      ),
      child: Column(
        children: widgets(),
      ),
    );
  }

  List<Widget> widgets() {
    List<Widget> list = [
      17.verticalSpace,
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            Assets.assetsImagesSessionActionTip,
            width: 28.w,
          ),
          5.horizontalSpace,
          Text(
            'Out of ideas? Try these:'.tr,
            style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                color: AppColor.colorsUtil('#F0BE72')),
          ),
        ],
      ),
      10.verticalSpace,
      16.verticalSpace,
    ];
    if (SessionPageController.to.msgTipLogic.tipCount == 0 && UserService.to.isVip != true) {
      list.insertAll(3, [
        Image.asset(
          Assets.assetsImagesSessionTightAstrictIcon,
          width: 96.w,
        ),
        4.verticalSpaceFromWidth,
        Text(
          'chat ideas tip'.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            fontStyle: FontStyle.italic,
            color: CommonUtil.colorsUtil('#F0BE72'),
          ),
        ),
        10.verticalSpaceFromWidth,
        Container(
          height: 40.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: CommonUtil.colorsUtil('#C69A51'),
            ),
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Refreshing in'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                ),
              ),
              10.horizontalSpace,
              SizedBox(
                height: 22.w,
                child: SlideCountdownSeparated(
                  duration: Duration(seconds: SessionPageController.to.msgTipLogic.tipCountDown),
                  separatorType: SeparatorType.symbol,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                      AppColor.colorsUtil('#FBA000'),
                      AppColor.colorsUtil('#FC680B'),
                    ]),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  separatorStyle: TextStyle(
                    fontSize: 15.sp,
                    color: CommonUtil.colorsUtil('#FBA000'),
                    fontWeight: FontWeight.w600,
                  ),
                  separatorPadding: EdgeInsets.symmetric(horizontal: 4.w),
                  style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      height: 1.2),
                  shouldShowDays: (p0) => false,
                  shouldShowHours: (p0) => false,
                  showZeroValue: true,
                  onDone: () {
                    SessionPageController.to.msgTipLogic.hideTip(showSale: false);
                  },
                ),
              ),
            ],
          ),
        )
      ]);
      return list;
    }
    if (chatMessageModel.actionTips!.isNotEmpty) {
      list.insertAll(
        3,
        List.generate(
          chatMessageModel.actionTips!.length,
          (index) => GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              SessionWidgetsController.to.chatInputTextFieldCtl.text =
                  "${SessionWidgetsController.to.chatInputTextFieldCtl.text}${chatMessageModel.actionTips!.elementAt(index)}";
              SessionPageController.to.msgTipLogic.hideTip();
              SessionWidgetsController.to.focusNode.requestFocus();
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.w / 2),
                  color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.5),
                ),
                padding: REdgeInsets.symmetric(horizontal: 15, vertical: 5),
                margin: REdgeInsets.only(bottom: 16),
                child: EasyRichText(
                  chatMessageModel.actionTips!.elementAt(index),
                  textAlign: TextAlign.start,
                  defaultStyle:
                      TextStyle(fontSize: 12.sp, color: Colors.white, fontFamily: fontBeVietnamPro),
                  patternList: CommonUtil.countOccurrences(
                                  chatMessageModel.actionTips!.elementAt(index) ?? '', '*') ==
                              0 ||
                          CommonUtil.countOccurrences(
                                  chatMessageModel.actionTips!.elementAt(index) ?? '', '*')
                              .isOdd
                      ? null
                      : (CommonUtil.getTextBetweenStr(
                                  text: chatMessageModel.actionTips!.elementAt(index) ?? '',
                                  exp: [r'\*([^]*?)\*', r'\((.*?)\)'],
                                  include: 1) ??
                              [])
                          .map(
                            (e) => EasyRichTextPattern(
                              targetString: e,
                              hasSpecialCharacters: true,
                              style: const TextStyle(
                                color: Colors.white,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          )
                          .toList(),
                )),
          ),
        ),
      );
    } else {
      list.insertAll(
        3,
        List.generate(
            3,
            (index) => Shimmer.fromColors(
                  baseColor: AppColor.colorsUtil('#F7D4A7'),
                  highlightColor: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.2),
                  enabled: true,
                  child: Container(
                    width: 200.w,
                    height: 28.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30.w / 2),
                      color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.5),
                    ),
                    margin: REdgeInsets.only(bottom: 16),
                  ),
                )),
      );
    }
    if (UserService.to.isVip != true) {
      list.insert(
        list.length - 1,
        Container(
          height: 40.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: CommonUtil.colorsUtil('#C69A51'),
            ),
          ),
          alignment: Alignment.center,
          child: Text(
            'Free Passes Left'.trArgs(['${SessionPageController.to.msgTipLogic.tipCount - 1}']),
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }
    return list;
  }
}
