import 'dart:convert';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatMessageVideoWidget extends StatelessWidget {
  final SessionModel chatMessageModel;
  const ChatMessageVideoWidget({super.key, required this.chatMessageModel});

  @override
  Widget build(BuildContext context) {
    Map? data;
    try {
      data = jsonDecode(chatMessageModel.content ?? '');
    } catch (e) {
      data = null;
    }
    if (data == null) {
      return Container();
    }
    return Row(
      children: [
        InkWell(
          onTap: () => SessionWidgetsController.to.playVideo(model: chatMessageModel),
          child: Container(
            width: 200.w,
            height: 266.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: AmorTraService.ar ? Radius.circular(16.r) : Radius.circular(6.r),
                topRight: AmorTraService.ar ? Radius.circular(6.r) : Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
                bottomLeft: Radius.circular(16.r),
              ),
              color: Colors.black.withValues(alpha: 0.5),
            ),
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      //锁定状态
                      if (UserService.to.isVip == false || data['thumbnail_url'] == null)
                        Positioned.fill(
                          child: Image.asset(
                            Assets.assetsImagesSessionVideoLicoBg,
                          ),
                        ),
                      if (UserService.to.isVip == false || data['thumbnail_url'] == null)
                        Positioned.fill(
                            child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                        )),
                      if (data['thumbnail_url'] != null && UserService.to.isVip == true)
                        Positioned.fill(
                          child: CachedNetworkImage(
                            imageUrl: data['thumbnail_url'],
                            fit: BoxFit.cover,
                          ),
                        ),

                      Center(
                        child: Image.asset(
                          Assets.assetsImagesSessionVideoPlay,
                          width: 32.w,
                        ),
                      ),
                      Align(
                        alignment: Alignment.topLeft,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(bottomRight: Radius.circular(16.r)),
                            color: CommonUtil.colorsUtil('#FFA942'),
                          ),
                          child: Text(
                            'Porn video'.tr,
                            style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w600,
                                color: CommonUtil.colorsUtil('#262626')),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
