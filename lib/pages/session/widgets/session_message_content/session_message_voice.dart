import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class SessionMessageVoice extends StatelessWidget {
  final SessionModel chatMessageModel;
  final int listIndex;
  const SessionMessageVoice({super.key, required this.chatMessageModel, required this.listIndex});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: messageContentWidgetList(),
    );
  }

  List<Widget> messageContentWidgetList() {
    return [
      Stack(
        alignment: Alignment.bottomLeft,
        children: [
          InkWell(
            onTap: () {
              if (UserService.to.isVip == true) {
                if (chatMessageModel.content != null) {
                  SessionPageController.to.playVoiceRequest(index: listIndex);
                }
              } else {
                SessionWidgetsController.to.focusNode.unfocus();
                Analytics().logEvent(Analytics.clockaudio);
                Future.delayed(
                    Duration(
                        milliseconds: SessionWidgetsController.to.focusNode.hasFocus ? 400 : 0),
                    () {
                  PurchaseSheet.show(
                      page: ReportUtil.chatwin,
                      modelId: SessionPageController.to.state.modelId,
                      source: Analytics.tpaygemslockaudio);
                });
              }
            },
            child: _messageContainer(
              margin: EdgeInsets.all(0),
              child: Row(
                children: [
                  20.horizontalSpace,
                  //生成或下载中
                  SessionPageController.to.voicePlay.underWayList.contains(chatMessageModel.msgId)
                      ? SizedBox(
                          height: 18.w,
                          width: 18.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primaryText),
                            backgroundColor: AppColor.primaryText.withValues(alpha: 0.2),
                          ),
                        )
                      : Obx(
                          () => Image.asset(
                            SessionWidgetsController.to.state.voicesPlayIndex == listIndex
                                ? Assets.assetsImagesSessionVoicePause
                                : Assets.assetsImagesSessionVoicePlay,
                            width: 24.w,
                            height: 24.w,
                          ),
                        ),
                  4.horizontalSpace,
                  SizedBox(
                    width: 90.w,
                    height: 36.w,
                    child: Obx(
                      () => Lottie.asset(
                        'assets/lottie/session_voice_long_play.json',
                        animate: SessionWidgetsController.to.state.voicesPlayIndex == listIndex,
                        repeat: true,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(16.r)),
                color: CommonUtil.colorsUtil('#FFA942'),
              ),
              child: Text(
                'Moans for you'.tr,
                style: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w400,
                    color: CommonUtil.colorsUtil('#030303')),
              ),
            ),
          ),
        ],
      ),
    ];
  }

  //消息容器
  Widget _messageContainer({required Widget child, required EdgeInsetsGeometry margin}) {
    return Container(
      width: 154.w,
      height: 64.w,
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(6.r),
          topRight: Radius.circular(16.r),
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
        color: AppColor.colorsUtil('#323235'),
      ),
      margin: margin,
      child: child,
    );
  }
}
