import 'dart:ui';

import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';

class ChatMessageTime extends StatelessWidget {
  final SessionModel chatMessageModel;

  // 上一条消息
  final SessionModel? lastModel;

  const ChatMessageTime({super.key, required this.chatMessageModel, this.lastModel});

  @override
  Widget build(BuildContext context) {
    if (chatMessageModel.sendDate == null) {
      return Container();
    }
    if (lastModel != null && lastModel!.sendDate != null) {
      //小于5分钟 ，不展示
      if (CommonUtil.timeDifference(chatMessageModel.sendDate!, lastModel!.sendDate!,
              unit: 'minutes') <
          5) {
        return Container();
      }
    }
    String t = getTimeString(chatMessageModel.sendDate);
    if (t.isEmpty) {
      return Container();
    }
    return Center(
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0, tileMode: TileMode.repeated),
          child: Container(
            margin: REdgeInsets.only(bottom: 15),
            decoration: BoxDecoration(
              color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(24.w / 2),
            ),
            // alignment: Alignment.center,
            padding: REdgeInsets.symmetric(
              horizontal: 12,
              vertical: 4,
            ),
            child: Text(
              t,
              style: TextStyle(
                fontSize: 12.sp,
                height: 1.2,
                color: AppColor.primaryText.withValues(alpha: 0.7),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String getTimeString(int? timeNum) {
    String time = CommonUtil.convertTimes(timeNum ?? DateTime.now().millisecondsSinceEpoch);
    return time;
  }
}
