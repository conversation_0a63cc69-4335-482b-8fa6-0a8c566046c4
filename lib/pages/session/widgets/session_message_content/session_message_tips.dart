import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionMessageAiTips extends StatelessWidget {
  const SessionMessageAiTips({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 2.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.w / 2),
            color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.5),
          ),
          child: Text(
            'chat ai tip'.tr,
            style: TextStyle(
              fontSize: 12.sp,
              height: 1.2,
              color: Colors.white.withValues(alpha: 0.7),
              shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 1)],
            ),
          ),
        ),
      ],
    );
  }
}
