import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class ChatMessageRatingBar extends StatelessWidget {
  final double initialRating;
  final bool enable;
  final bool enableDrag; //滑动评分
  final Function(double rating) onRatingUpdate;

  const ChatMessageRatingBar(
      {super.key,
      required this.initialRating,
      required this.onRatingUpdate,
      required this.enable,
      this.enableDrag = false});

  @override
  Widget build(BuildContext context) {
    return RatingBar.builder(
      initialRating: initialRating,
      minRating: 1,
      ignoreGestures: !enable,
      direction: Axis.horizontal,
      itemCount: 5,
      unratedColor: Colors.white, //未评分颜色
      // glowColor: AppColor.colorsUtil('#FFD25F'),
      glow: false, //操作时发光
      itemPadding: const EdgeInsets.symmetric(horizontal: 3.0),
      tapOnlyMode: !enableDrag,
      itemSize: 20.w,
      itemBuilder: (context, index) {
        return Image.asset(
          index + 1 > initialRating
              ? Assets.assetsImagesSessionScore
              : Assets.assetsImagesSessionScored,
          width: 20.w,
          height: 20.w,
        );
      },
      onRatingUpdate: onRatingUpdate,
    );
  }
}
