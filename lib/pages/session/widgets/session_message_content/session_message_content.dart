import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'session_message_time.dart';
// import 'chat_message_voice.dart';
import 'session_message_widgets.dart';

class ChatMessageContent extends StatelessWidget {
  final SessionModel chatMessageModel;
  final SessionModel? lastModel;
  final int listIndex;
  //消息是否有语音
  final bool showVoice;
  const ChatMessageContent({
    super.key,
    required this.chatMessageModel,
    required this.listIndex,
    this.lastModel,
    required this.showVoice,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          chatMessageModel.msgType == 1 ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        ChatMessageTime(
          chatMessageModel: chatMessageModel,
          lastModel: lastModel,
        ),
        //主要消息
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: chatMessageModel.msgType == 2
                ? CrossAxisAlignment.start
                : CrossAxisAlignment.center,
            mainAxisAlignment:
                chatMessageModel.msgType == 1 ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: chatMessageModel.msgType == 1
                ? _messageContentList().reversed.toList()
                : _messageContentList(),
          ),
        ),
        //评分
        chatMessageModel.msgType == 2 &&
                chatMessageModel.status != -2 &&
                ((SessionPageController.to.msgTipLogic.isShowTip && listIndex == 1) ||
                    !SessionPageController.to.msgTipLogic.isShowTip && listIndex == 0) &&
                SessionPageController.to.textOutputTimer == null
            ? Padding(
                padding: REdgeInsets.only(left: 20, top: 10),
                child: ChatMessageWidgets.oprationButtonList(
                    msgId: chatMessageModel.msgId ?? '',
                    content: chatMessageModel.content,
                    score: chatMessageModel.score ?? 0),
              )
            : Container(),
      ],
    );
  }

  List<Widget> _messageContentList() {
    String msgContent = (chatMessageModel.content ?? '');
    //这里只处理展示完成消息，动画展示在下边处理
    if (chatMessageModel.msgId != SessionPageController.to.state.showTextMsgId ||
        chatMessageModel.msgType == 2) {
      //展示翻译内容
      if (chatMessageModel.showTransContent == true && chatMessageModel.translateContent != null) {
        msgContent = chatMessageModel.translateContent!;
      }
      if (chatMessageModel.msgIgnoreErr == false) {
        //将*替换为空格
        msgContent = msgContent.replaceAll(RegExp(r'\*'), ' ');
        //如果第一个是空格就删除
        if (msgContent.startsWith(' ')) {
          msgContent = msgContent.replaceRange(0, 1, '');
        }
      }
    }
    return [
      Stack(
        children: [
          _messageContainer(
            padding: showVoice == true && chatMessageModel.msgType == 2
                ? REdgeInsets.fromLTRB(20, 15, 20, 20) //语音消息
                : REdgeInsets.symmetric(horizontal: 15, vertical: 15),
            margin: EdgeInsets.only(
              bottom: showVoice == true ? 25.w / 2 : 0,
              right: AmorTraService.ar
                  ? (chatMessageModel.msgType == 2 ? 0 : 24.w)
                  : (chatMessageModel.msgType == 2 ? 82.w : 0),
              left: AmorTraService.ar
                  ? (chatMessageModel.msgType == 1 ? 0 : 82.w)
                  : (chatMessageModel.msgType == 1 ? 24.w : 0),
            ),
            maxWidth: (chatMessageModel.giftImg != null && chatMessageModel.msgType == 1)
                ? 200.w
                : 1.sw - 24.w - 82.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                //用户消息的礼物图片
                if (chatMessageModel.giftImg != null && chatMessageModel.msgType == 1)
                  InkWell(
                    onTap: () =>
                        SessionWidgetsController.to.showGiftImage(chatMessageModel.giftImg!),
                    child: Padding(
                      padding: EdgeInsets.only(bottom: chatMessageModel.content != null ? 8.w : 0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                            imageUrl: chatMessageModel.giftImg!, fit: BoxFit.cover),
                      ),
                    ),
                  ),
                chatMessageModel.msgId == SessionPageController.to.state.showTextMsgId &&
                        chatMessageModel.msgType == 2
                    //正在输出的消息
                    ? Obx(() {
                        msgContent = SessionPageController.to.state.animateString.value;
                        if (chatMessageModel.msgIgnoreErr == false) {
                          //将*替换为空格
                          msgContent = msgContent.replaceAll(RegExp(r'\*'), ' ');
                          //如果第一个是空格就删除
                          if (msgContent.startsWith(' ')) {
                            msgContent = msgContent.replaceRange(0, 1, '');
                          }
                        }
                        return EasyRichText(
                          msgContent,
                          textAlign: TextAlign.start,
                          defaultStyle: TextStyle(
                              fontSize: 15.sp,
                              height: 1.3,
                              color: chatMessageModel.contentCategory == 'SFW'
                                  ? AppColor.colorsUtil('#F0BE72')
                                  : chatMessageModel.contentCategory == 'NSFW'
                                      ? AppColor.colorsUtil('#FF4D83')
                                      : AppColor.colorsUtil('#4DBFFF'),
                              fontFamily: fontBeVietnamPro),
                          patternList: chatMessageModel.msgIgnore == null ||
                                  chatMessageModel.msgIgnoreErr == true ||
                                  chatMessageModel.msgIgnore!.length > 15
                              ? null
                              : chatMessageModel.msgIgnore!.map((e) {
                                  return EasyRichTextPattern(
                                    targetString: e,
                                    hasSpecialCharacters: true,
                                    style: TextStyle(
                                      color: AppColor.primaryText.withValues(alpha: 0.7),
                                      fontStyle: FontStyle.italic,
                                    ),
                                  );
                                }).toList(),
                        );
                      })
                    : EasyRichText(
                        msgContent,
                        textAlign: TextAlign.start,
                        defaultStyle: TextStyle(
                            fontSize: 15.sp,
                            height: 1.3,
                            color: chatMessageModel.msgType == 2
                                ? chatMessageModel.contentCategory == 'SFW'
                                    ? AppColor.colorsUtil('#F0BE72')
                                    : chatMessageModel.contentCategory == 'NSFW'
                                        ? AppColor.colorsUtil('#FF4D83')
                                        : AppColor.colorsUtil('#4DBFFF')
                                : AppColor.primaryText,
                            fontFamily: fontBeVietnamPro),
                        patternList: chatMessageModel.msgIgnore == null ||
                                chatMessageModel.msgIgnoreErr == true ||
                                chatMessageModel.msgIgnore!.length > 15
                            ? null
                            : ((chatMessageModel.showTransContent == true &&
                                        chatMessageModel.translateContent != null &&
                                        chatMessageModel.traMsgIgnore != null)
                                    ? chatMessageModel.traMsgIgnore! //翻译的内容
                                    : chatMessageModel.msgIgnore!)
                                .map((e) {
                                return EasyRichTextPattern(
                                  targetString: e,
                                  hasSpecialCharacters: true,
                                  style: TextStyle(
                                    color: AppColor.primaryText.withValues(alpha: 0.7),
                                    fontStyle: FontStyle.italic,
                                  ),
                                );
                              }).toList(),
                      ),

                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    //展示原文 按钮
                    if (chatMessageModel.msgId != SessionPageController.to.state.showTextMsgId &&
                        chatMessageModel.showTransContent == true &&
                        chatMessageModel.translateContent != null &&
                        SessionPageController.to.state.displayMsgTrans &&
                        chatMessageModel.msgType == 2)
                      InkWell(
                        onTap: () => SessionWidgetsController.to
                            .showOriginalMessage(index: listIndex, model: chatMessageModel),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.w),
                          margin: EdgeInsets.only(top: 4.w),
                          decoration: BoxDecoration(
                            color: AppColor.colorsUtil('#EAC282'),
                            gradient: LinearGradient(
                              colors: chatMessageModel.contentCategory == 'SFW'
                                  ? [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69351')]
                                  : chatMessageModel.contentCategory == 'NSFW'
                                      ? [
                                          AppColor.colorsUtil('#FF5374'),
                                          AppColor.colorsUtil('#FF5374')
                                        ]
                                      : [
                                          AppColor.colorsUtil('#0085BD'),
                                          AppColor.colorsUtil('#00B0F9')
                                        ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(24.w / 2),
                          ),
                          child: Text(
                            'Undo'.tr,
                            style: TextStyle(
                                fontSize: 10.sp, fontWeight: FontWeight.w600, color: Colors.white),
                          ),
                        ),
                      ),
                    if (chatMessageModel.giftImg != null &&
                        chatMessageModel.content != null &&
                        chatMessageModel.msgType == 2)
                      InkWell(
                        onTap: () =>
                            SessionWidgetsController.to.showGiftImage(chatMessageModel.giftImg!),
                        child: Container(
                          width: 30.w,
                          height: 40.w,
                          margin: AmorTraService.ar == true
                              ? EdgeInsets.only(right: 4.w, top: 8.w)
                              : EdgeInsets.only(left: 4.w, top: 8.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          clipBehavior: Clip.hardEdge,
                          child: CachedNetworkImage(
                            imageUrl: chatMessageModel.giftImg!,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          if (showVoice)
            Positioned(
              right: AmorTraService.ar ? 20 : null,
              left: AmorTraService.ar ? null : 20,
              bottom: 0,
              child: ChatMessageWidgets.voiceMsgPlayButton(
                  chatMessageModel: chatMessageModel, listIndex: listIndex),
            ),
          //编辑消息、重新生成、翻译
          if ((chatMessageModel.msgType == 2 ||
                  (chatMessageModel.msgType == 1 &&
                      listIndex == SessionWidgetsController.to.state.newMessageFromUser)) &&
              chatMessageModel.status != -2)
            Positioned(
              bottom: showVoice ? 15.w : 4.w,
              right: AmorTraService.ar
                  ? (chatMessageModel.msgType == 1 ? 0 : null)
                  : (chatMessageModel.msgType == 2 ? 0 : null),
              left: AmorTraService.ar
                  ? (chatMessageModel.msgType == 2 ? 0 : null)
                  : (chatMessageModel.msgType == 1 ? 0 : null),
              width: chatMessageModel.msgType == 2 ? 78.w : 20.w,
              child: _messageButton(),
            ),
        ],
      ),
    ];
  }

  //消息容器
  Widget _messageContainer({
    required Widget child,
    required EdgeInsetsGeometry padding,
    required EdgeInsetsGeometry margin,
    required double maxWidth,
  }) {
    return Opacity(
      opacity: 0.93,
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth, minWidth: showVoice ? 96.w : 0),
        decoration: BoxDecoration(
          color: chatMessageModel.msgType != 1 ? AppColor.colorsUtil('#323235') : null,
          gradient: chatMessageModel.msgType != 1
              ? null
              : LinearGradient(
                  colors: chatMessageModel.contentCategory == 'SFW'
                      ? [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69351')]
                      : chatMessageModel.contentCategory == 'NSFW'
                          ? [AppColor.colorsUtil('#FF5374'), AppColor.colorsUtil('#FF5374')]
                          : [AppColor.colorsUtil('#0085BD'), AppColor.colorsUtil('#00B0F9')],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.only(
            topLeft: AmorTraService.ar
                ? Radius.circular(chatMessageModel.msgType == 2 ? 16.r : 6.r)
                : Radius.circular(chatMessageModel.msgType == 1 ? 16.r : 6.r),
            bottomRight: Radius.circular(16.r),
            bottomLeft: Radius.circular(16.r),
            topRight: AmorTraService.ar
                ? Radius.circular(chatMessageModel.msgType == 2 ? 6.r : 16.r)
                : Radius.circular(chatMessageModel.msgType == 1 ? 6.r : 16.r),
          ),
        ),
        padding: padding,
        margin: margin,
        child: child,
      ),
    );
  }

  //按钮
  Widget _messageButton() {
    return Container(
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          //展示翻译时不能编辑消息
          if (chatMessageModel.showTransContent != true &&
              chatMessageModel.contentType != 'gift' &&
              chatMessageModel.contentType != 'clothe')
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ImageBtn(
                  iconSting: Assets.assetsImagesEditMessage,
                  onPressed: () {
                    SessionWidgetsController.to
                        .editMessage(model: chatMessageModel, listIndex: listIndex);
                  },
                  width: 20.w,
                  height: 20.w,
                ),
              ],
            ),

          if (chatMessageModel.msgType == 2 &&
              listIndex == 0 &&
              chatMessageModel.contentType != 'gift' &&
              chatMessageModel.contentType != 'clothe')
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                  height: 16.w,
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                ),
                ImageBtn(
                  iconSting: Assets.assetsImagesChatMsgRefresh,
                  onPressed: () {
                    SessionPageController.to.regenerate();
                  },
                  width: 20.w,
                  height: 20.w,
                ),
              ],
            ),

          if (chatMessageModel.msgType == 2 &&
              SessionPageController.to.state.displayMsgTrans &&
              chatMessageModel.showTransContent != true &&
              (chatMessageModel.content ?? '').isNotEmpty)
            Row(
              children: [
                if (chatMessageModel.contentType != 'gift' &&
                    chatMessageModel.contentType != 'clothe')
                  Container(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                    height: 16.w,
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                  ),
                ImageBtn(
                  iconSting: Assets.assetsImagesMsgTra,
                  onPressed: () {
                    SessionWidgetsController.to
                        .messageTranslate(model: chatMessageModel, index: listIndex);
                  },
                  width: 20.w,
                  height: 20.w,
                )
              ],
            ),
        ],
      ),
    );
  }
  //转义
  // String escapeRegexSymbols(String input) {
  //   return input.replaceAllMapped(RegExp(r'[[\]{}+()?\\^|]'), (match) {
  //     return '\\${match.group(0)}';
  //   });
  // }
}
