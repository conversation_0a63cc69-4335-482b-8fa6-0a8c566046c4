import 'dart:ui';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatMessageLockContent extends StatelessWidget {
  final SessionModel chatMessageModel;
  const ChatMessageLockContent({super.key, required this.chatMessageModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: messageContentWidgets(),
    );
  }

  List<Widget> messageContentWidgets() {
    String msgContent = (chatMessageModel.content ?? '');
    return [
      Stack(
        alignment: Alignment.bottomLeft,
        children: [
          Container(
            width: 1.sw - 95.w,
            constraints: BoxConstraints(minHeight: 110.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6.r),
                topRight: Radius.circular(16.r),
                bottomLeft: Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
              ),
              color: AppColor.colorsUtil('#323235'),
            ),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 25.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (chatMessageModel.msgId == SessionPageController.to.state.showTextMsgId)
                  Obx(() {
                    msgContent = SessionPageController.to.state.animateString.value;
                    return Text(
                      msgContent,
                      textAlign: TextAlign.start,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                        color: Colors.white,
                      ),
                    );
                  }),
                if (chatMessageModel.msgId != SessionPageController.to.state.showTextMsgId)
                  Text(
                    msgContent,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w500,
                      height: 1.3,
                      color: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6.r),
                topRight: Radius.circular(16.r),
                bottomLeft: Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
              ),
              child: BackdropFilter(
                blendMode: BlendMode.src,
                filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColor.colorsUtil('#323235').withValues(alpha: 0.5),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      10.verticalSpaceFromWidth,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Tap to see the'.tr,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          8.horizontalSpace,
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.w),
                              color: CommonUtil.colorsUtil('#FFA942'),
                            ),
                            child: Text(
                              'message'.tr,
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w800,
                                color: CommonUtil.colorsUtil('#282828'),
                              ),
                            ),
                          ),
                        ],
                      ),
                      8.verticalSpaceFromWidth,
                      InkWell(
                        onTap: () {
                          PurchaseSheet.show(
                              page: ReportUtil.chatwin,
                              modelId: SessionPageController.to.state.modelId,
                              source: Analytics.tpaygemslocktext);
                          Analytics().logEvent(Analytics.clocktext);
                        },
                        child: Container(
                          width: 148.w,
                          height: 34.w,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(Assets.assetsImagesChatMsgUnlockBtn),
                                fit: BoxFit.fill),
                          ),
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(bottom: 3.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Unlock'.tr,
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                              2.horizontalSpace,
                              Image.asset(
                                Assets.assetsImagesChatMsgUnlockBtnIcon,
                                width: 23.w,
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            child: Container(
              padding: REdgeInsets.fromLTRB(12, 5, 14, 5),
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(Assets.assetsImagesChatMsgLockTitle), fit: BoxFit.fill),
              ),
              alignment: Alignment.center,
              child: Text(
                'Unlock NSFW Replay'.tr,
                style: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w400,
                    color: CommonUtil.colorsUtil('#030303')),
              ),
            ),
          ),
        ],
      ),
    ];
  }
}
