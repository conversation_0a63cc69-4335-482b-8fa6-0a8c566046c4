import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SessionMessageIntro extends StatefulWidget {
  final String introContent;
  const SessionMessageIntro({super.key, required this.introContent});

  @override
  State<SessionMessageIntro> createState() => _SessionMessageIntroState();
}

class _SessionMessageIntroState extends State<SessionMessageIntro> {
  @override
  Widget build(BuildContext context) {
    String text = widget.introContent;
    if (widget.introContent.endsWith('\n')) {
      text = widget.introContent
          .replaceRange(widget.introContent.length - 1, widget.introContent.length, '');
    }
    return Stack(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            SessionWidgetsController.to.state.introUnfold.toggle();
            SessionPageController.to.msgTipLogic.hideTip();
            ReportUtil.reportEvents(
                page: ReportUtil.chatwin,
                action: ReportUtil.expand,
                value: SessionWidgetsController.to.state.introUnfold.value == true
                    ? 'Scenario'
                    : 'chatmode');
          },
          child: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              final span = TextSpan(
                  text: "${'Scenario:'.tr} $text", style: TextStyle(fontSize: 15.sp, height: 1.3));
              final tp = TextPainter(text: span, maxLines: 100, textDirection: TextDirection.ltr);
              tp.layout(maxWidth: 1.sw - 24.w - 52.w);
              TextSelection selection =
                  TextSelection(baseOffset: 0, extentOffset: "${'Scenario:'.tr} $text".length);
              List<TextBox> boxes = tp.getBoxesForSelection(selection);
              int numberOfLines = boxes.length;
              return Obx(
                () => _messageContainer(
                  height: tp.size.height,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: REdgeInsets.symmetric(vertical: 12),
                          child: RichText(
                            overflow: TextOverflow.ellipsis,
                            maxLines: 100,

                            // SessionWidgetsController.to.state.introUnfold.value == false
                            //     ? 2
                            //     : 100,
                            text: TextSpan(
                              style: TextStyle(
                                fontSize: 15.sp,
                                height: 1.3,
                                letterSpacing: GetPlatform.isIOS ? -0.0 : 0,
                                color: AppColor.primaryText.withValues(alpha: 0.7),
                              ),
                              children: [
                                TextSpan(
                                  text: "Scenario:".tr,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextSpan(
                                  text: text,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (numberOfLines > 2)
                        Padding(
                          padding: EdgeInsets.only(right: 12.w, bottom: 10, left: 4.w),
                          child: Image.asset(
                            SessionWidgetsController.to.state.introUnfold.value == false
                                ? Assets.assetsImagesSessionMsgUnfold
                                : Assets.assetsImagesSessionMsgPackup,
                            width: 16.w,
                            height: 16.w,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  //消息容器
  Widget _messageContainer({required Widget child, required double height}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 50),
      height: SessionWidgetsController.to.state.introUnfold.value == false ? 60.w : height + 30.w,
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          blendMode: BlendMode.src,
          child: Container(
            constraints: BoxConstraints(maxWidth: 1.sw - 24.w),
            decoration: BoxDecoration(
              color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.5),
              borderRadius: BorderRadius.all(Radius.circular(16.r)),
            ),
            clipBehavior: Clip.none,
            padding: REdgeInsets.fromLTRB(20, 0, 0, 0),
            child: child,
          ),
        ),
      ),
    );
  }
}
