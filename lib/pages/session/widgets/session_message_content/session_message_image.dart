import 'dart:ui';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatMessageImage extends StatelessWidget {
  final SessionModel model;
  const ChatMessageImage({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            SessionWidgetsController.to.showImage(model: model);
          },
          child: Opacity(
            opacity: 0.94,
            child: Container(
              width: 126.w,
              height: 160.w,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                  border: Border.all(
                      color: Colors.white.withValues(alpha: 0.5),
                      strokeAlign: BorderSide.strokeAlignOutside),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(14.r),
                    bottomRight: Radius.circular(14.r),
                    topRight: AmorTraService.ar ? Radius.circular(0) : Radius.circular(14.r),
                    topLeft: AmorTraService.ar ? Radius.circular(14.r) : Radius.circular(0),
                  )),
              child: Stack(
                children: [
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: model.giftImg ?? model.content ?? '',
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return const ImagePlaceholder();
                      },
                      placeholder: (context, url) {
                        return const ImagePlaceholder();
                      },
                    ),
                  ),
                  //未解锁
                  if (UserService.to.isVip == false && model.giftImg == null)
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(14.r),
                          bottomRight: Radius.circular(14.r),
                          topRight: AmorTraService.ar ? Radius.circular(0) : Radius.circular(14.r),
                          topLeft: AmorTraService.ar ? Radius.circular(14.r) : Radius.circular(0),
                        ),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                          blendMode: BlendMode.src,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.5),
                            ),
                            child: Column(
                              children: [
                                20.verticalSpaceFromWidth,
                                Image.asset(
                                  Assets.assetsImagesSessionTightAstrictIcon,
                                  width: 62.w,
                                ),
                                2.verticalSpaceFromWidth,
                                CommonUtil.gradientText(
                                  text: '${'Naughty Pic'.tr}😈',
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    AppColor.colorsUtil('#F0F4E7'),
                                    AppColor.colorsUtil('#CFD2C6'),
                                  ],
                                  textStyle: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 3.w),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(30.w / 2),
                                    border: Border.all(
                                        color: AppColor.colorsUtil('#F0BE72'), width: 1.w),
                                  ),
                                  // alignment: Alignment.center,
                                  child: Text(
                                    'View'.tr,
                                    style: TextStyle(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        color: AppColor.colorsUtil('#F0BE72')),
                                  ),
                                ),
                                13.verticalSpaceFromWidth,
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  if (model.contentCategory != 'SFW' && model.giftImg == null)
                    Align(
                      alignment: Alignment.topRight,
                      child: Image.asset(
                        model.contentCategory == 'NSFW'
                            ? Assets.assetsImagesImageMsgTagNsfw
                            : Assets.assetsImagesImageMsgTagNsfw,
                        width: 48.w,
                      ),
                    ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
