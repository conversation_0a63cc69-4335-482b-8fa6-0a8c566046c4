import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'session_message_action_cue.dart';
import 'session_message_content.dart';
import 'session_message_image.dart';
import 'session_message_intro.dart';
import 'session_message_lock.dart';
import 'session_message_tips.dart';
import 'session_message_video.dart';
import 'session_message_voice.dart';

class SessionMessageWidget extends StatelessWidget {
  // 消息
  final SessionModel chatMessageModel;
  // 上一条消息
  final SessionModel? lastModel;
  final int listIndex;
  final bool testModel;
  const SessionMessageWidget({
    super.key,
    required this.listIndex,
    required this.chatMessageModel,
    this.lastModel,
    this.testModel = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      //每条消息上下间距
      padding: REdgeInsets.symmetric(vertical: 10),
      child: _chatContent(),
    );
  }

  Widget _chatContent() {
    //简介
    if (chatMessageModel.contentType == 'intro') {
      return SessionMessageIntro(introContent: chatMessageModel.content ?? '');
    }
    //ai生成提示
    if (chatMessageModel.contentType == 'aiGenerTip') {
      return const SessionMessageAiTips();
    }
    //图片
    if (chatMessageModel.contentType == 'image' ||
        (chatMessageModel.contentType == 'clothe' && chatMessageModel.giftImg != null)) {
      return ChatMessageImage(model: chatMessageModel);
    }
    //语音消息 不含文字
    if (chatMessageModel.contentType == 'audio' && AppService.audit == false) {
      return SessionMessageVoice(chatMessageModel: chatMessageModel, listIndex: listIndex);
    }
    //视频消息
    if (chatMessageModel.contentType == 'video' && AppService.audit == false) {
      return ChatMessageVideoWidget(chatMessageModel: chatMessageModel);
    }
    //灵感提示消息
    if (chatMessageModel.contentType == 'msgTip') {
      return ChatMessageActionCue(chatMessageModel: chatMessageModel);
    }
    if (chatMessageModel.contentType == 'text' ||
        chatMessageModel.contentType == 'sounds' ||
        chatMessageModel.contentType == 'gift' ||
        chatMessageModel.contentType == 'clothe') {
      //加密消息
      if (chatMessageModel.unlock == 0 &&
          UserService.to.isVip == false &&
          AppService.audit == false) {
        return ChatMessageLockContent(chatMessageModel: chatMessageModel);
      }
      return ChatMessageContent(
        chatMessageModel: chatMessageModel,
        listIndex: listIndex,
        lastModel: lastModel,
        showVoice: chatMessageModel.contentType == 'sounds',
      );
    } else if (chatMessageModel.contentType == 'await') {
      return _awaitReplyWidget();
    }
    return Container();
  }

  //等待回复
  Widget _awaitReplyWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _messageContainer(
          child: Lottie.asset('assets/lottie/session_await_reply.json'),
          padding: REdgeInsets.symmetric(horizontal: 15),
          margin: REdgeInsets.only(top: 0),
        )
      ],
    );
  }

  //消息容器
  Widget _messageContainer(
      {required Widget child,
      required EdgeInsetsGeometry padding,
      required EdgeInsetsGeometry margin}) {
    return Opacity(
      opacity: 1,
      child: Container(
        width: 60.w,
        height: 40.w,
        decoration: BoxDecoration(
          color: AppColor.colorsUtil('#1F1F21').withValues(alpha: 0.94),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(chatMessageModel.msgType == 1 ? 16.r : 4.r),
            topRight: Radius.circular(16.r),
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(chatMessageModel.msgType == 1 ? 4.r : 16.r),
          ),
        ),
        padding: padding,
        margin: margin,
        child: child,
      ),
    );
  }
}
