import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../controller/controller.dart';
import 'session_message_rating.dart';

class ChatMessageWidgets {
//消息顶部提示（包含敏感词等）
  static Widget messageTip({required String text, required double top}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 30.w,
          margin: REdgeInsets.only(top: top),
          padding: REdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppColor.colorsUtil('#F0F0F0'),
            borderRadius: BorderRadius.circular(30.w / 2),
          ),
          alignment: Alignment.center,
          child: Text(
            text,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColor.secondaryText,
            ),
          ),
        )
      ],
    );
  }

//消息评分
  static Widget oprationButtonList({
    required String msgId,
    String? content,
    required int score,
  }) {
    if (SessionPageController.to.state.msgRating.value == 0) {
      SessionPageController.to.state.msgRating.value = score;
      if (SessionPageController.to.state.msgRating.value > 0) {
        //已经评过就只展示文本
        SessionPageController.to.state.msgRatingShow.value = false;
      }
    }
    return Container(
      color: Colors.transparent,
      child: Obx(
        () => AnimatedCrossFade(
          firstChild: ChatMessageRatingBar(
            initialRating: SessionPageController.to.state.msgRating.value.toDouble(),
            enable: true,
            onRatingUpdate: (rating) {
              SessionPageController.to
                  .sessionScore(msgId: msgId, score: rating.toInt(), content: content ?? '');
            },
          ),
          secondChild: Text(
            ' ${'Thx for rating'.tr} 😘',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.primaryText.withValues(alpha: 0.8),
            ),
          ),
          crossFadeState: SessionPageController.to.state.msgRatingShow.value
              ? CrossFadeState.showFirst
              : CrossFadeState.showSecond,
          duration: const Duration(milliseconds: 200),
        ),
      ),
    );
  }

  //语音消息 播放按钮
  static Widget voiceMsgPlayButton({
    required SessionModel chatMessageModel,
    required int listIndex,
  }) {
    // String? voiceUrl = chatMessageModel.contentAnalysis ?? '';
    return GestureDetector(
      onTap: () {
        SessionPageController.to.playVoiceRequest(index: listIndex);
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 25.w,
        padding: REdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: AmorTraService.ar ? Radius.circular(25.w / 2) : Radius.circular(4.r),
            topRight: AmorTraService.ar ? Radius.circular(4.r) : Radius.circular(25.w / 2),
            bottomRight: Radius.circular(25.w / 2),
            bottomLeft: Radius.circular(25.w / 2),
          ),
          gradient: chatMessageModel.contentCategory != 'SFW'
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: chatMessageModel.contentCategory == 'NSFW'
                      ? [AppColor.colorsUtil('#FF68B9'), AppColor.colorsUtil('#FF3D62')]
                      : [AppColor.colorsUtil('#0085BD'), AppColor.colorsUtil('#00B0F9')],
                )
              : null,
          color: AppColor.colorsUtil('#666768'),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            //生成或下载中
            SessionPageController.to.voicePlay.underWayList.contains(chatMessageModel.msgId)
                ? SizedBox(
                    height: 13.w,
                    width: 13.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primaryText),
                      backgroundColor: AppColor.primaryText.withValues(alpha: 0.2),
                    ),
                  )
                : Obx(
                    () => SessionWidgetsController.to.state.voicesPlayIndex == listIndex
                        ? SizedBox(
                            height: 13.w,
                            width: 17.w,
                            child: Lottie.asset('assets/lottie/session_voice_play.json',
                                animate: true),
                          )
                        : Image.asset(
                            Assets.assetsImagesSessionPagePlay,
                            width: 12.w,
                            height: 12.w,
                          ),
                  ),

            SessionPageController.to.voicePlay.underWayList.contains(chatMessageModel.msgId) ||
                    chatMessageModel.duration == null
                ? 0.horizontalSpace
                : 8.horizontalSpace,
            Text(
              SessionPageController.to.voicePlay.underWayList.contains(chatMessageModel.msgId) ||
                      chatMessageModel.duration == null
                  ? ''
                  : '${chatMessageModel.duration}″',
              style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColor.primaryText,
                  height: 1.2,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}
