import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:get/get.dart';

class SessionPageState {
  //发音人、顶部标题、头像
  String sessionNo = '';
  String defaultVoice = '';
  String sessionTitle = '';
  String? sessionAvatarUrl;
  String? modelIntro;
  //是否在等待AI回复
  var awaitReply = false.obs;
  var messageList = <SessionModel>[].obs;
  //收到的最新消息队列
  List<SessionModel> newMsgContent = [];
  //用于拼接socket返回的文字
  StringBuffer socketText = StringBuffer();
  //逐一展示文字时的下标
  int showTextIndex = 0;
  //逐一展示文字时的消息ID
  String? showTextMsgId;
  bool socketReceiveEnd = false;
  //分页
  int page = 1;
  //是否点赞
  var likeModel = false.obs;
  //背景图片
  var bgImg = ''.obs;
  //模型ID
  int? modelId;
  //评分
  var msgRating = 0.obs;
  //展示评分还是文本 默认展示评分 评分完成后展示文本
  var msgRatingShow = true.obs;
  var animateString = "".obs;
  //nsfw开关状态
  List contentCategorys = [];
  //当前选中的是nsfw、sfw、bdsm
  String contentCategory = 'SFW';
  //是否展示nsfw开关
  bool displayNsfw = false;
  //设备是否支持自定义震动
  bool? vibrationEnable;
  //是否需要解锁角色
  var lockModel = 1; //1 :不需要  2：需要解锁
  //是否已经解锁
  var unlock = false.obs; //默认未解锁
  //看广告解锁角色倒计时
  var adUnlockCountDown = 0.obs;
  //倒计时最大时长
  int maxAdUnlockCountDown = 1800;
  //是否展示解锁动画
  var showUnlockAnim = false.obs;
  //解锁需要的宝石数量
  int unlockGems = 0;
  //是否已经弹出顶部宝石提示
  bool showTopGemTip = false;
  //是否弹出设置基本信息
  bool showSetBaseInfo = false;
  //排队时间
  int maxServiceWaitSeconds = 1000;
  //文字展示速度
  int contentDisplayRate = 10;
  //是否弹出APP评分
  bool isShowAppScore = false;
  //展示undress弹窗需要的消息次数
  int undressShowCount = 5;
  //是否展示视频菜单
  var showVideoMenu = false.obs;
  String? videoGifUrl;
  //角色相册
  var photos = [].obs;
  //展开、收起相册
  var photosExpanded = true.obs;
  //等级信息
  var sessionLevelInfo = {}.obs;
  //是否是首次启动进入聊天
  bool firstLaunchOpenChat = false;
  //消息是否展示翻译按钮（自动翻译时不展示、设备语言是英语时不展示）
  bool displayMsgTrans = true;
}
