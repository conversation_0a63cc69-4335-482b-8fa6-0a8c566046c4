import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/inner_models/model_list_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/amors/widgets/clothing_list/controller.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class InnerPageController extends GetxController with GetSingleTickerProviderStateMixin {
  static InnerPageController get to => Get.find();
  late TabController tabController;
  List<EasyRefreshController> refreshControllerList = [];
  List<List<ModelListModel>> allModelList = [];

  List<int> pageList = [];
  int tabbarIndex = 0;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(
      () async {
        if (tabController.animation!.value == tabController.index) {
          tabbarIndex = tabController.index;
          if (allModelList.elementAt(tabbarIndex).isEmpty) {
            getModelList(index: tabbarIndex);
          }
        }
      },
    );
    for (var i = 0; i < 2; i++) {
      refreshControllerList
          .add(EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true));
      pageList.add(1);
      allModelList.add([]);
    }
    getModelList(index: tabbarIndex);
  }

  getModelList({required int index, bool refresh = false}) async {
    if (UserService.to.isLogin == false) {
      return;
    }
    List<ModelListModel> list = [];
    //我聊过的
    if (index == 0) {
      list = await InnerApis.chatted(
        pageList.elementAt(0),
        cacheCallBack: (cache) {
          //刷新时不使用缓存
          if (refresh == false) {
            allModelList.elementAt(0).assignAll(cache);
            update(['inner_model_index_$index']);
          }
        },
      );
    }
    //我喜欢的
    if (index == 1) {
      list = await InnerApis.likedModelList(
        pageList.elementAt(1),
        cacheCallBack: (cache) {
          //刷新时不使用缓存
          if (refresh == false) {
            allModelList.elementAt(1).assignAll(cache);
            update(['inner_model_index_$index']);
          }
        },
      );
    }
    pageList.elementAt(index) == 1
        ? allModelList.elementAt(index).assignAll(list)
        : allModelList.elementAt(index).addAllIf(list.isNotEmpty, list);
    if (list.isEmpty) {
      refreshControllerList.elementAt(index).finishLoad(IndicatorResult.noMore);
    }
    update(['inner_model_index_$index']);
  }

  //刷新
  refreshRequest() async {
    pageList.replaceRange(tabbarIndex, tabbarIndex + 1, [1]);
    await getModelList(index: tabbarIndex, refresh: true);

    refreshControllerList.elementAt(tabbarIndex).finishRefresh();
    refreshControllerList.elementAt(tabbarIndex).resetFooter();
  }

  //加载更多
  loadMoreRequest() {
    pageList.replaceRange(tabbarIndex, tabbarIndex + 1, [pageList.elementAt(tabbarIndex) + 1]);
    // refreshControllerList.elementAt(tabbarIndex).finishLoad(IndicatorResult.noMore);
    getModelList(index: tabbarIndex);
  }

  //置顶、喜欢
  oprationAction(int index) async {
    //震动
    if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true) {
      HapticFeedback.mediumImpact();
    }
    ModelListModel model = allModelList.elementAt(tabbarIndex).elementAt(index);
    //喜欢
    if (tabbarIndex == 1) {
      ModelListModel model = allModelList.elementAt(1).elementAt(index);
      if (model.hasLike == true) {
        model.hasLike = false;
      } else {
        model.hasLike = true;
      }
      // allModelList.elementAt(1).replaceRange(index, index + 1, [model]);
      allModelList.elementAt(1).removeAt(index);
      update(['inner_model_index_1']);
      await AmorsApis.doLike(modelId: model.modelId);
      //刷新缓存
      InnerApis.likedModelList(1);
      //刷新首页
      if (Get.isRegistered<AmorsPageController>() == true) {
        AmorsPageController.to.likeOperation(modelId: model.modelId ?? 0);
      }
      if (Get.isRegistered<HomeClothListWidgetController>() == true) {
        HomeClothListWidgetController.to.likeOperation(modelId: model.modelId ?? 0);
      }
    } else {
      //置顶
      allModelList.elementAt(0).removeAt(index);
      if (model.top == true) {
        //取消置顶
        allModelList.elementAt(0).add(model);
      } else {
        allModelList.elementAt(0).insert(0, model);
      }
      model.top = !(model.top ?? false);
      update(['inner_model_index_0']);
      await InnerApis.chattedTop(modelId: model.modelId ?? 0, top: model.top!);
    }
  }

  //跳转聊天
  toChat(ModelListModel model) async {
    if (model.modelId == null) {
      return;
    }
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: model.modelId!);
    if (params != null && params['sessionNo'] != null) {
      Get.toNamed(Routes.session, arguments: params['sessionNo']);
      ReportUtil.reportEvents(page: ReportUtil.inner, action: ReportUtil.select);
    }
  }

  //用户切换时刷新
  refreshPage() {
    pageList.clear();
    allModelList.clear();
    for (var i = 0; i < 2; i++) {
      pageList.add(1);
      allModelList.add([]);
      update(['inner_model_index_$i']);
    }
    if (UserService.to.isLogin) {
      getModelList(index: tabbarIndex);
    }
  }

  //刷新某一个列表
  refreshIndexTabbarList({required int index}) async {
    pageList.replaceRange(index, index + 1, [1]);
    await getModelList(index: index);
    refreshControllerList.elementAt(tabbarIndex).finishRefresh();
    refreshControllerList.elementAt(tabbarIndex).resetFooter();
  }

  @override
  void onClose() {
    super.onClose();
    tabController.dispose();
    refreshControllerList.map((e) => e.dispose());
  }
}
