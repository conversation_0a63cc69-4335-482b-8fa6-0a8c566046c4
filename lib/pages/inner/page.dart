import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../common/widgets/custom_widget/custom_tabbar.dart' as customtabbar;
import 'widgets/inner_page_list_widget.dart';

class InnerPage extends GetView<InnerPageController> {
  const InnerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InnerPageController>(
      init: InnerPageController(),
      builder: (controller) => Stack(
        children: [
          Positioned(
            top: 0,
            child: Image.asset(
              Assets.assetsImagesInnerTopBg,
              width: 1.sw,
            ),
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: AppBar(
              titleSpacing: 52.w,
              title: tabbar(
                tabs: [
                  Tab(text: 'Chatted'.tr),
                  Tab(text: 'Liked'.tr),
                ],
              ),
              backgroundColor: Colors.transparent,
            ),
            body: TabBarView(
              controller: controller.tabController,
              children: List.generate(
                2,
                (index) => KeepAliveWrapper(child: InnerPageListViewWidget(tabbarIndex: index)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget tabbar({required List<Widget> tabs}) {
    return customtabbar.TabBar(
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      // isScrollable: true,
      // padding: EdgeInsets.all(0),
      controller: controller.tabController,
      indicator: const BoxDecoration(
        image: DecorationImage(
          fit: BoxFit.contain,
          image: AssetImage(Assets.assetsImagesInnerTabbarIndex),
        ),
      ),
      indicatorPadding: const EdgeInsets.fromLTRB(0, 26, 0, 12),
      labelColor: AppColor.colorsUtil('#F0BE72'),
      unselectedLabelColor: AppColor.primaryText,
      labelStyle: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w700,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.normal,
      ),
      tabs: tabs,
    );
  }
}
