import 'package:amor_app/common/models/inner_models/model_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InnerPageListViewWidget extends GetView<InnerPageController> {
  final int tabbarIndex;
  const InnerPageListViewWidget({super.key, required this.tabbarIndex});

  @override
  Widget build(BuildContext context) {
    return EasyRefresh.builder(
      controller: controller.refreshControllerList.elementAt(tabbarIndex),
      onRefresh: () => controller.refreshRequest(),
      onLoad: () => controller.loadMoreRequest(),
      childBuilder: (context, physics) {
        return GetBuilder<InnerPageController>(
          id: 'inner_model_index_$tabbarIndex',
          builder: (controller) {
            return ListView.separated(
              itemBuilder: listviewItemBuilder,
              physics: physics,
              separatorBuilder: (context, index) => 16.verticalSpaceFromWidth,
              itemCount: controller.allModelList.elementAt(tabbarIndex).isEmpty
                  ? 1
                  : controller.allModelList.elementAt(tabbarIndex).length,
            );
          },
        );
      },
    );
  }

  Widget listviewItemBuilder(context, index) {
    if (controller.allModelList.elementAt(tabbarIndex).isEmpty) {
      return const CustomEmptyWidget();
    }

    ModelListModel model = controller.allModelList.elementAt(tabbarIndex).elementAt(index);
    return InkWell(
      onTap: () {
        controller.toChat(model);
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Container(
          height: 100.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            color: AppColor.colorsUtil('#1F1A21'),
          ),
          padding: EdgeInsets.only(left: 16.w, right: 10.w),
          child: Row(
            children: [
              ClipOval(
                child: CachedNetworkImage(
                  imageUrl: model.cover ?? '',
                  memCacheWidth: Get.width.toInt(),
                  errorWidget: (context, url, error) {
                    return Container();
                  },
                  width: 68.w,
                  height: 68.w,
                  fit: BoxFit.cover,
                  placeholder: (context, url) {
                    return SizedBox(
                      width: 68.w,
                      height: 68.w,
                    );
                  },
                ),
              ),
              10.horizontalSpace,
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Text(
                            model.modelName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.white,
                                height: 18.sp / 16.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        4.horizontalSpace,
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ImageBtn(
                                iconSting: tabbarIndex == 0
                                    ? (model.top == true
                                        ? Assets.assetsImagesInnerListTopLight
                                        : Assets.assetsImagesInnerListTop)
                                    : model.hasLike == true
                                        ? Assets.assetsImagesInnerListLiked
                                        : Assets.assetsImagesInnerListLike,
                                onPressed: () {
                                  controller.oprationAction(index);
                                },
                                width: 28.w,
                                height: 28.w),
                            if (tabbarIndex == 1 && model.clothe == true)
                              Padding(
                                padding: EdgeInsets.only(
                                  left: AmorTraService.ar ? 0 : 8.w,
                                  right: AmorTraService.ar ? 8.w : 0,
                                ),
                                child: Image.asset(
                                  Assets.assetsImagesListClothIcon,
                                  width: 20.w,
                                ),
                              ),
                          ],
                        )
                      ],
                    ),
                    8.verticalSpaceFromWidth,
                    Text(
                      model.intro ?? '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
