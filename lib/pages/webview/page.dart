import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'controller.dart';

class WebviewPage extends GetView<WebviewPageController> {
  const WebviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: const CustomBackButton(),
        title: Obx(
          () => Text(
            controller.webTitle.value,
          ),
        ),
        elevation: 0,
      ),
      backgroundColor: AppColor.mainBg,
      body: WebViewWidget(controller: controller.webCtl),
    );
  }
}
