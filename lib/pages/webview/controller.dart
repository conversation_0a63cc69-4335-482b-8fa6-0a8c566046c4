import 'package:amor_app/common/utils/utils.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewPageController extends GetxController {
  var width = 0.0.obs;
  var showBar = true.obs;
  var webTitle = ''.obs;

  Map<String, String> parameters = {};
  late WebViewController webCtl;
  @override
  void onInit() async {
    super.onInit();
    parameters = Get.parameters as Map<String, String>;
    // setcookie();
    webCtl = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (url) async {
            webTitle.value = (await webCtl.getTitle())!;
          },
          /*
          onNavigationRequest: (request) {
            if (request.url.endsWith('/knows')) {
              // 跳到原生页面
              return NavigationDecision.prevent; //阻止导航
            } else {
              // 继续原来的请求
              return NavigationDecision.navigate;
            }
          },
          onProgress: (progress) {},
          */
        ),
      );
    /*
      ..addJavaScriptChannel('invite', onMessageReceived: (message) {
        debugPrint('js方法${message.message}');
        webCtl.runJavaScript('AndroidCallback("休息休息")');
      });
    */
    webCtl.loadRequest(Uri.parse(parameters['url']!));
  }

  setcookie() async {
    WebViewCookieManager cookieManager = WebViewCookieManager();
    if (UserService.to.isLogin == true) {
      // 设置 cookie
      cookieManager.setCookie(
        WebViewCookie(name: 'token', value: UserService.to.token!, domain: parameters['url']!),
      );
    }
  }
}
