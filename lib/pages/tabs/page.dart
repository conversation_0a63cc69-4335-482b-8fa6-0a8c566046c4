import 'dart:ui';

import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/amors/page.dart';
import 'package:amor_app/pages/inner/page.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:amor_app/pages/klone/page.dart';
import 'package:amor_app/pages/profile/page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'controller.dart';

class TabsPage extends StatefulWidget {
  const TabsPage({super.key});

  @override
  State<TabsPage> createState() => _TabsPageState();
}

class _TabsPageState extends State<TabsPage> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 路由订阅
    AppRouteObserver().routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void didPushNext() {
    super.didPushNext();
    if (Get.isRegistered<KlonePageController>() && TabsController.to.page == 1) {
      KlonePageController.to.stopPlay();
    }
    //取消首页停留计时
    TabsController.to.cancelHomeStayTimer();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (Get.isRegistered<KlonePageController>() && TabsController.to.page == 1) {
      KlonePageController.to.refreshRequest();
    }
    if (TabsController.to.page == 0) {
      //首页停留计时
      TabsController.to.startHomeStayTimer();
    }
  }

  @override
  void dispose() {
    super.dispose();
    // 取消路由订阅
    AppRouteObserver().routeObserver.unsubscribe(this);
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: GetBuilder<TabsController>(
        builder: (controller) => Scaffold(
          extendBody: true,
          resizeToAvoidBottomInset: false,
          body: _buildPageView(controller),
          bottomNavigationBar: _buildBottomNavigationBar(controller),
        ),
      ),
    );
  }

  // 内容页
  Widget _buildPageView(TabsController controller) {
    return Obx(
      () => LazyLoadIndexedStack(
        index: controller.page,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: CommonUtil.bottomBarHeight() + 56),
            child: const AmorsPage(),
          ),
          const KlonePage(),
          const InnerPage(),
          Padding(
            padding: EdgeInsets.only(bottom: CommonUtil.bottomBarHeight() + 56),
            child: const ProfilePage(),
          ),
        ],
      ),
    );
  }

  // 底部导航
  Widget _buildBottomNavigationBar(TabsController controller) {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 10.0,
          sigmaY: 10.0,
        ),
        child: Obx(
          () => BottomNavigationBar(
            elevation: 0,
            backgroundColor: AppColor.colorsUtil('#1E1B24').withValues(alpha: 0.5),
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: Image.asset(
                  Assets.assetsImagesTabAmors0,
                  width: 26.w,
                  // excludeFromSemantics: true, //去除图片语义
                  // gaplessPlayback: true, //重新加载图片的过程中，原图片的展示是否保留
                ),
                label: 'Amors',
                activeIcon: Image.asset(
                  Assets.assetsImagesTabAmors1,
                  width: 26.w,
                ),
                // Lottie.asset('assets/lottieAnimation/tabbar/characters/characters.json',
                //     animate: true, repeat: false, width: 36.w, height: 32.w),
              ),
              BottomNavigationBarItem(
                label: 'Clone'.tr,
                icon: Image.asset(
                  Assets.assetsImagesTabKlone0,
                  width: 26.w,
                ),
                activeIcon: Image.asset(
                  Assets.assetsImagesTabKlone1,
                  width: 26.w,
                ),
              ),
              BottomNavigationBarItem(
                label: 'Inner'.tr,
                icon: Image.asset(
                  Assets.assetsImagesTabChats0,
                  width: 26.w,
                ),
                activeIcon: Image.asset(
                  Assets.assetsImagesTabChats1,
                  width: 26.w,
                ),
              ),
              BottomNavigationBarItem(
                label: 'Profile'.tr,
                icon: Image.asset(
                  Assets.assetsImagesTabProfile0,
                  width: 26.w,
                ),
                activeIcon: Image.asset(
                  Assets.assetsImagesTabProfile1,
                  width: 26.w,
                ),
              ),
            ],
            currentIndex: controller.page,
            type: BottomNavigationBarType.fixed,
            onTap: controller.handleNavBarTap,
            selectedFontSize: 10.sp,
            selectedItemColor: AppColor.colorsUtil('#F0BE72'),
            unselectedFontSize: 10.sp,
            unselectedItemColor: AppColor.colorsUtil('#64616B'),
          ),
        ),
      ),
    );
  }
  /*
  Widget tabbarButton(
      {required VoidCallback onTap,
      required bool selected,
      required String title,
      required String image,
      String? lottieFilePath,
      String? selectedImage,
      int showBadgeCount = 0}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            selected
                ? lottieFilePath == null
                    ? Image.asset(selectedImage!, width: 26.w)
                    : Lottie.asset(lottieFilePath,
                        animate: true, repeat: false, width: 26.w, height: 26.w)
                : Image.asset(image, width: 26.w),
            1.verticalSpace,
            Text(
              title,
              style: TextStyle(
                fontSize: 10.sp,
                color: selected ? AppColor.colorsUtil('#7A35F5') : AppColor.colorsUtil('#64616B'),
              ),
            )
          ],
        ),
      ),
    );
  }
  */
}
