import 'dart:async';
import 'dart:math';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class TabsController extends FullLifeCycleController with FullLifeCycleMixin {
  static TabsController get to => Get.find();
  final _page = 0.obs;
  set page(value) => _page.value = value;
  get page => _page.value;
  //首页停留激活来电 计时
  Timer? homeStayTimer;
  //来电次数统计
  int callCount = 0;
  //来电时间
  int callTime = 0;
  // tab栏点击
  void handleNavBarTap(int index) async {
    // 如果同一页面，不做处理，防止重绘
    if (index == page) {
      return;
    }
    //震动
    if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true) {
      HapticFeedback.selectionClick();
    }
    if (index == 0) {
      if (Get.isRegistered<AmorsPageController>() == false) {
        Get.put(AmorsPageController());
      }
      ReportUtil.reportViews(page: ReportUtil.amor, action: ReportUtil.view);
      ReportUtil.reportViews(
          page: page == 1
              ? ReportUtil.clone
              : page == 2
                  ? ReportUtil.inner
                  : ReportUtil.profile,
          action: ReportUtil.go,
          value: ReportUtil.amor);
      Analytics().logEvent(Analytics.view, screen: Analytics.pageAmors);
    }
    if (index == 1) {
      if (Get.isRegistered<KlonePageController>() == false) {
        Get.put(KlonePageController());
      }
      KlonePageController.to.showAmorGemsReceiveDialog();
      ReportUtil.reportViews(page: ReportUtil.clone, action: ReportUtil.view);
      ReportUtil.reportViews(
          page: page == 0
              ? ReportUtil.amor
              : page == 2
                  ? ReportUtil.inner
                  : ReportUtil.profile,
          action: ReportUtil.go,
          value: ReportUtil.clone);
    }
    if (index == 2) {
      if (Get.isRegistered<InnerPageController>() == false) {
        Get.put(InnerPageController());
      }
      ReportUtil.reportViews(page: ReportUtil.inner, action: ReportUtil.view);
      ReportUtil.reportViews(
          page: page == 0
              ? ReportUtil.amor
              : page == 1
                  ? ReportUtil.clone
                  : ReportUtil.profile,
          action: ReportUtil.go,
          value: ReportUtil.inner);
    }
    //我的
    if (index == 3) {
      if (Get.isRegistered<ProfilePageController>() == false) {
        Get.put(ProfilePageController());
      }
      Map<String, dynamic>? userLoginInfo =
          Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
      if (UserService.to.isLogin && userLoginInfo?['register'] == true) {
        await Get.toNamed(Routes.register);
      }
      UserService.to.getUserInfo();
      ProfilePageController.to.showSubscribeSuccess();
      ReportUtil.reportViews(
          page: page == 0
              ? ReportUtil.amor
              : page == 1
                  ? ReportUtil.clone
                  : ReportUtil.inner,
          action: ReportUtil.go,
          value: ReportUtil.profile);
      ReportUtil.reportViews(page: ReportUtil.profile, action: ReportUtil.view);
    }
    if (index == 0) {
      //首页停留计时
      startHomeStayTimer();
    } else {
      //取消首页停留计时
      cancelHomeStayTimer();
    }
    page = index;
  }

  @override
  void onReady() {
    super.onReady();
    //通过通知打开app 跳转聊天
    notificationToChat();
    initData();
    Analytics().logEvent(Analytics.view, screen: Analytics.pageAmors);
  }

  //通过通知打开app 跳转聊天
  notificationToChat() {
    if (AppService.sp.get(spNotificationSessionNo) != null) {
      AppService.sp.remove(spNotificationSessionNo);
      Get.offNamedUntil(Routes.session, (route) => route.settings.name == Routes.tabs,
          arguments: AppService.sp.get(spNotificationSessionNo));
    }
  }

  Future<void> initData() async {
    ReportUtil.reportViews(page: ReportUtil.opening, action: ReportUtil.view);
    int curDate = CommonUtil.currentTimeMillis();
    int date = (Get.find<SPService>().get(spDailyShowSale) ?? 0) as int;
    bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
    //每日一次 弹出购买页
    if (!isSameDay &&
        Get.find<SPService>().get(spUserVipInfo) != true &&
        Get.currentRoute == Routes.tabs) {
      //缓存成功才弹出购买页
      if (CacheSaleService.storeSsvipConfigList.isNotEmpty) {
        PurchaseSheet.show(page: '*');
        Get.find<SPService>().set(spDailyShowSale, curDate);
        Analytics().logEvent(
          Analytics.view,
          sourceEvent: null,
          sourceChar: null,
          sourceScreen: Analytics.pageAmors,
          screen: Analytics.pageHot,
        );
      } else {
        Loading.toast(
            GetPlatform.isAndroid ? 'GooglePlay Error,Try later!' : 'AppStore Error,Try later!',
            duration: const Duration(milliseconds: 1000));
      }
    } else {
      startHomeStayTimer();
    }
    //记录每日登录
    if (UserService.to.isLogin) {
      CommonApis.dailyReport();
    }
    updateTranslateLanguage();
  }

  //每次打开APP更新自动翻译的目标语言
  updateTranslateLanguage() {
    if (UserService.to.isAutoTranslation && Get.find<SPService>().get(spUserVipInfo) == true) {
      ProfileApis.profileUpdate(params: {
        'autoTranslate': true,
        'targetLanguage': (Get.deviceLocale ?? const Locale('en', 'US')).toString(),
        'sourceLanguage': 'en_US'
      }, loading: false);
    }
  }

  //首页停留开始计时
  void startHomeStayTimer() {
    if (AppService.audit == true) {
      return;
    }
    // int a = CommonUtil.timeDifference(callTime, CommonUtil.currentTimeMillis(), unit: 'seconds');
    // print('时间相差$a秒');
    // print('已经展示$callCount次');
    if (UserService.to.isVip == false &&
        callCount < 2 &&
        (callTime == 0 ||
            CommonUtil.timeDifference(callTime, CommonUtil.currentTimeMillis(), unit: 'seconds') >
                180)) {
      homeStayTimer = Timer(Duration(seconds: 5), () {
        cancelHomeStayTimer();
        getCallLogs();
      });
    }
  }

  //请求来电信息
  getCallLogs() async {
    int attempts = 0; // 尝试获取女性角色的次数
    if (AmorsPageController.to.state.dataList.isEmpty ||
        Get.currentRoute.startsWith(Routes.tabs) == false) {
      return;
    }
    //增加次数
    callCount++;
    //设置时间
    callTime = CommonUtil.currentTimeMillis();
    //先设置一个初始角色
    AmorsFeedModel model = AmorsPageController.to.state.dataList.first;
    //随机获取性别为女的角色
    do {
      int randomIndex = Random().nextInt(AmorsPageController.to.state.dataList.length);
      model = AmorsPageController.to.state.dataList[randomIndex];
    } while (model.gender == 1 && attempts++ < 20);
    //创建会话ID
    Map? result = await AmorsApis.createSession(modelId: model.modelId ?? 0, loding: false);
    if (result != null && result['sessionNo'] != null) {
      //获取会话配置
      Map<String, dynamic>? config = await SessionApis.getSessionConfig(
          sessionNo: result['sessionNo'], cacheCallBack: (Map<String, dynamic>? cache) {});
      if (config != null && await SttUtil().checkAvailable() == true) {
        //缓存背景
        await DefaultCacheManager().getSingleFile(config['bgImg'] ?? '');
        if (Get.currentRoute.startsWith(Routes.tabs) && page == 0) {
          Analytics().logEvent(Analytics.taicallyou);
          Get.toNamed(Routes.voiceCall, parameters: {
            'voiceType': '1',
            'modelId': '${model.modelId ?? 0}',
            'sessionNo': result['sessionNo'],
            'avatarUrl': model.cover ?? '',
            'nickname': model.modelName ?? '',
            'bgImgUrl': config['bgImg'] ?? '',
            'defaultVoice': config['voice'] ?? ''
          });
        }
      }
    }
  }

  //首页停留取消计时
  void cancelHomeStayTimer() {
    if (homeStayTimer != null) {
      homeStayTimer!.cancel();
      homeStayTimer = null;
    }
  }

  @override
  void onClose() {
    super.onClose();
    cancelHomeStayTimer();
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {}

  @override
  void onResumed() {
    // AppService.isResumed = true;
    // if (Get.isRegistered<ChatListController>() == true) {
    //   ChatListController.to.refreshRequest();
    // }
    // //判断是否在会话页面在链接socket、
    // if (Get.isRegistered<ChatPageController>() == true) {
    //   ChatPageController.to.socket.reconnection();
    // }
    updateTranslateLanguage();
  }

  @override
  void onHidden() {}
}
