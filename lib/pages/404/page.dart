import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotFound extends StatefulWidget {
  const NotFound({super.key});

  @override
  State<NotFound> createState() => _NotFoundState();
}

class _NotFoundState extends State<NotFound> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          margin: EdgeInsets.only(top: 100.w),
          height: 136.w,
          // child: Image.asset(
          //   Assets.assetsImages20Logo,
          //   fit: BoxFit.contain,
          // ),
        ),
      ),
    );
  }
}
