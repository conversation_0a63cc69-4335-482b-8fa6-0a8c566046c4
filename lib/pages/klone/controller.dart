import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

import 'widgets/dialog_widgets/dialog_refresh.dart';

class KlonePageController extends GetxController {
  static KlonePageController get to => Get.find();
  var stepNum = 0.obs;
  int page = 1;
  var modelList = [].obs;
  var loadDone = false.obs;
  var playingVoice = ''.obs;
  late EasyRefreshController refreshController;
  //播放器
  late AudioPlayer voicePlayer;
  @override
  void onInit() {
    super.onInit();
    refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    requestData();
    initPlayer();
  }

  requestData() async {
    if (UserService.to.isLogin == false) {
      loadDone.value = true;
      modelList.clear();
      loadDone.refresh();
      return;
    }
    List list = await CloneApis.cloneList(
      page: page,
      cacheCallBack: (cache) {
        setData(cache);
      },
    );
    setData(list);
  }

  setData(List list) {
    if (page == 1) {
      modelList.assignAll(list);
      refreshController.resetFooter();
    } else {
      modelList.addAll(list);
      list.isEmpty
          ? refreshController.finishLoad(IndicatorResult.noMore)
          : refreshController.finishLoad(IndicatorResult.success);
    }
    loadDone.value = true;
    loadDone.refresh();
  }

  //刷新列表
  refreshRequest() async {
    page = 1;
    await requestData();
    refreshController.finishRefresh();
  }

  //加载更多
  loadMoreRequest() {
    page++;
    requestData();
  }

  //初始化播放器
  initPlayer() {
    voicePlayer = AudioPlayer();
    //监听播放
    voicePlayer.playerStateStream.listen((playState) {
      if (playState.processingState == ProcessingState.completed) {
        playingVoice.value = '';
      }
    });
    voicePlayer.playingStream.listen((event) {
      if (event == false) {
        playingVoice.value = '';
      }
    });
  }

  //播放音频
  playVoice(CloneListModel model) async {
    if (model.greetingUrl == null || model.greetingUrl!.isEmpty) {
      return;
    }
    if (playingVoice.value == model.modelNo) {
      voicePlayer.stop();
      return;
    }
    playingVoice.value = model.modelNo!;
    await voicePlayer.setAudioSource(AudioSource.uri(Uri.parse(model.greetingUrl!)));
    voicePlayer.play();
  }

  //编辑
  editModel(CloneListModel model) {
    if (model.modelProcess!.toUpperCase() == 'COMPLETE') {
      Get.toNamed(Routes.cloneEdit, arguments: model.modelNo);
    } else {
      delete(model);
    }
  }

  //删除
  delete(CloneListModel model) async {
    bool? result = await Get.dialog(
      const CustomDialogWidget(
        title: "System",
        subTitle: 'Deletion will be permanent',
        confirmTitle: 'Do it',
        cancelTitle: 'Cancel',
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      bool delResult = await CloneApis.cloneDel(modelNo: model.modelNo!);
      if (delResult == true) {
        modelList.remove(model);
      }
    }
  }

  //刷新 模型
  cloneRefreshDialog(CloneListModel model) async {
    int? result = await Get.dialog(CloneDialogRefresh(gems: model.refreshGems ?? 1000));
    if (result == 1) {
      cloneRefreshRequest(model: model);
    }
    if (result == 0) {
      /*
      ApplovinUtil.loadRewardedAd(
        adUnitId: ApplovinUtil.rewardedAdUnitIdCloneRefresh,
        refreshModelNo: model.modelNo ?? '',
        callBack: (bool success) {
          if (success) {
            int modelIndex = modelList.indexOf(model);
            model.expireDay = -1;
            modelList.replaceRange(modelIndex, modelIndex + 1, [model]);
          }
        },
      );
      */
    }
  }

  cloneRefreshRequest({required CloneListModel model}) async {
    int modelIndex = modelList.indexOf(model);
    bool success = await CloneApis.cloneRefresh(modelNo: model.modelNo!);
    if (success) {
      model.expireDay = -1;
      modelList.replaceRange(modelIndex, modelIndex + 1, [model]);
      ReportUtil.reportEvents(
          page: ReportUtil.cloneExi, action: ReportUtil.refresh, value: '${model.refreshGems}');
    }
  }

  //点击模型
  itemOnTap(CloneListModel model) {
    if (model.modelProcess!.toUpperCase() == 'COMPLETE') {
      //跳转聊天
      toChat(model);
    } else {
      toCreat(model);
    }
  }

  //跳转聊天
  toChat(CloneListModel model) async {
    if (model.modelId == null) {
      return;
    }
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: model.modelId!);
    if (params != null && params['sessionNo'] != null) {
      Get.toNamed(Routes.session, arguments: params['sessionNo']);
    }
  }

  //跳转创建
  toCreat(CloneListModel model) {
    //设置私人、公开
    if (model.modelProcess!.toUpperCase() == 'CONFIRM') {
      Get.toNamed(Routes.cloneStepThree, arguments: model.modelNo);
    } else {
      //创建
      Get.toNamed(Routes.cloneStepTwo, arguments: model.modelNo);
    }
  }

  //停止播放
  stopPlay() {
    voicePlayer.stop();
  }

  //创建新模型
  creatNewClone() async {
    CloneListModel model = modelList.elementAt(0);
    if (modelList.isNotEmpty && (model.modelProcess ?? '').toUpperCase() != 'COMPLETE') {
      // bool? result = await Get.dialog(
      //   const CustomDialogWidget(
      //     title: "System",
      //     subTitle: 'Deletion will be permanent',
      //     confirmTitle: 'Do it',
      //     cancelTitle: 'Cancel',
      //     confirmColor: '#00B0F9',
      //   ),
      // );
      // if (result != true) {}
      Loading.toast("Can't create a new clone when a draft is in process.");
      return;
    }
    stepNum.value = 0;
    Get.toNamed(Routes.clone, arguments: 'creat');
  }

  @override
  void dispose() {
    super.dispose();
    voicePlayer.dispose();
  }

  //展示自创Amor奖励弹窗
  showAmorGemsReceiveDialog() async {
    if (UserService.to.isLogin == true) {
      int curDate = CommonUtil.currentTimeMillis();
      int date = (Get.find<SPService>().get(spKloneDialog) ?? 0) as int;
      bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
      if (isSameDay == true) {
        return;
      }
      Get.find<SPService>().set(spKloneDialog, curDate);
      Map? data = await CloneApis.cloneEarnings();
      if (data != null && data['gems'] != null && (data['gems'] as int) > 0) {
        GemsDialog.receiveGemsDialog(
          gemsNum: data['gems'],
          subTitle: 'Earnings From Amors You Created Is Ready.',
          bottomTitle: 'Invite Friends To Earn A Lot More',
          callBack: ({required int result}) async {
            if (result == 0) {
              reciveReward(ad: false);
            } else {
              watchAdReward();
            }
          },
        );
      }
    }
  }

  //看广告获得双倍奖励
  watchAdReward() async {
    /*
    ApplovinUtil.loadInterstitialAd(
      adUnitId: ApplovinUtil.interstitialAdUnitIdClone,
      callBack: (success) {
        reciveReward(ad: success);
      },
    );
    */
  }

  //领取奖励
  reciveReward({required bool ad}) async {
    bool success = await CloneApis.cloneEarningsClaim(ad: ad);
    if (success) {
      GemsAnimation.show();
      Future.delayed(const Duration(milliseconds: 1800), () {
        Get.back();
      });
    }
    // ReportUtil.reportEvents(
    //     page: ReportUtil.earningPop,
    //     action: ad ? ReportUtil.double : ReportUtil.claim,
    //     value: '${data['gems']}');
  }
}
