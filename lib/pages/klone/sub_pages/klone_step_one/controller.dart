import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class KloneStepOnePageController extends GetxController {
  static KloneStepOnePageController get to => Get.find();
  String showRecordVoice = '';
  late TextEditingController textCtl;
  var selectedSex = 3.obs;
  var nickName = ''.obs;
  var selectedImage = false.obs;
  Uint8List? cropImageData;
  Uint8List? bgImageData;

  @override
  void onInit() {
    super.onInit();
    showRecordVoice = Get.parameters['showRecordVoice'] ?? '';
    textCtl = TextEditingController();
    ReportUtil.reportViews(page: ReportUtil.cloneinfo, action: ReportUtil.view);
  }

//随机昵称
  cloneRandomNickname() async {
    //选择性别后才能生成昵称
    if (selectedSex.value != 3) {
      HapticFeedback.mediumImpact();
      String result = await CloneApis.cloneRandomProfile(
          gender: selectedSex.value == 1
              ? -1
              : selectedSex.value == 2
                  ? 1
                  : 0);
      nickName.value = result;
      textCtl.text = result;
    }
  }

  selectImage() async {
    ReportUtil.reportEvents(page: ReportUtil.cloneinfo, action: ReportUtil.addPhoto);
    String? path = await SelectMediaUtil.selectImage(crop: false);
    if (path == null) {
      return;
    }
    selectedImage.value = false;
    final result = await Get.toNamed(Routes.cropCloneImage, parameters: {'imagePath': path});
    if (result != null && result.length == 2) {
      cropImageData = result[0];
      bgImageData = result[1];
      selectedImage.value = true;
    }
  }

  next() async {
    if (nickName.isEmpty || selectedSex.value == 3) {
      return;
    }
    if (cropImageData == null) {
      bool? result = await Get.dialog(
        const CustomDialogWidget(
          title: "System prompt",
          subTitle: 'Move on without a profile photo?',
          confirmTitle: 'Upload',
          cancelTitle: 'Later',
          confirmColor: '#00B0F9',
        ),
      );
      if (result == true) {
        selectImage();
        return;
      }
    }
    String? avatarUrl;
    String? bgImagerUrl;
    if (cropImageData != null) {
      Loading.show();
      avatarUrl = await UploadOss.upload(
          fileBytes: cropImageData, format: 'png', servicePath: ossServicePathClone);
      bgImagerUrl = await UploadOss.upload(
          fileBytes: bgImageData, format: 'png', servicePath: ossServicePathClone);
    }
    String modelNo = CommonUtil.randomLetters(20);
    bool result = await CloneApis.cloneInit(
        avatar: avatarUrl,
        bgImg: bgImagerUrl,
        gender: selectedSex.value == 1
            ? 'UNKNOWN'
            : selectedSex.value == 2
                ? 'MALE'
                : 'FEMALE',
        nickname: nickName.value,
        recordOptions: showRecordVoice == 'true' ? 1 : 0,
        modelNo: modelNo);
    if (result == true) {
      if (avatarUrl == null) {
        ReportUtil.reportEvents(page: ReportUtil.cloneinfo, action: ReportUtil.noPhoto);
      }
      // Get.offNamed(Routes.cloneStepTwo, arguments: modelNo); //替换当前页
      Get.offNamedUntil(Routes.cloneStepTwo, ModalRoute.withName(Routes.tabs), arguments: modelNo);
    }
  }

  @override
  void onClose() {
    super.onClose();
    textCtl.dispose();
    ReportUtil.reportViews(page: ReportUtil.cloneInfo, action: ReportUtil.quit);
  }
}
