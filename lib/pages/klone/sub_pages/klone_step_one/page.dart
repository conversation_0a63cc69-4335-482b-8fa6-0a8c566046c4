import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'controller.dart';

class KloneStepOnePage extends GetView<KloneStepOnePageController> {
  const KloneStepOnePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const CustomBackButton(),
        backgroundColor: Colors.transparent,
      ),
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      backgroundColor: AppColor.mainBg,
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Image.asset(Assets.assetsImagesKloneTopBg),
          ),
          Positioned.fill(
            child: Obx(
              () => controller.selectedImage.value == true
                  ? Opacity(
                      opacity: 0.2,
                      child: Image.memory(
                        controller.bgImageData!,
                        fit: BoxFit.contain,
                      ),
                    )
                  : Container(),
            ),
          ),
          Positioned(
            top: CommonUtil.statusBarHeight(context) + CommonUtil.appBarHeight(),
            left: 0,
            right: 0,
            bottom: 0,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  10.verticalSpaceFromWidth,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Obx(
                        () => sexWidget(
                          selected: controller.selectedSex.value == 0,
                          sex: 'Female'.tr,
                          onTap: () {
                            //震动
                            HapticFeedback.mediumImpact();
                            controller.selectedSex.value = 0;
                          },
                        ),
                      ),
                      Obx(
                        () => sexWidget(
                          selected: controller.selectedSex.value == 1,
                          sex: 'N/A'.tr,
                          onTap: () {
                            //震动
                            HapticFeedback.mediumImpact();
                            controller.selectedSex.value = 1;
                          },
                        ),
                      ),
                      Obx(
                        () => sexWidget(
                          selected: controller.selectedSex.value == 2,
                          sex: 'Male'.tr,
                          onTap: () {
                            //震动
                            HapticFeedback.mediumImpact();
                            controller.selectedSex.value = 2;
                          },
                        ),
                      ),
                    ],
                  ),
                  33.verticalSpaceFromWidth,
                  Container(
                    height: 50.w,
                    padding: REdgeInsets.only(left: 16),
                    margin: EdgeInsets.symmetric(horizontal: 20.w),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: CustomTextField.textField(
                            textCtl: controller.textCtl,
                            fontSize: 14,
                            textAlign: TextAlign.start,
                            maxLength: 20,
                            onChanged: (value) {
                              controller.nickName.value = value;
                            },
                            hintText: 'Clone Name (20 characters)'.tr,
                          ),
                        ),
                        10.horizontalSpace,
                        Obx(
                          () => Opacity(
                            opacity: controller.selectedSex.value != 3 ? 1 : 0.5,
                            child: GestureDetector(
                              onTap: () {
                                controller.cloneRandomNickname();
                              },
                              behavior: HitTestBehavior.translucent,
                              child: Padding(
                                padding: REdgeInsets.symmetric(horizontal: 16),
                                child: Image.asset(
                                  Assets.assetsImagesRandomNickname,
                                  width: 20.w,
                                  height: double.infinity,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  30.verticalSpaceFromWidth,
                  InkWell(
                    onTap: () {
                      controller.selectImage();
                    },
                    child: Container(
                      width: 220.w,
                      height: 330.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        color: AppColor.colorsUtil('#23212B'),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Obx(
                        () => controller.selectedImage.value == false
                            ? Column(
                                children: [
                                  110.verticalSpaceFromWidth,
                                  Image.asset(
                                    Assets.assetsImagesKloneAddImage,
                                    width: 50.w,
                                  ),
                                  30.verticalSpaceFromWidth,
                                  Text(
                                    'Tailor a photo or use default'.tr,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.white.withValues(alpha: 0.2),
                                    ),
                                  )
                                ],
                              )
                            : Image.memory(
                                controller.bgImageData!,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                  ),
                  30.verticalSpaceFromWidth,
                  Obx(
                    () => Opacity(
                      opacity: controller.nickName.isNotEmpty && controller.selectedSex.value != 3
                          ? 1
                          : 0.5,
                      child: InkWell(
                        onTap: () {
                          controller.next();
                        },
                        child: Container(
                          width: 234.w,
                          height: 45.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(45.w / 2),
                            gradient: LinearGradient(
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A'),
                              ],
                            ),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            'GO'.tr,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w700,
                              color: controller.nickName.isNotEmpty &&
                                      controller.selectedSex.value != 3
                                  ? Colors.white
                                  : AppColor.colorsUtil('#9C835C'),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  20.verticalSpaceFromWidth
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget sexWidget({required bool selected, required String sex, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Image.asset(
            sex == 'Male'.tr
                ? (selected
                    ? Assets.assetsImagesKloneSexMaleSelected
                    : Assets.assetsImagesKloneSexMaleSelect)
                : sex == 'Female'.tr
                    ? (selected
                        ? Assets.assetsImagesKloneSexFemaleSelected
                        : Assets.assetsImagesKloneSexFemaleSelect)
                    : (selected
                        ? Assets.assetsImagesKloneSexNASelected
                        : Assets.assetsImagesKloneSexNASelect),
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) =>
                SizedBox(height: 70.w, width: 70.w, child: child),
          ),
          10.verticalSpaceFromWidth,
          Text(
            sex,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: selected ? FontWeight.w500 : FontWeight.w400,
              color: selected ? Colors.white : AppColor.colorsUtil('#4F4D55'),
            ),
          )
        ],
      ),
    );
  }
}
