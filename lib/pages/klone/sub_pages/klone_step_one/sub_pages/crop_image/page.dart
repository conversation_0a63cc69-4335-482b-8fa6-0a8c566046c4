import 'dart:io';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import 'controller.dart';

class CropCloneImagePage extends GetView<CropCloneImagePageController> {
  const CropCloneImagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        width: 1.sw,
        height: 1.sh,
        child: Stack(
          children: [
            //裁剪的图片 放到背后
            Obx(() => controller.showCropImage.value
                ? Positioned(
                    child: WidgetsToImage(
                      controller: controller.cropImageController,
                      child: CustomPaint(
                        painter: controller.clipper,
                        size: Size(275.w, 275.w),
                      ),
                    ),
                  )
                : Container()),
            //背景图
            Positioned.fill(
              child: WidgetsToImage(
                controller: controller.bgImageController,
                child: SizedBox(
                  width: 1.sw,
                  height: 1.sh,
                  child: CustomImageCrop(
                    cropController: controller.zoomController,
                    image: FileImage(
                      File(controller.imagePath),
                    ),
                    shape: CustomCropShape.Ratio,
                    ratio: Ratio(width: 1.sw, height: 1.sh),
                    overlayColor: Colors.transparent,
                    backgroundColor: Colors.transparent,
                    pathPaint: Paint(),
                    imageFit: CustomImageFit.fillCropHeight,
                    cropPercentage: 1,
                    canRotate: false,
                    forceInsideCropArea: true,
                  ),
                  // Image.file(
                  //   File(controller.imagePath),
                  //   fit: BoxFit.cover,
                  // ),
                ),
              ),
            ),
            Obx(
              () => Positioned(
                top: controller.cropOffset.value.dy,
                left: controller.cropOffset.value.dx,
                child: draggableWidget(),
              ),
            ),
            Positioned(
              bottom: 55.h + CommonUtil.bottomBarHeight(),
              left: 0,
              right: 0,
              child: Column(
                children: [
                  Text(
                    '*Move dashed box to crop avatar\n*Zoom Image Crop Background',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white,
                      //文字阴影
                      shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 1)],
                    ),
                  ),
                  30.verticalSpaceFromWidth,
                  GradientColorBtn(
                    height: 45.w,
                    width: 234.w,
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                    text: 'GO',
                    onTap: () {
                      controller.done();
                    },
                  ),
                ],
              ),
            ),
            Positioned(
              top: CommonUtil.statusBarHeight(context),
              child: const CustomBackButton(),
            ),
          ],
        ),
      ),
    );
  }

  Widget draggableWidget() {
    return Draggable(
      feedback: _buildDraggable(),
      childWhenDragging: Container(),
      maxSimultaneousDrags: 1,
      //开始拖动时回调
      onDragStarted: () {
        controller.showCropImage.value = false;
      },
      //拖动结束时回调
      onDragEnd: (details) {
        controller.cropOffset.value = Offset(
          details.offset.dx < 0
              ? 10.w
              : details.offset.dx > (1.sw - 275.w)
                  ? 1.sw - 275.w - 10.w
                  : details.offset.dx,
          details.offset.dy < 0
              ? 10.w
              : details.offset.dy > (1.sh - 275.w)
                  ? 1.sh - 275.w - 10.w
                  : details.offset.dy,
        );
      },
      child: _buildDraggable(),
    );
  }

  Widget _buildDraggable() {
    // background
    return Column(
      children: [
        Container(
          width: 275.w,
          height: 275.w,
          alignment: Alignment.bottomCenter,
          decoration: const BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.fill,
              image: AssetImage(Assets.assetsImagesCloneAvatarContainer),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(bottom: 10.w),
            child: Text(
              'Profile',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
                shadows: [Shadow(color: Colors.black.withValues(alpha: 0.65), blurRadius: 5)],
              ),
            ),
          ),
        ),
        // 4.verticalSpaceFromWidth,
        Text(
          'Background',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.white,
            shadows: [Shadow(color: Colors.black.withValues(alpha: 0.65), blurRadius: 5)],
          ),
        ),
      ],
    );
  }
}
