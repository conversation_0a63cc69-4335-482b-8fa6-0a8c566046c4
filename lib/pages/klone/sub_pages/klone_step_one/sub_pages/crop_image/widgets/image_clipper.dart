import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

/// 图片裁剪
class ImageClipper extends CustomPainter {
  final ui.Image image;
  final double left;
  final double top;
  final double width;
  final double height;

  ImageClipper({
    required this.image,
    required this.left,
    required this.top,
    required this.height,
    required this.width,
  });
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint();
    double scale = image.width / 1.sw;
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(left * scale, top * scale, width * scale, height * scale),
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
