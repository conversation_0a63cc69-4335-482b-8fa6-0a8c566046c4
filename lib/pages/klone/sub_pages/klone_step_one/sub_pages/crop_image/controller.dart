import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:flutter/material.dart';
// import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import 'widgets/image_clipper.dart';

class CropCloneImagePageController extends GetxController {
  late String imagePath;
  late WidgetsToImageController bgImageController;
  late WidgetsToImageController cropImageController;
  late CustomImageCropController zoomController;
  //裁剪框默认位置
  var cropOffset = Offset((1.sw - 275.w) / 2, CommonUtil.statusBarHeight(Get.context)).obs;
  ImageClipper? clipper;
  var showCropImage = false.obs;
  @override
  void onInit() {
    super.onInit();
    imagePath = Get.parameters['imagePath'] ?? '';
    bgImageController = WidgetsToImageController();
    cropImageController = WidgetsToImageController();
    zoomController = CustomImageCropController();
  }

  done() async {
    Loading.show();
    // Uint8List? bgImageData = await bgImageController.capture();
    MemoryImage? bgImage = await zoomController.onCropImage();
    if (bgImage == null) {
      Loading.dismiss();
      return;
    }
    ui.Image imageUi = await decodeImageFromList(bgImage.bytes);
    clipper = ImageClipper(
        image: imageUi,
        left: cropOffset.value.dx,
        top: cropOffset.value.dy,
        height: 275.w,
        width: 275.w);
    showCropImage.value = true;
    Future.delayed(const Duration(milliseconds: 100), () async {
      Uint8List? cropImageData = await cropImageController.capture();
      Loading.dismiss();
      Get.back(result: [cropImageData, bgImage.bytes]);
    });
  }

  @override
  void onClose() {
    super.onClose();
  }
}
