import 'dart:async';
import 'dart:io';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/models/clone_model/voice_bank_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/state.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/widgets/choose_voice/choose_voice.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path/path.dart';
import 'package:record/record.dart';

import '../controller.dart';

class VoiceCloneLogic {
  KloneStepTwoPageState get state => KloneStepTwoPageController.to.state;
  KloneStepTwoPageController get controller => KloneStepTwoPageController.to;
  //列表新增或删除的消息
  List<Map<String, dynamic>> listOperation = [];
  //选择声音
  selectVoice() async {
    VoiceBankItemVoiceModel? result = await Get.bottomSheet(
      const CloneChooseVoice(),
      isScrollControlled: true,
    );
    if (result == null) {
      resetOptionWidget();
    } else {
      listOperation.add(
          selectVoiceComplete(voiceVal: result.voiceVal ?? '', voiceName: result.voiceName ?? ''));
      addDoneButton();
    }
  }

  Map<String, dynamic> selectVoiceComplete({required String voiceVal, required String voiceName}) {
    state.selectedVoice = voiceVal;
    state.voiceSource = 'select';
    state.selectedVoiceBankName = voiceName;
    return {
      'opration': 'add',
      'model': CloneStepContentModel(
        type: 3,
        source: 1,
        content: 'Selected'.trArgs(['“$voiceName”']),
        contentColor: AppColor.colorsUtil('#333333'),
        specialColor: AppColor.colorsUtil('#F0BE72'),
        specialText: voiceName,
      )
    };
  }

//选择声音文件
  selectVoiceFile() async {
    String? path = await FilePickerUtil.pickAudio();
    if (path != null) {
      File videoFile = File(path);
      int size = await videoFile.length();
      if (size > 10 * 1024 * 1024) {
        //大于10M不上传
        Loading.toast("File size exceeds limit");
        resetOptionWidget();
        return;
      }
      //获取文件扩展名
      String fileType = (extension(path)).substring(1);
      Loading.show();
      String? url =
          await UploadOss.upload(path: path, format: fileType, servicePath: ossServicePathClone);
      Loading.dismiss();
      if (url != null) {
        listOperation.add(selectVoiceFileComplete(path: path, url: url));
        addDoneButton();
      } else {
        resetOptionWidget();
      }
    } else {
      resetOptionWidget();
    }
  }

  Map<String, dynamic> selectVoiceFileComplete({required String path, String? url}) {
    state.selectedVoice = url ?? path;
    state.voiceSource = 'upload';
    return {
      'opration': 'add',
      'model': CloneStepContentModel(
          type: 5,
          source: 1,
          // content: 'Uploaded file ...${path.substring(path.length - 3)}'));
          content: '...${path.substring(path.length - 15)}')
    };
  }

//录音 提示
  recordVoiceStart() async {
    bool? result = await Get.dialog(
      CustomDialogWidget(
        title: "System".tr,
        subTitle: 'clone record tip 1'.tr,
        confirmTitle: "Let's go".tr,
        cancelTitle: 'Pass'.tr,
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      //模拟插入数据
      listOperation.add(recordTipMessage());
      inserRecordVoiceText(index: 0);
    } else {
      resetOptionWidget();
    }
  }

  //录音提示消息
  Map<String, dynamic> recordTipMessage() {
    return {
      'opration': 'add',
      'model': CloneStepContentModel(
          type: 1,
          source: 0,
          contentColor: Colors.white.withValues(alpha: 0.8),
          content: "clone record tip 2".tr)
    };
  }

  //上传语音
  uploadVoice({required int fileIndex, required int messageIndex, required bool lastVoice}) async {
    stopPlay();
    //上传语音
    if (state.recordVoiceFilePath.length > fileIndex) {
      if (lastVoice == true) {
        Loading.show();
      }
      CloneStepContentModel answerModel = state.messageList.elementAt(messageIndex);

      CloneStepContentModel voiceModel = state.messageList.elementAt(messageIndex + 2);
      int auditDuration = state.messageList.elementAt(messageIndex + 2).duration ?? 0;
      voiceModel.complete = true;
      state.messageList.replaceRange(messageIndex + 2, messageIndex + 3, [voiceModel]);
      if (lastVoice != true) {
        //下一个语音
        inserRecordVoiceText(index: answerModel.questionIndex! + 1);
      }
      String? url = await UploadOss.upload(
          path: state.recordVoiceFilePath.elementAt(fileIndex),
          format: 'm4a',
          servicePath: ossServicePathClone);
      if (url != null) {
        bool result = await cloneSaveProcess(questions: [
          {
            'answer': answerModel.content,
            'auditUrl': url,
            'id': int.parse(answerModel.msgId!),
            'auditDuration': auditDuration
          }
        ], showLoading: lastVoice);
        if (result == true) {
          if (lastVoice == true) {
            //最后一个语音
            state.cloneDone.value = true;
            controller.startFineTune();
          }
        }
      }
    }
  }

  //新增录音 朗读的文本
  inserRecordVoiceText({required int index, bool redo = false}) async {
    state.voiceSource = 'record';
    //删除录音组件 然后添加到最后
    state.messageList.where((element) => element.type == 7).toList().forEach((element) {
      listOperation.add({'opration': 'del', 'model': element});
    });
    //删除 “下一步”按钮
    state.messageList.where((element) => element.type == 9).toList().forEach((element) {
      listOperation.add({'opration': 'del', 'model': element});
    });
    // print('问题下标:$index');
    if (redo == false) {
      CloneCreatQuestionModel model = state.infoModel.vtQuestions!.elementAt(index);
      listOperation.add({
        'opration': 'add',
        'model': CloneStepContentModel(
            type: 3,
            source: 0,
            contentColor: AppColor.colorsUtil('#F0BE72'),
            specialColor: Colors.white,
            questionIndex: index,
            specialText: 'Q${index + 1}/${state.infoModel.vtQuestions!.length}:',
            content: "Q${index + 1}/${state.infoModel.vtQuestions!.length}: ${model.question}"),
      });
      listOperation.add({
        'opration': 'add',
        'model': CloneStepContentModel(
          type: 4,
          source: 1,
          questionIndex: index,
          content: model.answer,
          msgId: '${model.id}', //重新生成时使用
        ),
      });
      listOperation.add({
        'opration': 'add',
        'model': CloneStepContentModel(
          type: 6,
          content: "Not a fan? Regenerate or edit".tr,
          questionIndex: index,
        )
      });
    }

    // questionIndex  标识第几个问题的录音
    listOperation
        .add({'opration': 'add', 'model': CloneStepContentModel(type: 7, questionIndex: index)});
    controller.refreshWidgetList(widgets: listOperation);
    listOperation = [];
  }

//选项组件 重新生成
  optionWidgetRedo() {
    if (state.messageList.last.type == 2 && state.messageList.last.optionsSelected == null) {
      //最后一个是选项组件且 未选择 就忽略
      return;
    }
    state.recordVoiceFilePath = [];
    state.selectedVoice = '';
    state.voiceSource = '';
    //删除 除第一第二文本组件外的其他组件
    state.messageList.where((element) => element.type != 1).toList().forEach((element) {
      listOperation.add({'opration': 'del', 'model': element});
    });
    // //删除 “下一步”/”完成“ 按钮
    // state.messageList.where((element) => element.type == 9).toList().forEach((element) {
    //   listOperation.add({'opration': 'del', 'model': element});
    // });
    // //删除录音组件
    // state.messageList.where((element) => element.type == 7).toList().forEach((element) {
    //   listOperation.add({'opration': 'del', 'model': element});
    // });
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(
          type: 2, msgId: CommonUtil.randomLetters(10), source: 0, showRedo: true)
    });
    controller.refreshWidgetList(widgets: listOperation);
    listOperation = [];
  }

  //点击 录音按钮
  record() async {
    await stopPlay();
    if (state.recordUtil.recordSub == null) {
      state.recordUtil.init(onStateChanged: (recordState) {
        if (recordState == RecordState.record) {
          state.recordState.value = 1;
          startRecordTimer();
        }
      });
    }
    //结束录音
    if (state.recordState.value == 1) {
      String? voiceFilePath = await state.recordUtil.stop();
      state.recordState.value = 0;
      removeTimer();
      if (state.recordDuration.value < 6) {
        BlurToast.show(content: 'Recordings must be greater than 5 seconds'.tr);
        return;
      }
      //添加音频内容组件到列表
      if (voiceFilePath != null && voiceFilePath.isNotEmpty) {
        state.recordVoiceFilePath.add(voiceFilePath);
        //删除录音组件
        int? questionIndex;
        state.messageList.where((element) => element.type == 7).toList().forEach((element) {
          listOperation.add({'opration': 'del', 'model': element});
          questionIndex = element.questionIndex;
        });
        listOperation.add({
          'opration': 'add',
          'model': CloneStepContentModel(
              type: 8,
              content: voiceFilePath,
              duration: state.recordDuration.value,
              questionIndex: questionIndex, //标记是哪个问题的录音
              msgId: voiceFilePath)
        });
        //完成全部录音
        if (questionIndex == state.infoModel.vtQuestions!.length - 1) {
          addDoneButton();
        } else {
          //添加“下一步”按钮组件 到列表
          listOperation.add({
            'opration': 'add',
            'model': CloneStepContentModel(type: 9, content: "Next", msgId: 'Record')
          });
          controller.refreshWidgetList(widgets: listOperation);
          listOperation = [];
          //初始化播放器
          initVoicePlayer();
        }
      }
    } else {
      //开始录音
      state.recordDuration.value = 0;
      state.recordUtil.start();
    }
  }

  //录音倒计时
  void startRecordTimer() {
    state.recordTimer?.cancel();
    state.recordTimer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      state.recordDuration.value++;
    });
  }

  //删除录音
  removeRecordFile({required CloneStepContentModel model}) {
    listOperation.add({'opration': 'del', 'model': model});
    state.recordVoiceFilePath.removeWhere((element) => element == model.msgId);
    inserRecordVoiceText(index: model.questionIndex!, redo: true);
    //如果正在播放就停止
    if (state.playingMsgId.value == model.msgId) {
      stopPlay();
    }
  }

  //初始化播放器
  initVoicePlayer() async {
    if (state.voicePlayer == null) {
      state.voicePlayer = AudioPlayer();
      debugPrint('初始化播放器');
      //监听播放
      state.voicePlayer!.playerStateStream.listen((playState) {
        if (Get.isRegistered<KloneStepTwoPageController>() == false) {
          return;
        }
        debugPrint(playState.processingState.toString());
        if (playState.processingState == ProcessingState.completed) {
          state.playCompleted = true;
          state.recordFilePlaying.value = false;
          state.playDuration.value = 0;
          state.playingMsgId.value = '';
        }
      });
      state.voicePlayer!.positionStream.listen((event) {
        if (Get.isRegistered<KloneStepTwoPageController>() == false) {
          return;
        }
        if (state.playCompleted == false) {
          state.playDuration.value = event.inSeconds;
        } else {
          state.playDuration.value = 0;
        }
      });
    }
  }

  //播放、暂停录音播放
  playRecordFile({required String msgId}) async {
    initVoicePlayer();
    //点击的是正在播放的消息
    if (state.playingMsgId.value == msgId) {
      if (state.recordFilePlaying.value == true) {
        //暂停
        state.recordFilePlaying.value = false;
        state.voicePlayer!.pause();
      } else {
        //继续播放
        state.recordFilePlaying.value = true;
        state.voicePlayer!.play();
      }
      return;
    }
    //停止播放
    await stopPlay();
    state.playingMsgId.value = msgId;
    state.playCompleted = false;
    state.recordFilePlaying.value = true;
    // 开始播放
    await setPlayerSource(path: msgId);
    state.voicePlayer!.play();
  }

  //停止播放
  Future stopPlay() async {
    if (state.recordFilePlaying.value == true) {
      state.playCompleted = true;
      state.recordFilePlaying.value = false;
      state.playDuration.value = 0;
      state.playingMsgId.value = '';
      await state.voicePlayer!.stop();
    }
  }

  //设置播放数据
  Future<int> setPlayerSource({required String path}) async {
    Duration? value;
    if (path.contains("https://")) {
      value = await state.voicePlayer!.setAudioSource(AudioSource.uri(Uri.parse(path)));
    } else {
      value = await state.voicePlayer!.setAudioSource(AudioSource.file(path));
    }
    //获取时长 使用录音倒计时时长 所以这个暂时无用
    if (value != null) {
      return value.inSeconds;
    }
    return 0;
  }

  //添加完成按钮组件到列表
  addDoneButton() {
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(type: 9, content: "Fine-tune", msgId: 'FineTune')
    });
    controller.refreshWidgetList(widgets: listOperation);
    listOperation = [];
  }

  //vt完成
  vtDone() async {
    if (state.ontapNext == true) {
      return;
    }
    String? audit;
    if (state.voiceSource != 'record' && state.selectedVoice.isNotEmpty) {
      audit = state.selectedVoice;
      state.ontapNext = true;
      bool result = await cloneSaveProcess(audit: audit, voiceName: state.selectedVoiceBankName);
      if (result == true) {
        state.cloneDone.value = true;
        controller.startFineTune();
      } else {
        state.ontapNext = false;
      }
    } else if (state.recordVoiceFilePath.isNotEmpty) {
      /*
      //声音合并
      Loading.show();
      File file = await FFmpeg.concatenate(state.recordVoiceFilePath);
      String? path = await FileUtil.saveVoiceFile(file.readAsBytesSync(), 'm4a');
      if (path != null) {
        String? url = await UploadOss.upload(path: path, format: 'm4a',servicePath: ossServicePathClone);
        if (url != null) {
          audit = url;
        }
      }
      */
      uploadVoice(
          fileIndex: state.recordVoiceFilePath.length - 1,
          messageIndex: state.messageList.length - 4,
          lastVoice: true);
    }
  }

  //保存进度
  Future<bool> cloneSaveProcess(
      {List? questions, String? audit, String? voiceName, bool showLoading = true}) async {
    bool result = await CloneApis.cloneSaveProcess(
        cloneModelProcess: 'VT',
        voiceTypeEnum: state.voiceSource,
        modelNo: state.modelNo,
        audit: audit,
        voiceName: voiceName,
        questions: questions,
        showLoading: showLoading);
    return result;
  }

  //重置选项组件
  resetOptionWidget() {
    CloneStepContentModel model = KloneStepTwoPageController.to.state.messageList.last;
    model.optionsSelected = null;
    state.messageList.replaceRange(state.messageList.length - 1, state.messageList.length, [model]);
    controller.refreshWidgetList(msgId: model.msgId);
  }

  removeTimer() {
    if (state.recordTimer != null) {
      state.recordTimer!.cancel();
      state.recordTimer = null;
    }
  }
}
