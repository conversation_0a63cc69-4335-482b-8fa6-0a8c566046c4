import 'dart:async';
import 'dart:convert' as convert;
import 'dart:typed_data';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

class QuestionVoicePlayer {
  late AudioPlayer player;
  //正在进行中的生成语音的消息
  List underWayList = [];
  // int playIndex = 0;
  String msgId = '';
  init() {
    player = AudioPlayer();
    player.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        player.stop();
        refreshWidget();
        msgId = '';
      }
    });
    player.playingStream.listen((event) {
      if (Get.isRegistered<KloneStepTwoPageController>()) {
        if (event == true) {
          KloneStepTwoPageController.to.state.playingQuestion = msgId;
        } else {
          KloneStepTwoPageController.to.state.playingQuestion = '';
        }
        KloneStepTwoPageController.to.update([msgId]);
      }
    });
    underWayList.clear();
  }

  dispose() {
    player.stop();
    player.dispose();
  }

  Future play({required CloneStepContentModel model}) async {
    if (msgId.isNotEmpty) {
      await stopPlayVoice();
      if (msgId == model.msgId) {
        refreshWidget();
        msgId = '';
        return;
      }
    }
    if (underWayList.contains(model.msgId)) {
      Loading.toast('Loading...');
      return;
    }
    msgId = model.msgId ?? '';
    if (model.audioBuffer != null) {
      await player.setAudioSource(BytesSource(Uint8List.fromList(model.audioBuffer!)));
      player.play();
    } else {
      debugPrint('文字转语音');
      //文字转语音
      ttsRequest(model: model);
    }
  }

  //停止播放
  Future stopPlayVoice() async {
    debugPrint('停止播放');
    await player.stop();
  }

  //文字转语音请求
  ttsRequest({required CloneStepContentModel model}) async {
    // Loading.toast('4');
    String text = CommonUtil.extractEmojis(model.content!.substring(model.specialText!.length));
    // debugPrint('文字转语音：$text');
    String voice = KloneStepTwoPageController.to.state.voiceParam;
    // debugPrint('发音人:$voice');

    if (voice.isEmpty || text.isEmpty) {
      Loading.toast('Playback Error');
      return;
    }
    underWayList.add(model.msgId);
    refreshWidget();
    //微软文字转语音
    if (voice.contains('azure')) {
      return;
    }
    try {
      Uint8List? stream = await _fetch(text, voice);
      underWayList.removeWhere((element) => element == model.msgId);
      if (stream == null) {
        Loading.toast('Playback Error');
        return;
      }
      model.audioBuffer = stream;
      for (var i = 0; i < KloneStepTwoPageController.to.state.messageList.length; i++) {
        CloneStepContentModel model = KloneStepTwoPageController.to.state.messageList.elementAt(i);
        if (model.msgId == msgId) {
          model.audioBuffer = stream;
          KloneStepTwoPageController.to.state.messageList.replaceRange(i, i + 1, [model]);
          break;
        }
      }
      await player.setAudioSource(BytesSource(Uint8List.fromList(model.audioBuffer!)));
      player.play();
    } catch (ex) {
      debugPrint('Error: $ex');
      underWayList.removeWhere((element) => element == msgId);
      refreshWidget();
    }
  }

  //请求音频流
  Future<Uint8List?> _fetch(String text, String voice) async {
    // Loading.show();
    // print('${AppService.configModel.voiceUrl}${Api.ttsStream}');
    // print({'content': text, 'character': voice, 'speed': 1});
    final response = await Dio().postUri(
      Uri.parse('${AppService.configModel.voiceUrl}${Api.ttsStream}'),
      data: convert.jsonEncode({'content': text, 'character': voice, 'speed': 1}),
      options: Options(headers: {
        'Content-Type': 'application/json',
        'appSource': 'amor',
        'token': UserService.to.token
      }, responseType: ResponseType.bytes),
    );
    // Loading.dismiss();
    debugPrint('api:${response.statusCode}');
    // print(response.data);
    // ResponseBody responseBody = response.data;
    if (response.statusCode != 200) {
      return null;
    }
    return response.data;
  }

  //刷新列表
  refreshWidget() {
    if (Get.isRegistered<KloneStepTwoPageController>()) {
      KloneStepTwoPageController.to.state.playingQuestion = '';
      KloneStepTwoPageController.to.update([msgId]);
    }
  }
}

class BytesSource extends StreamAudioSource {
  final Uint8List _buffer;
  BytesSource(this._buffer) : super(tag: 'BytesSource');
  @override
  Future<StreamAudioResponse> request([int? start, int? end]) async {
    // Returning the stream audio response with the parameters
    return StreamAudioResponse(
      sourceLength: _buffer.length,
      contentLength: (end ?? _buffer.length) - (start ?? 0),
      offset: start ?? 0,
      stream: Stream.fromIterable([_buffer.sublist(start ?? 0, end)]),
      contentType: 'audio/mpeg',
    );
  }
}
