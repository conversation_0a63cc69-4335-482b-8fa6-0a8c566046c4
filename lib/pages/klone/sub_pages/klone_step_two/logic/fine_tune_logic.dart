import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/state.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller.dart';

class FineTuneLogic {
  KloneStepTwoPageState get state => KloneStepTwoPageController.to.state;
  KloneStepTwoPageController get controller => KloneStepTwoPageController.to;
  //列表新增或删除的消息
  List<Map<String, dynamic>> listOperation = [];

  //展示消息
  addMessage() {
    if (state.ftQuestionIndex == 0) {
      //删除“下一步”按钮
      state.messageList.where((element) => element.type == 9).toList().forEach((element) {
        listOperation.add({'opration': 'del', 'model': element});
      });
      //添加“FineTune”提示
      listOperation
          .add({'opration': 'add', 'model': CloneStepContentModel(type: 6, content: "Fine-tune")});
      if ((state.infoModel.ftQuestions ?? []).isEmpty) {
        Loading.toast('error');
        return;
      }
    }

    CloneCreatQuestionModel model = state.infoModel.ftQuestions!.elementAt(state.ftQuestionIndex);
    addMessageContent(model);
  }

  //新增消息
  addMessageContent(CloneCreatQuestionModel model) async {
    delNextButton();
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(
          type: 3,
          source: 0,
          contentColor: AppColor.colorsUtil('#F0BE72'),
          specialColor: Colors.white,
          msgId: CommonUtil.randomLetters(10),
          duration:
              ("FT ${state.ftQuestionIndex + 1}/${state.infoModel.ftQuestions!.length}: ${model.question}"
                      .length ~/
                  9),
          specialText: 'FT ${state.ftQuestionIndex + 1}/${state.infoModel.ftQuestions!.length}:',
          content:
              "FT ${state.ftQuestionIndex + 1}/${state.infoModel.ftQuestions!.length}: ${model.question}"),
    });
    //展示loading消息
    listOperation.add({'opration': 'add', 'model': CloneStepContentModel(type: 12, source: 1)});
    //先展示问题
    refreshListWidget();
    //请求答案
    if (model.answer == null || model.questTypeEnum == 'prompt') {
      Map? result = await CloneApis.cloneRegenerateAnswer(id: model.id!, modelNo: state.modelNo);
      state.messageList.where((element) => element.type == 12).toList().forEach((element) {
        listOperation.add({'opration': 'del', 'model': element});
      });
      if (result != null && result['voiceParam'] != null) {
        state.voiceParam = result['voiceParam'];
      }
      if (result != null && result['answer'] != null) {
        model.answer = result['answer'];
      }
    }
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(
          type: model.styleType == 'keywords' ? 10 : 4,
          source: 1,
          showRedo: true,
          labelText: model.styleType == 'keywords' ? true : false,
          msgId: '${model.id}',
          questionIndex: state.ftQuestionIndex,
          content: model.answer),
    });
    addNextButton();
    refreshListWidget();
    Future.delayed(const Duration(milliseconds: 1000), () {
      state.ontapNext = false;
    });
  }

  //全部完成动画
  showCompleted() async {
    state.cloneCompleted.value = true;
    delNextButton();
    listOperation.add({'opration': 'add', 'model': CloneStepContentModel(type: 11)});
    refreshListWidget();
    ReportUtil.reportEvents(page: ReportUtil.cloneFT, action: ReportUtil.finish);

    Future.delayed(const Duration(milliseconds: 6000), () {
      Get.offNamed(Routes.cloneStepThree, arguments: state.modelNo);
    });
  }

//文本组件 重新生成
  textWidgetRedo(CloneStepContentModel model) async {
    if (model.msgId == null) {
      return;
    }
    int modelIndex = state.messageList.indexOf(model);
    state.messageList.replaceRange(modelIndex, modelIndex + 1,
        [CloneStepContentModel(type: 12, source: 1, msgId: model.msgId)]);
    refreshListWidget();
    Map? result =
        await CloneApis.cloneRegenerateAnswer(id: int.parse(model.msgId!), modelNo: state.modelNo);
    if (result != null && result['voiceParam'] != null) {
      state.voiceParam = result['voiceParam'];
    }
    if (result != null && result['answer'] != null) {
      model.content = result['answer'];
      state.messageList.replaceRange(modelIndex, modelIndex + 1, [model]);
      controller.refreshWidgetList(msgId: model.msgId);
      //是已经完成的消息 保存
      if (modelIndex != state.messageList.length - 2) {
        cloneSaveProcess(index: modelIndex, showLoading: false);
      }
    }
  }

  //点击下一步
  nextAction({required CloneStepContentModel model}) async {
    if (state.ontapNext == true) {
      return;
    }
    int modelIndex = state.messageList.indexOf(model);
    state.ontapNext = true;
    //保存进度
    bool result = await cloneSaveProcess(
        index: modelIndex - 1,
        showLoading: state.ftQuestionIndex == state.infoModel.ftQuestions!.length - 1);
    if (result == true) {
      //全部回答完毕
      if (state.ftQuestionIndex == state.infoModel.ftQuestions!.length - 1) {
        showCompleted();
        return;
      }
      //添加下一步的组件
      state.ftQuestionIndex++;
      addMessage();
    } else {
      state.ontapNext = false;
    }
  }

  //添加"下一步"按钮组件 到列表
  addNextButton({String? title}) {
    listOperation.add(
        {'opration': 'add', 'model': CloneStepContentModel(type: 9, content: title ?? 'Next'.tr)});
  }

  //删除 “下一步”按钮
  delNextButton() {
    state.messageList.where((element) => element.type == 9).toList().forEach((element) {
      listOperation.add({'opration': 'del', 'model': element});
    });
  }

  //刷新列表
  refreshListWidget({String? msgId}) {
    controller.refreshWidgetList(msgId: msgId, widgets: listOperation);
    listOperation = [];
  }

  //保存进度
  Future<bool> cloneSaveProcess(
      {String cloneModelProcess = 'FT', required int index, showLoading = true}) async {
    CloneStepContentModel? model;
    List questions = [];
    if (cloneModelProcess == 'FT') {
      model = state.messageList.elementAt(index);
      questions.add({'answer': model.content, 'id': int.parse(model.msgId!)});
    }
    bool result = await CloneApis.cloneSaveProcess(
        cloneModelProcess: cloneModelProcess,
        modelNo: state.modelNo,
        questions: questions.isNotEmpty ? questions : null,
        showLoading: showLoading = showLoading);
    return result;
  }
}
