import 'dart:async';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/record_util/record_util.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'logic/fine_tune_logic.dart';
import 'logic/question_voice_play.dart';
import 'logic/voice_clone_logic.dart';
import 'state.dart';
import 'widgets/edit_content/edit_content.dart';

class KloneStepTwoPageController extends GetxController {
  static KloneStepTwoPageController get to => Get.find();
  late ScrollController scrollController;
  final state = KloneStepTwoPageState();
  late VoiceCloneLogic voiceCloneLogic;
  late FineTuneLogic fineTuneLogic;
  late QuestionVoicePlayer questionVoicePlay;

  @override
  void onInit() {
    super.onInit();
    state.modelNo = Get.arguments;
    // state.modelNo = 'fmAdZzu8yhoGDfuC4Voo';
    // state.showRecordVoice = (Get.parameters['showRecordVoice'] ?? 'true') == 'true';
    scrollController = ScrollController();
    voiceCloneLogic = VoiceCloneLogic();
    fineTuneLogic = FineTuneLogic();
    state.recordUtil = RecordUtil();
    questionVoicePlay = QuestionVoicePlayer();
    questionVoicePlay.init();
    initData();
  }

  //获取数据
  initData() async {
    CloneCreatModel? result = await CloneApis.cloneProcess(modelNo: state.modelNo);
    if (result != null) {
      state.name.value = result.nickname ?? '';
      state.bgImageUrl.value = result.bgImg ?? '';
      state.avatarUrl.value = result.cover ?? '';
      state.infoModel = result;
      state.showRecordVoice = result.recordOptions == 1;
      addData();
    }
  }

  //展示最开始的两条欢迎消息和选项组件
  addData() {
    //固定数据
    CloneStepContentModel welcomeModel = CloneStepContentModel(
        type: 1,
        content:
            "Hi, I'm ${state.name.value}. Thanks for Cloning me. Now, let's complete the cloning process just so I speak and sound more like you.");
    CloneStepContentModel optionWidgetTipModel = CloneStepContentModel(
        type: 1, content: "Now, where are we going to get my voice more like me?");
    CloneStepContentModel optionModel = CloneStepContentModel(
      type: 2,
      msgId: CommonUtil.randomLetters(10),
      showRedo: true,
    );
    List<Map<String, dynamic>> list = [
      {'opration': 'add', 'model': welcomeModel},
      {'opration': 'add', 'model': optionWidgetTipModel},
    ];

    //添加已完成的信息
    if (state.infoModel.voiceTypeEnum != null) {
      state.voiceSource = state.infoModel.voiceTypeEnum!;
      if (state.infoModel.voiceTypeEnum == 'select') {
        optionModel.optionsSelected = 0;
        list.add(voiceCloneLogic.selectVoiceComplete(
            voiceVal: state.infoModel.audit ?? '', voiceName: state.infoModel.voiceName ?? ''));
        list.add(addDoneButton());
      }
      if (state.infoModel.voiceTypeEnum == 'upload') {
        optionModel.optionsSelected = 1;
        list.add(voiceCloneLogic.selectVoiceFileComplete(path: state.infoModel.audit ?? ''));
      }
      if (state.infoModel.voiceTypeEnum == 'record') {
        optionModel.optionsSelected = 2;
        list.add(voiceCloneLogic.recordTipMessage());
        int completeIndex = 0; //已完成的最后一题
        for (CloneCreatQuestionModel element in state.infoModel.vtQuestions ?? []) {
          int index = state.infoModel.vtQuestions!.indexOf(element);

          if (element.complete == true || (index == completeIndex && index == 0)) {
            completeIndex = element.complete == true ? index : -1;
            list.addAll(recoedMessage(index: index));
          } else if (index == completeIndex + 1) {
            //添加下一题
            list.addAll(recoedMessage(index: index));
          }
        }
      }
    }

    //添加FT消息
    //下一题的内容
    CloneCreatQuestionModel? nextQuestionModel;
    if (state.infoModel.cloneProcess == 'FT') {
      List<Map<String, dynamic>> ftList = [];
      //添加“FineTune”提示
      ftList
          .add({'opration': 'add', 'model': CloneStepContentModel(type: 6, content: "Fine-tune")});

      for (CloneCreatQuestionModel element in state.infoModel.ftQuestions ?? []) {
        int index = state.infoModel.ftQuestions!.indexOf(element);
        if (element.complete == true) {
          ftList.add({
            'opration': 'add',
            'model': CloneStepContentModel(
                type: 3,
                source: 0,
                contentColor: AppColor.colorsUtil('#F0BE72'),
                specialColor: Colors.white,
                msgId: CommonUtil.randomLetters(10),
                duration:
                    ("FT ${state.ftQuestionIndex + 1}/${state.infoModel.ftQuestions!.length}: ${element.question}"
                            .length ~/
                        9),
                specialText: 'FT ${index + 1}/${state.infoModel.ftQuestions!.length}:',
                content:
                    "FT ${index + 1}/${state.infoModel.ftQuestions!.length}: ${element.question}"),
          });

          ftList.add({
            'opration': 'add',
            'model': CloneStepContentModel(
                type: element.styleType == 'keywords' ? 10 : 4,
                source: 1,
                showRedo: true,
                labelText: element.styleType == 'keywords' ? true : false,
                msgId: '${element.id}',
                questionIndex: state.ftQuestionIndex,
                content: element.answer),
          });
          state.ftQuestionIndex = index + 1;
        } else {
          nextQuestionModel ??= element;
        }
      }
      list.addAll(ftList);
    }
    list.insert(2, {'opration': 'add', 'model': optionModel});
    state.cloneDone.value = state.infoModel.cloneProcess == 'FT';
    ReportUtil.reportViews(
        page: state.cloneDone.value ? ReportUtil.cloneFT : ReportUtil.cloneVT,
        action: ReportUtil.view);

    // refreshWidgetList(widgets: list);
    state.messageList.addAll(list.map<CloneStepContentModel>((e) => e['model']).toList());
    refreshWidgetList();
    if (state.cloneDone.value == false) {
      Future.delayed(const Duration(milliseconds: 100), () {
        scrollController.animateTo(scrollController.position.maxScrollExtent,
            curve: Curves.ease, duration: const Duration(milliseconds: 400));
      });
    }
    //添加FT下一题
    if (state.cloneDone.value == true && nextQuestionModel != null) {
      fineTuneLogic.addMessageContent(nextQuestionModel);
    }
    return;
  }

  //添加完成按钮组件到列表
  Map<String, dynamic> addDoneButton() {
    return {
      'opration': 'add',
      'model': CloneStepContentModel(type: 9, content: "Fine-tune".tr, msgId: 'FineTune')
    };
  }

  //录音消息
  List<Map<String, dynamic>> recoedMessage({required int index}) {
    List<Map<String, dynamic>> listOperation = [];
    CloneCreatQuestionModel model = state.infoModel.vtQuestions!.elementAt(index);
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(
          type: 3,
          source: 0,
          contentColor: AppColor.colorsUtil('#F0BE72'),
          specialColor: Colors.white,
          questionIndex: index,
          specialText: 'Q${index + 1}/${state.infoModel.vtQuestions!.length}:',
          content: "Q${index + 1}/${state.infoModel.vtQuestions!.length}: ${model.question}"),
    });
    listOperation.add({
      'opration': 'add',
      'model': CloneStepContentModel(
        type: 4,
        source: 1,
        questionIndex: index,
        content: model.answer,
        msgId: '${model.id}', //重新生成时使用
      ),
    });
    if (model.complete == true) {
      listOperation.add({
        'opration': 'add',
        'model': CloneStepContentModel(
            type: 8,
            content: model.auditUrl,
            duration: model.auditDuration,
            complete: model.complete,
            questionIndex: index, //标记是哪个问题的录音
            msgId: model.auditUrl)
      });
    }
    if (model.complete != true) {
      listOperation.add({
        'opration': 'add',
        'model': CloneStepContentModel(type: 6, content: "Not a fan? Regenerate or edit")
      });
      listOperation
          .add({'opration': 'add', 'model': CloneStepContentModel(type: 7, questionIndex: index)});
    }
    return listOperation;
  }

  //展示声音库
  showVoiceBank() async {
    state.voiceBankModel ??= await CloneApis.cloneVoiceBank();
    voiceCloneLogic.selectVoice();
  }

  //克隆完成 开始设置其他信息
  startFineTune() async {
    //请求ft问题的语音参数
    CloneCreatModel? result = await CloneApis.cloneProcess(modelNo: state.modelNo);
    if (result != null && result.voiceParam != null) {
      state.voiceParam = result.voiceParam!;
    }
    for (var element in state.messageList) {
      element.showRedo = false;
    }
    fineTuneLogic.addMessage();
    ReportUtil.reportEvents(page: ReportUtil.cloneVT, action: ReportUtil.finish);
    ReportUtil.reportViews(page: ReportUtil.cloneFT, action: ReportUtil.view);
  }

  //更新列表
  refreshWidgetList({String? msgId, List<Map<String, dynamic>>? widgets}) {
    if (msgId != null) {
      update([msgId]);
      // Future.delayed(const Duration(milliseconds: 100), () {
      //   scrollController.animateTo(scrollController.position.maxScrollExtent,
      //       curve: Curves.ease, duration: const Duration(milliseconds: 100));
      // });
      return;
    }
    if (widgets != null && widgets.isNotEmpty) {
      scrollAnimation(widgets);
      return;
    }
    //刷新列表
    update(['cloneListWidget']);
  }

  //滚动动画
  scrollAnimation(List<Map<String, dynamic>> widgets) {
    int index = 0;
    state.addMessageTimer = Timer.periodic(const Duration(milliseconds: 100), (Timer t) {
      if (index >= widgets.length && state.addMessageTimer != null) {
        state.addMessageTimer?.cancel();
        state.addMessageTimer = null;
        return;
      }
      if (widgets.elementAt(index)['model'] != null) {
        if (widgets.elementAt(index)['opration'] == 'add') {
          state.messageList.add(widgets.elementAt(index)['model']);
        } else {
          state.messageList.remove(widgets.elementAt(index)['model']);
        }
        //刷新列表
        update(['cloneListWidget']);
        Future.delayed(const Duration(milliseconds: 50), () {
          scrollController.animateTo(scrollController.position.maxScrollExtent,
              curve: Curves.ease, duration: const Duration(milliseconds: 50));
        });
      }
      index++;
    });
  }

//编辑答案文本
  editContentText(CloneStepContentModel model) async {
    //获取问题数据
    int questionIndex = state.messageList.indexOf(model) - 1;
    if (questionIndex == -1) {
      return;
    }
    CloneStepContentModel questionModel = state.messageList.elementAt(questionIndex);
    String? result = await Get.bottomSheet(
      EditContentWidget(
          questionModel: questionModel,
          contentModel: model,
          showCount: state.cloneDone.value,
          editLabel: model.type == 10),
      isScrollControlled: true,
    );
    if (result != null && result != model.content) {
      model.content = result;
      state.messageList.replaceRange(questionIndex + 1, questionIndex + 2, [model]);
      //ft答案修改
      if (state.cloneDone.value == true) {
        //编辑之前已完成的标签时需要保存  如果是当前问题的标签 点击下一步时再保存
        if (questionIndex != state.messageList.length - 1) {
          fineTuneLogic.cloneSaveProcess(index: state.messageList.indexOf(model));
        }
      }
      refreshWidgetList(msgId: model.msgId);
    }
  }

  //保存进度
  Future<void> saveProgress() async {
    if (state.messageList.isEmpty) {
      return;
    }
    CloneStepContentModel model = state.messageList.last;
    bool? result = await Get.dialog(
      const CustomDialogWidget(
          title: "Save Your Progress?",
          subTitle: 'Come back anytime.',
          confirmTitle: 'OK',
          cancelTitle: 'No',
          confirmColor: '#00B0F9'),
      barrierDismissible: false,
    );
    if (result == true && (model.type == 9 || model.type == 7)) {
      String? audit;
      List? questions;
      //vt
      if (state.cloneDone.value == false) {
        if (state.voiceSource != 'record') {
          audit = state.selectedVoice;
        } else if (model.type == 9) {
          CloneStepContentModel voiceMessageModel = state.messageList[state.messageList.length - 2];
          Loading.show();
          String? url = await UploadOss.upload(
              path: voiceMessageModel.content, format: 'm4a', servicePath: ossServicePathClone);
          CloneStepContentModel? voiceQuestionModel;
          for (var i = state.messageList.length - 1; i == 0; i--) {
            if (state.messageList.elementAt(i).type == 4 &&
                state.messageList.elementAt(i).questionIndex == voiceMessageModel.questionIndex) {
              voiceQuestionModel = state.messageList.elementAt(i);
              break;
            }
          }
          if (voiceQuestionModel != null) {
            questions = [
              {
                'answer': voiceQuestionModel.content ?? '',
                'auditUrl': url,
                'id': int.parse(voiceQuestionModel.msgId!),
                'auditDuration': voiceMessageModel.duration
              }
            ];
          }
        } else {
          CloneStepContentModel voiceMessageModel = state.messageList[state.messageList.length - 3];
          questions = [
            {
              'answer': voiceMessageModel.content ?? '',
              'id': int.parse(voiceMessageModel.msgId!),
            }
          ];
        }
      } else {
        CloneStepContentModel answerModel = state.messageList[state.messageList.length - 2];
        questions = [
          {
            'answer': answerModel.content ?? '',
            'id': int.parse(answerModel.msgId!),
          }
        ];
      }
      //保存
      await CloneApis.cloneSaveProcess(
          cloneModelProcess: state.cloneDone.value == true ? 'FT' : 'VT',
          voiceTypeEnum: state.cloneDone.value == true ? null : state.voiceSource,
          modelNo: state.modelNo,
          audit: audit,
          voiceName: state.selectedVoiceBankName,
          questions: questions);
      return;
    }
  }

  //播放问题语音
  playQuestionVoice(CloneStepContentModel model) {
    questionVoicePlay.play(model: model);
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
    state.recordUtil.dispose();
    if (state.voicePlayer != null) {
      state.voicePlayer!.dispose();
    }
    voiceCloneLogic.removeTimer();
    questionVoicePlay.dispose();
    if (state.addMessageTimer != null) {
      state.addMessageTimer!.cancel();
      state.addMessageTimer = null;
    }
  }
}
