import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class EditContentWidget extends StatelessWidget {
  final CloneStepContentModel questionModel;
  final CloneStepContentModel contentModel;
  final bool showCount;
  final bool editLabel;
  const EditContentWidget(
      {super.key,
      required this.questionModel,
      required this.contentModel,
      required this.showCount,
      required this.editLabel});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditContentWidgetController>(
      init: EditContentWidgetController(content: contentModel.content ?? ''),
      builder: (controller) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
            gradient: LinearGradient(
              colors: [
                AppColor.colorsUtil('#27292D'),
                AppColor.colorsUtil('#111214'),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 18.w),
                  child: EasyRichText(
                    questionModel.content ?? '',
                    textAlign: TextAlign.center,
                    defaultStyle: TextStyle(
                      fontSize: 17.sp,
                      height: 20 / 17,
                      color: AppColor.colorsUtil('#F0BE72'),
                    ),
                    patternList: [
                      EasyRichTextPattern(
                        targetString: questionModel.specialText,
                        style: const TextStyle(fontWeight: FontWeight.w500, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                if (editLabel)
                  Padding(
                    padding: EdgeInsets.only(bottom: 10.w),
                    child: Text(
                      'use “,” to separate key words'.tr,
                      style: TextStyle(fontSize: 13.sp, color: Colors.white.withValues(alpha: 0.6)),
                    ),
                  ),
                Container(
                  height: 200.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(11.r),
                    color: AppColor.colorsUtil('#F7D4A7').withValues(alpha: 0.2),
                  ),
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
                  child: CustomTextField.textField(
                    textCtl: controller.textCtl,
                    autofocus: true,
                    maxLength: showCount ? 300 : 600,
                    showCount: showCount,
                  ),
                ),
                20.verticalSpaceFromWidth,
                GradientColorBtn(
                  height: 45.w,
                  width: 234.w,
                  text: 'Done'.tr,
                  onTap: () {
                    controller.done();
                  },
                ),
                20.verticalSpaceFromWidth,
              ],
            ),
          ),
        );
      },
    );
  }
}
