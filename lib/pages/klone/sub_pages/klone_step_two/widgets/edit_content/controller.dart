import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditContentWidgetController extends GetxController {
  final String content;
  EditContentWidgetController({required this.content});

  late TextEditingController textCtl;
  @override
  void onInit() {
    super.onInit();
    textCtl = TextEditingController(text: content);
  }

  done() {
    Get.back(result: textCtl.text);
  }

  @override
  void onClose() {
    super.onClose();
    textCtl.dispose();
  }
}
