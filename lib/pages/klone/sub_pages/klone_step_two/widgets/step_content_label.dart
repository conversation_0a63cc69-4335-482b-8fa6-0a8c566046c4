import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StepContentLabelWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentLabelWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: rowChildren(),
    );
  }

  //内容组件列表
  List<Widget> rowChildren() {
    List labelList = [];
    if (contentModel.content != null && contentModel.content!.isNotEmpty) {
      labelList = contentModel.content!.split(',');
    }
    return [
      if (contentModel.showRedo == true)
        InkWell(
          onTap: () {
            KloneStepTwoPageController.to.fineTuneLogic.textWidgetRedo(contentModel);
          },
          child: Padding(
            padding: EdgeInsets.only(left: 10.w, bottom: 10.w, right: 10.w),
            child: Column(
              children: [
                Image.asset(
                  Assets.assetsImagesChatMsgRefresh,
                  width: 20.w,
                ),
                if (contentModel.source == 0)
                  Text(
                    'Redo'.tr,
                    style: TextStyle(fontSize: 10.sp, color: Colors.white),
                  ),
              ],
            ),
          ),
        ),
      _contentContainer(
        child: Stack(
          children: [
            Container(
              margin: EdgeInsets.only(right: 26.w),
              child: Wrap(
                spacing: 5.w,
                runSpacing: 10.w,
                children: labelList
                    .map(
                      (e) => InputChip(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                        labelPadding: EdgeInsets.zero,
                        backgroundColor: AppColor.colorsUtil('#F0BE72'),
                        showCheckmark: false,
                        onPressed: () {
                          // KloneStepTwoPageController.to.fineTuneLogic.labelDelete(
                          //     model: contentModel, index: contentModel.labelList!.indexOf(e));
                        },
                        //设置圆角
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            10.horizontalSpace,
                            Text(
                              (e as String).length > 20 ? '${e.substring(0, 20)}...' : e,
                              style: TextStyle(fontSize: 14.sp, color: Colors.white),
                            ),
                            10.horizontalSpace,
                            // Padding(
                            //   padding: EdgeInsets.symmetric(horizontal: 10.w),
                            //   child: Image.asset(
                            //     Assets.assetsImagesCloneLabelDel,
                            //     width: 12.w,
                            //   ),
                            // ),
                          ],
                        ),
                        side: BorderSide(width: 0, color: AppColor.colorsUtil('F0BE72')),
                      ),
                    )
                    .toList(),
              ),
            ),
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: InkWell(
                onTap: () {
                  KloneStepTwoPageController.to.editContentText(contentModel);
                },
                child: Padding(
                  padding: EdgeInsets.only(left: 10.w),
                  child: Image.asset(
                    Assets.assetsImagesCloneVoiceTextEdit,
                    width: 16.w,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ];
  }

  //容器
  Widget _contentContainer({required Widget child}) {
    return Opacity(
      opacity: 0.94,
      child: Container(
        constraints: BoxConstraints(maxWidth: 1.sw - 95.w),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.94),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(6.r),
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(16.r),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
        child: child,
      ),
    );
  }
}
