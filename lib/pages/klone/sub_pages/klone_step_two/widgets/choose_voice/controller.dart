import 'package:amor_app/common/models/clone_model/voice_bank_model.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CloneChooseVoiceController extends GetxController with GetSingleTickerProviderStateMixin {
  static CloneChooseVoiceController get to => Get.find();

  late TabController tabController;
  late ItemScrollController maleVoiceScrollCtl;
  late ItemScrollController femaleVoiceScrollCtl;
  var voiceClassifySelectedIndex = [0, 0].obs;
  var playingVoice = ''.obs;
  var selectedVoiceUrl = ''.obs;
  VoiceBankItemVoiceModel? selectedVoice;
  //播放器
  late AudioPlayer voicePlayer;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(
        length: 2,
        vsync: this,
        initialIndex: KloneStepTwoPageController.to.state.infoModel.gender == 0 ? 1 : 0);
    tabController.addListener(
      () async {
        if (tabController.animation!.value == tabController.index) {}
      },
    );
    maleVoiceScrollCtl = ItemScrollController();
    femaleVoiceScrollCtl = ItemScrollController();
    initPlayer();
  }

  //初始化播放器
  initPlayer() {
    voicePlayer = AudioPlayer();
    //监听播放
    voicePlayer.playerStateStream.listen((playState) {
      debugPrint(playState.processingState.toString());
      if (playState.processingState == ProcessingState.completed) {
        playingVoice.value = '';
      }
    });
    voicePlayer.playingStream.listen((event) {
      if (event == false) {
        playingVoice.value = '';
      }
    });
  }

  //选择音频分类
  selectVoiceClassify({required int index}) {
    voiceClassifySelectedIndex.replaceRange(tabController.index, tabController.index + 1, [index]);
    (tabController.index == 0 ? maleVoiceScrollCtl : femaleVoiceScrollCtl).scrollTo(
      index: index,
      duration: const Duration(milliseconds: 200),
      alignment: 0.45,
    );
  }

  //播放音频
  playVoice({required VoiceBankItemVoiceModel model}) async {
    if (model.voiceUrl == null || model.voiceUrl!.isEmpty) {
      return;
    }
    if (playingVoice.value == model.voiceUrl) {
      voicePlayer.stop();
      return;
    }
    playingVoice.value = model.voiceUrl!;
    await voicePlayer.setAudioSource(AudioSource.uri(Uri.parse(model.voiceUrl!)));
    voicePlayer.play();
  }

  //选择音频
  selectVoice(VoiceBankItemVoiceModel value) {
    selectedVoiceUrl.value = value.voiceUrl ?? '';
    selectedVoice = value;
  }

  done() {
    Get.back(result: selectedVoice);
  }

  @override
  void onClose() {
    super.onClose();
    tabController.dispose();
    voicePlayer.dispose();
  }
}
