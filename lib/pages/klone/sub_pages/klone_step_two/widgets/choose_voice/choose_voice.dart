import 'package:amor_app/common/models/clone_model/voice_bank_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'controller.dart';

class CloneChooseVoice extends StatelessWidget {
  const CloneChooseVoice({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CloneChooseVoiceController>(
      init: CloneChooseVoiceController(),
      builder: (controller) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
            gradient: LinearGradient(
              colors: [
                AppColor.colorsUtil('#27292D').withValues(alpha: 0.9),
                AppColor.colorsUtil('#111214'),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              20.verticalSpaceFromWidth,
              Text(
                'Choose From'.tr,
                style: TextStyle(
                    fontSize: 17.sp,
                    color: Colors.white,
                    fontStyle: FontStyle.italic,
                    fontWeight: FontWeight.w800),
              ),
              // 16.verticalSpaceFromWidth,
              TabBar(
                overlayColor: WidgetStateProperty.all(Colors.transparent),
                // isScrollable: true,
                padding: EdgeInsets.zero,
                dividerColor: Colors.transparent,
                controller: controller.tabController,
                indicator: BoxDecoration(
                  color: AppColor.colorsUtil('#F0BE72'),
                  borderRadius: BorderRadius.circular(10),
                ),
                indicatorPadding: EdgeInsets.fromLTRB((1.sw / 4) - 10, 40, (1.sw / 4) - 10, 5),
                labelColor: AppColor.colorsUtil('#F0BE72'),
                indicatorSize: TabBarIndicatorSize.tab,
                unselectedLabelColor: Colors.white.withValues(alpha: 0.3),
                labelStyle: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.normal,
                ),
                tabs: [
                  Tab(text: 'Male Voice'.tr),
                  Tab(text: 'Female Voice'.tr),
                ],
              ),
              // 20.verticalSpaceFromWidth,
              SizedBox(
                width: 1.sw,
                height: 480.w,
                child: TabBarView(
                  controller: controller.tabController,
                  children: List.generate(
                    2,
                    (index) => KeepAliveWrapper(
                      child: tabbarViewWidget(tabbarIndex: index),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget tabbarViewWidget({required int tabbarIndex}) {
    late List<VoiceBankItemModel> voiceList;
    if (tabbarIndex == 0) {
      voiceList = KloneStepTwoPageController.to.state.voiceBankModel!.male ?? [];
    } else {
      voiceList = KloneStepTwoPageController.to.state.voiceBankModel!.famale ?? [];
    }
    return Column(
      children: [
        Container(
          width: 1.sw,
          height: 28.w,
          margin: EdgeInsets.symmetric(vertical: 15.w),
          child: NotificationListener<OverscrollIndicatorNotification>(
            onNotification: (OverscrollIndicatorNotification? overscroll) {
              overscroll!.disallowIndicator();
              return true;
            },
            child: ScrollablePositionedList.builder(
              physics: const ClampingScrollPhysics(),
              itemScrollController: tabbarIndex == 0
                  ? CloneChooseVoiceController.to.maleVoiceScrollCtl
                  : CloneChooseVoiceController.to.femaleVoiceScrollCtl,
              padding: REdgeInsets.symmetric(horizontal: 10),
              scrollDirection: Axis.horizontal,
              itemCount: voiceList.length,
              itemBuilder: (BuildContext context, int index) {
                return InkWell(
                  onTap: () => CloneChooseVoiceController.to.selectVoiceClassify(index: index),
                  child: Obx(
                    () => Container(
                      height: 28.w,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      margin: EdgeInsets.symmetric(horizontal: 5.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(28.w / 2),
                        color: CloneChooseVoiceController.to.voiceClassifySelectedIndex
                                    .elementAt(tabbarIndex) ==
                                index
                            ? AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.7)
                            : AppColor.colorsUtil('#111111').withValues(alpha: 0.3),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        voiceList.elementAt(index).cateName ?? '',
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        Expanded(
          child: Obx(
            () {
              return ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  itemBuilder: (context, index) {
                    return voiceItemWidget(
                        model: voiceList
                            .elementAt(CloneChooseVoiceController.to.voiceClassifySelectedIndex
                                .elementAt(tabbarIndex))
                            .voice!
                            .elementAt(index));
                  },
                  separatorBuilder: (context, index) {
                    return 16.verticalSpaceFromWidth;
                  },
                  itemCount: voiceList
                      .elementAt(CloneChooseVoiceController.to.voiceClassifySelectedIndex
                          .elementAt(tabbarIndex))
                      .voice!
                      .length);
            },
          ),
        ),
        30.verticalSpaceFromWidth,
        GradientColorBtn(
          height: 45.w,
          width: 234.w,
          text: 'Done',
          onTap: () {
            CloneChooseVoiceController.to.done();
          },
        ),
        20.verticalSpaceFromWidth,
      ],
    );
  }

  Widget voiceItemWidget({required VoiceBankItemVoiceModel model}) {
    return Row(
      children: [
        16.horizontalSpace,
        Expanded(
          child: InkWell(
            onTap: () {
              CloneChooseVoiceController.to.playVoice(model: model);
            },
            child: Container(
              height: 46.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(46.w / 2),
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: Row(
                children: [
                  10.horizontalSpace,
                  Obx(
                    () => Image.asset(
                      CloneChooseVoiceController.to.playingVoice.value == model.voiceUrl
                          ? Assets.assetsImagesCloneVoicePause
                          : Assets.assetsImagesCloneVoicePlay,
                      width: 26.w,
                    ),
                  ),
                  10.horizontalSpace,
                  Image.asset(
                    Assets.assetsImagesCloneListItemVoice,
                    width: 20.w,
                  ),
                  10.horizontalSpace,
                  Text(
                    model.voiceName ?? '',
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  //取消时长展示
                  /*
                  Expanded(
                    child: Text(
                      model.voiceName ?? '',
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Image.asset(
                    Assets.assetsImagesCloneListItemVoice,
                    width: 20.w,
                  ),
                  6.horizontalSpace,
                  Text(
                    '${model.voiceVal ?? '0'}”',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  16.horizontalSpace,
                  */
                ],
              ),
            ),
          ),
        ),
        3.horizontalSpace,
        InkWell(
          onTap: () => CloneChooseVoiceController.to.selectVoice(model),
          child: Container(
            width: 56.w,
            height: 46.w,
            alignment: Alignment.center,
            child: Obx(
              () => Image.asset(
                CloneChooseVoiceController.to.selectedVoiceUrl.value == model.voiceUrl
                    ? Assets.assetsImagesCloneVoiceSelected
                    : Assets.assetsImagesCloneVoiceSelect,
                width: 24.w,
              ),
            ),
          ),
        )
      ],
    );
  }
}
