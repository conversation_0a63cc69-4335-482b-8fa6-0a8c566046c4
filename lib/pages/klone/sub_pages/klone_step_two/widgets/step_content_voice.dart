import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class StepContentVoiceWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentVoiceWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: rowChildren(),
    );
  }

  //内容组件列表
  List<Widget> rowChildren() {
    return [
      //克隆完成后不能删除、已上传完成的语音不能删除
      if (KloneStepTwoPageController.to.state.cloneDone.value == false &&
          contentModel.complete != true)
        InkWell(
          onTap: () {
            if (KloneStepTwoPageController.to.state.recordState.value == 1) {
              return;
            }
            KloneStepTwoPageController.to.voiceCloneLogic.removeRecordFile(model: contentModel);
          },
          //删除按钮
          child: Padding(
            padding: EdgeInsets.all(10.w),
            child: Image.asset(
              Assets.assetsImagesCloneRecordVoiceRemove,
              width: 22.w,
            ),
          ),
        ),
      _contentContainer(
        child: Row(
          children: [
            InkWell(
              onTap: () {
                if (KloneStepTwoPageController.to.state.recordState.value == 1) {
                  return;
                }
                KloneStepTwoPageController.to.voiceCloneLogic
                    .playRecordFile(msgId: contentModel.msgId ?? '');
              },
              child: Padding(
                padding: EdgeInsets.fromLTRB(20.w, 15.w, 10.w, 15.w),
                child: Obx(
                  () {
                    return Image.asset(
                      KloneStepTwoPageController.to.state.recordFilePlaying.value == true &&
                              KloneStepTwoPageController.to.state.playingMsgId.value ==
                                  contentModel.msgId
                          ? Assets.assetsImagesCloneRecordVoicePause
                          : Assets.assetsImagesCloneRecordVoicePlay,
                      width: 18.w,
                    );
                  },
                ),
              ),
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                height: 4.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.w / 2),
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                alignment: Alignment.centerLeft,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double parentWidth = constraints.maxWidth; // 获取父组件的宽度
                    if (contentModel.duration == null) {
                      return Container();
                    }
                    return Obx(
                      () => Container(
                        width: KloneStepTwoPageController.to.state.playingMsgId.value !=
                                contentModel.msgId
                            ? 0
                            : parentWidth *
                                (KloneStepTwoPageController.to.state.playDuration.value /
                                    contentModel.duration!),
                        height: 4.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4.w / 2),
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 10.w, right: 20.w),
              child: Obx(
                () => Text(
                  KloneStepTwoPageController.to.state.playingMsgId.value != contentModel.msgId
                      ? CommonUtil.durationTransformPro(contentModel.duration ?? 0)
                      : CommonUtil.durationTransformPro((contentModel.duration ?? 0) -
                          KloneStepTwoPageController.to.state.playDuration.value),
                  style: TextStyle(fontSize: 12.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    ];
  }

  //容器
  Widget _contentContainer({required Widget child}) {
    return Opacity(
      opacity: 0.94,
      child: Container(
        constraints: BoxConstraints(maxWidth: 1.sw - 95.w),
        decoration: BoxDecoration(
          color: AppColor.colorsUtil('#00B0F9').withValues(alpha: 0.94),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(6.r),
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(16.r),
          ),
        ),
        // padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
        child: child,
      ),
    );
  }
}
