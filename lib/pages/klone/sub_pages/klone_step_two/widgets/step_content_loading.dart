import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class StepContentLoadingWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentLoadingWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: contentModel.source == 1 ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: contentModel.source == 1 ? rowChildren().reversed.toList() : rowChildren(),
    );
  }

  //内容组件列表
  List<Widget> rowChildren() {
    return [
      _contentContainer(
        child: Lottie.asset('assets/lottie/klone_loading.json',
            animate: true, fit: BoxFit.contain, width: 50.w, height: 14.w),
      ),
    ];
  }

  //容器
  Widget _contentContainer({required Widget child}) {
    return Opacity(
      opacity: 0.94,
      child: Container(
        constraints: BoxConstraints(maxWidth: 1.sw - 95.w),
        decoration: BoxDecoration(
          color: contentModel.source == 1
              ? Colors.white.withValues(alpha: 0.94)
              : AppColor.colorsUtil('#323235'),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(contentModel.source == 1 ? 16.r : 6.r),
            topRight: Radius.circular(contentModel.source == 1 ? 6.r : 16.r),
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(16.r),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
        child: child,
      ),
    );
  }
}
