import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StepContentRecoedWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentRecoedWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Obx(
            () => ImageBtn(
                iconSting: KloneStepTwoPageController.to.state.recordState.value == 0
                    ? Assets.assetsImagesCloneRecordStart
                    : Assets.assetsImagesCloneRecordStop,
                onPressed: () {
                  KloneStepTwoPageController.to.voiceCloneLogic.record();
                },
                width: 70.w,
                height: 70.w),
          ),
          10.verticalSpaceFromWidth,
          Obx(
            () => Text(
              KloneStepTwoPageController.to.state.recordState.value == 0
                  ? 'Click to start recording'.tr
                  : '00:${_formatNumber(KloneStepTwoPageController.to.state.recordDuration.value)}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
                shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 1)],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatNumber(int number) {
    String numberStr = number.toString();
    if (number < 10) {
      numberStr = '0$numberStr';
    }

    return numberStr;
  }
}
