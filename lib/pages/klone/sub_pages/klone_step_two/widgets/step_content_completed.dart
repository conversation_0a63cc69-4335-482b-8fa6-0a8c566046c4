import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class StepContentCompletedWidget extends StatefulWidget {
  const StepContentCompletedWidget({super.key});

  @override
  State<StepContentCompletedWidget> createState() => _StepContentCompletedWidgetState();
}

class _StepContentCompletedWidgetState extends State<StepContentCompletedWidget> {
  int showIndex = 0;
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        showIndex = 1;
      });
      scrollToBottom();
    });
    Future.delayed(const Duration(milliseconds: 4000), () {
      setState(() {
        showIndex = 2;
      });
      scrollToBottom();
    });
  }

  scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 20), () {
      KloneStepTwoPageController.to.scrollController.animateTo(
          KloneStepTwoPageController.to.scrollController.position.maxScrollExtent,
          curve: Curves.ease,
          duration: const Duration(milliseconds: 200));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 140.w,
          alignment: Alignment.center,
          child: Lottie.asset('assets/lottie/klone_scan/klone_scan.json', animate: true),
        ),
        10.verticalSpaceFromWidth,
        if (showIndex > 0)
          Text(
            'Completed!'.tr,
            style: TextStyle(
                fontSize: 22.sp,
                color: AppColor.colorsUtil('#F0BE72'),
                fontWeight: FontWeight.w800,
                fontStyle: FontStyle.italic),
          ),
        if (showIndex > 1)
          Text(
            'clone completed tip'.trArgs([KloneStepTwoPageController.to.state.name.value]),
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 15.sp,
                height: 20 / 15,
                color: AppColor.colorsUtil('#F0BE72'),
                fontStyle: FontStyle.italic),
          ),
      ],
    );
  }
}
