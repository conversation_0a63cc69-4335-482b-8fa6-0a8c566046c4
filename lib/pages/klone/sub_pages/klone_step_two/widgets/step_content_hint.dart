import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';

class StepContentHintWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentHintWidget({super.key, required this.contentModel});
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (contentModel.questionIndex == 0 ||
              KloneStepTwoPageController.to.state.cloneDone.value == true)
            Container(
              height: 24.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.w / 2),
                color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.5),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              alignment: Alignment.center,
              child: Text(
                contentModel.content ?? '',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
