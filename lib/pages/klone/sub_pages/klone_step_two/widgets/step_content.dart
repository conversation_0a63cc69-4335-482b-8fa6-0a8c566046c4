import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/widgets/step_content_button.dart';
import 'package:flutter/material.dart';
import 'step_content_completed.dart';
import 'step_content_file.dart';
import 'step_content_hint.dart';
import 'step_content_label.dart';
import 'step_content_loading.dart';
import 'step_content_option.dart';
import 'step_content_record.dart';
import 'step_content_text.dart';
import 'step_content_voice.dart';

class CloneStepContent extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const CloneStepContent({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 20.w),
      child: _content(),
    );
  }

  Widget _content() {
    //普通文本、多颜色文本、带编辑按钮文本
    if (contentModel.type == 1 || contentModel.type == 3 || contentModel.type == 4) {
      return StepContentTextWidget(contentModel: contentModel);
    }
    //选项组件
    if (contentModel.type == 2) {
      return StepContentOptionWidget(contentModel: contentModel);
    }
    //已选择的文件
    if (contentModel.type == 5) {
      return StepContentFileWidget(contentModel: contentModel);
    }
    //透明提示
    if (contentModel.type == 6) {
      return StepContentHintWidget(contentModel: contentModel);
    }
    //录音
    if (contentModel.type == 7) {
      return StepContentRecoedWidget(contentModel: contentModel);
    }
    //录制的音频
    if (contentModel.type == 8) {
      return StepContentVoiceWidget(contentModel: contentModel);
    }
    //按钮
    if (contentModel.type == 9) {
      return StepContentButtonWidget(contentModel: contentModel);
    }
    //标签
    if (contentModel.type == 10) {
      return StepContentLabelWidget(contentModel: contentModel);
    }
    //完成
    if (contentModel.type == 11) {
      return const StepContentCompletedWidget();
    }
    //loading
    if (contentModel.type == 12) {
      return StepContentLoadingWidget(contentModel: contentModel);
    }

    return Container();
  }
}
