import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StepContentOptionWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentOptionWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _contentContainer(
          child: ListView.separated(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) => itemWidget(
              onTap: () {
                //只能选择一次  或录音中
                if (contentModel.optionsSelected != null ||
                    KloneStepTwoPageController.to.state.recordState.value == 1) {
                  return;
                }
                //刷新按钮状态
                int modelIndex =
                    KloneStepTwoPageController.to.state.messageList.indexOf(contentModel);
                contentModel.optionsSelected = index;
                KloneStepTwoPageController.to.state.messageList
                    .replaceRange(modelIndex, modelIndex + 1, [contentModel]);
                KloneStepTwoPageController.to.refreshWidgetList(msgId: contentModel.msgId);
                if (index == 0) {
                  KloneStepTwoPageController.to.showVoiceBank();
                  ReportUtil.reportEvents(page: ReportUtil.cloneVT, action: ReportUtil.select);
                }
                if (index == 1) {
                  KloneStepTwoPageController.to.voiceCloneLogic.selectVoiceFile();
                  ReportUtil.reportEvents(page: ReportUtil.cloneVT, action: ReportUtil.upload);
                }
                if (index == 2) {
                  KloneStepTwoPageController.to.voiceCloneLogic.recordVoiceStart();
                  ReportUtil.reportEvents(page: ReportUtil.cloneVT, action: ReportUtil.record);
                }
              },
              index: index,
            ),
            separatorBuilder: (context, index) => 15.verticalSpaceFromWidth,
            itemCount: KloneStepTwoPageController.to.state.showRecordVoice == true ? 3 : 2,
          ),
        ),
        //整个消息列表只有一个 option组件 展示重做按钮
        if (contentModel.showRedo == true)
          InkWell(
            onTap: () => KloneStepTwoPageController.to.voiceCloneLogic.optionWidgetRedo(),
            child: Padding(
              padding: EdgeInsets.only(left: 10.w, bottom: 10.w),
              child: Column(
                children: [
                  Image.asset(
                    Assets.assetsImagesChatMsgRefresh,
                    width: 20.w,
                  ),
                  Text(
                    'Redo'.tr,
                    style: TextStyle(fontSize: 10.sp, color: Colors.white),
                  ),
                ],
              ),
            ),
          )
      ],
    );
  }

  Widget itemWidget({required VoidCallback onTap, required int index}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 35.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(35.w),
          color: contentModel.optionsSelected == index
              ? AppColor.colorsUtil('#30CEA3')
              : Colors.white.withValues(alpha: 0.3),
        ),
        alignment: Alignment.center,
        child: Text(
          index == 0
              ? 'Select from a voice bank'
              : index == 1
                  ? 'Upload an audio from ${KloneStepTwoPageController.to.state.name.value}'
                  : "Record ${KloneStepTwoPageController.to.state.name.value}'s Voice",
          style: TextStyle(
              fontSize: 13.sp,
              color: (contentModel.optionsSelected == null || contentModel.optionsSelected == index)
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.3)),
        ),
      ),
    );
  }

  //消息容器
  Widget _contentContainer({required Widget child}) {
    return Container(
      constraints: BoxConstraints(maxWidth: 1.sw - 95.w),
      decoration: BoxDecoration(
        color: AppColor.colorsUtil('#323235').withValues(alpha: 0.5),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(6.r),
          topRight: Radius.circular(16.r),
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
      ),
      padding: EdgeInsets.all(20.w),
      child: child,
    );
  }
}
