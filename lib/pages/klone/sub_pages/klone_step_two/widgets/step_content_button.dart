import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:flutter/material.dart';

class StepContentButtonWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentButtonWidget({super.key, required this.contentModel});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GradientColorBtn(
        height: 45.w,
        text: contentModel.content ?? '',
        width: 234.w,
        onTap: () {
          if (KloneStepTwoPageController.to.state.recordState.value == 1) {
            return;
          }
          //录音 下一步
          if (contentModel.msgId == 'Record') {
            int modelIndex = KloneStepTwoPageController.to.state.messageList.indexOf(contentModel);
            KloneStepTwoPageController.to.voiceCloneLogic.uploadVoice(
                fileIndex: KloneStepTwoPageController.to.state.recordVoiceFilePath.length - 1,
                messageIndex: modelIndex - 3,
                lastVoice: false);
            return;
          }
          //声音克隆完成 进入下一步
          if (contentModel.msgId == 'FineTune') {
            KloneStepTwoPageController.to.voiceCloneLogic.vtDone();
            return;
          }
          KloneStepTwoPageController.to.fineTuneLogic.nextAction(model: contentModel);
        },
      ),
    );
  }
}
