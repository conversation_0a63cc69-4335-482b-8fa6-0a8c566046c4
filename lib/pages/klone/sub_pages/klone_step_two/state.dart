import 'dart:async';

import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/models/clone_model/voice_bank_model.dart';
import 'package:amor_app/common/utils/record_util/record_util.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

class KloneStepTwoPageState {
  late String modelNo;
  late CloneCreatModel infoModel;
  var name = ''.obs;
  var avatarUrl = ''.obs;
  var bgImageUrl = ''.obs;
  //是否展示录音 编辑时默认展示 如果第一步选择了No就不展示
  bool showRecordVoice = true;
  //声音库
  VoiceBankModel? voiceBankModel;
  List<CloneStepContentModel> messageList = [];
  //添加组件动画倒计时
  Timer? addMessageTimer;
  //选择的声音 录音、声音库、上传
  String selectedVoice = '';
  //选择声音的发音人名称
  String? selectedVoiceBankName;
  //语音来源 select:声音库 upload:上传的文件 record:录音
  String voiceSource = '';
  //录音类
  late RecordUtil recordUtil;
  var recordState = 0.obs; //1:录音中
  //录音倒计时
  Timer? recordTimer;
  var recordDuration = 0.obs;
  //录音文件
  List<String> recordVoiceFilePath = [];
  //录音播放器
  AudioPlayer? voicePlayer;
  //播放状态
  var recordFilePlaying = false.obs;
  bool playCompleted = true;
  var playDuration = 0.obs;
  //正在播放的消息ID
  var playingMsgId = ''.obs;
  //声音克隆是否完成
  var cloneDone = false.obs;
  //全部操作已完成
  var cloneCompleted = false.obs;
  //FT当前问题下标
  int ftQuestionIndex = 0;
  //正在播放的问题
  String playingQuestion = '';
  //克隆完成 ft阶段 获取答案接口返回发音人
  String voiceParam = '';
  //点击next 正在保存
  bool ontapNext = false;
}
