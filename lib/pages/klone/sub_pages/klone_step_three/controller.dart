import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/tags/tags_select_widget.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:share_plus/share_plus.dart';

class KloneStepThreePageController extends GetxController {
  var selectedPermission = 1.obs; //默认公开
  late String modelNo;
  //播放器
  late AudioPlayer voicePlayer;
  var playing = false.obs;
  CloneInfoModel infoModel = CloneInfoModel();
  bool showHot = false;
  //标签切换
  var tagSwitchType = [];
  List<TagsItemModel> tagsList = [];
  List<int> selectedTagsIndex = [];
  //是否选中nsfw、bdsm的tag
  String selectedSpecailTag = '';
  //选中的tag
  List<TagsItemModel> selectedTagsList = [];

  @override
  void onInit() {
    super.onInit();
    modelNo = Get.arguments;
    getData();
    initPlayer();
    ReportUtil.reportViews(page: ReportUtil.cloneWrap, action: ReportUtil.view);
  }

  getData() async {
    CloneInfoModel? model = await CloneApis.cloneWrap(modelNo: modelNo);
    if (model != null) {
      infoModel = model;
      showHot = infoModel.privatePower == false && UserService.to.isVip == false;
      update();
      playVoice();
    }
  }

  //初始化播放器
  initPlayer() {
    voicePlayer = AudioPlayer();
    //监听播放
    voicePlayer.playerStateStream.listen((playState) {
      if (playState.processingState == ProcessingState.completed) {
        playing.value = false;
      }
    });
    voicePlayer.playingStream.listen((event) {
      playing.value = event;
    });
  }

  //播放
  playVoice() async {
    if (infoModel.welcomeVoice == null || infoModel.welcomeVoice!.isEmpty) {
      return;
    }
    if (playing.value == true) {
      voicePlayer.stop();
      return;
    }
    playing.value = true;
    await voicePlayer.setAudioSource(AudioSource.uri(Uri.parse(infoModel.welcomeVoice!)));
    voicePlayer.play();
  }

  //选择权限
  selecpermission(int index) {
    if (selectedPermission.value == index) {
      return;
    }
    if (index == 1) {
      selectedPermission.value = index;
      return;
    }
    if (infoModel.privatePower == true && UserService.to.isVip == false) {
      selectedPrivateDialog(free: true);
      return;
    }
    if (infoModel.privatePower == false) {
      selectedPrivateDialog(free: false);
      return;
    }
    selectedPermission.value = index;
  }

  //首次选择 Private 弹窗免费提示  之后选择Private 如果不是会员 就弹窗提示
  selectedPrivateDialog({required bool free}) async {
    bool? result = await Get.dialog(
      CustomDialogWidget(
        title: "System",
        subTitle:
            free ? 'Free Account gets one private Clone, sure to use it?' : 'Private ones used up.',
        confirmTitle: free ? 'Yes' : 'Go Hot',
        cancelTitle: free ? 'Public' : 'Set Public',
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      if (free) {
        selectedPermission.value = 0;
      } else {
        ReportUtil.reportViews(
            page: ReportUtil.cloneWrap, action: ReportUtil.go, value: ReportUtil.hot);
        Analytics().logEvent(Analytics.clickHot, screen: Analytics.pageFinishclone);
        Analytics().logEvent(
          Analytics.view,
          sourceEvent: Analytics.clickHot,
          sourceChar: null,
          sourceScreen: Analytics.pageFinishclone,
          screen: Analytics.pageHot,
        );
        await PurchaseSheet.show(buyType: UserService.to.isVip ? 1 : 0, page: ReportUtil.cloneWrap);
      }
      return;
    }
  }

  done() async {
    Map? result = await CloneApis.cloneDone(
        modelNo: modelNo,
        purview: selectedPermission.value,
        tags: selectedTagsList.map((e) => e.id).toList());
    if (result != null && result['sessionNo'] != null) {
      if (selectedPermission.value == 0) {
        //已选择了私人  第二次就要展示hot图标了
        Get.find<SPService>().set(spCreatCloneShowHot, true);
      }
      ReportUtil.reportEvents(page: ReportUtil.cloneWrap, action: ReportUtil.finish);
      Get.offNamedUntil(Routes.session, ModalRoute.withName(Routes.tabs),
          arguments: result['sessionNo']);
    }
  }

  //分享
  share() async {
    Map? shareParam = await CommonApis.commonShare();
    if (shareParam != null) {
      Share.share(shareParam['text']);
    }
  }

  //选择tag
  selectTags() async {
    if (tagsList.isEmpty) {
      List<TagsItemModel> result = await CommonApis.tagsQuery(
        cacheCallBack: (cache) {
          if (cache.isNotEmpty) {
            tagsList = cache;
            showTagsDialog();
          }
        },
      );
      if (tagsList.isEmpty && result.isNotEmpty) {
        tagsList = result;
        showTagsDialog();
      }
    } else {
      showTagsDialog();
    }
  }

  showTagsDialog() async {
    await Get.dialog(
        TagsSelectWidget(
          tagsList: tagsList,
          selectedTagsIndex: selectedTagsIndex,
          tagSwitchType: tagSwitchType,
          multiple: true,
          backBtn: 'right',
        ),
        useSafeArea: false,
        transitionDuration: Duration.zero);
    selectedTagsList.clear();
    selectedSpecailTag = '';
    for (var element in selectedTagsIndex) {
      if (tagsList[element].label == 'nsfw') {
        selectedSpecailTag = 'nsfw';
      }
      //nsfw优先级高于bdsm
      if (tagsList[element].label == 'bdsm' && selectedSpecailTag != 'nsfw') {
        selectedSpecailTag = 'bdsm';
      }
      selectedTagsList.add(tagsList[element]);
    }
    update();
  }

  @override
  void dispose() {
    super.dispose();
    voicePlayer.dispose();
    ReportUtil.reportViews(page: ReportUtil.cloneWrap, action: ReportUtil.quit);
  }
}
