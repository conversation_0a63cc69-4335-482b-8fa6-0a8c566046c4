import 'dart:ui';

import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_three/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:lottie/lottie.dart';

class KloneStepThreePage extends GetView<KloneStepThreePageController> {
  const KloneStepThreePage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        body: GetBuilder<KloneStepThreePageController>(
          builder: (controller) => Stack(
            children: [
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: controller.infoModel.bgImg ?? '',
                  placeholder: (context, url) {
                    return Container();
                  },
                  errorWidget: (context, url, error) {
                    return Container();
                  },
                  fit: BoxFit.cover,
                ),
              ),
              //底部阴影
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 1.sw,
                  height: 500.h + CommonUtil.bottomBarHeight(),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Colors.transparent, Colors.black]),
                  ),
                ),
              ),

              Align(
                alignment: Alignment.bottomCenter,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            //打招呼
                            messageContent(),
                            8.verticalSpaceFromWidth,
                            //名称
                            Text(
                              controller.infoModel.modelName ?? '',
                              style: TextStyle(
                                fontSize: 26.sp,
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.italic,
                                shadows: [
                                  Shadow(color: Colors.black.withValues(alpha: 0.25), blurRadius: 6)
                                ],
                              ),
                            ),
                            2.verticalSpaceFromWidth,
                            tags(),
                            6.verticalSpaceFromWidth,
                            Text(
                              controller.infoModel.intro ?? '',
                              style: TextStyle(
                                fontSize: 14.sp,
                                height: 18.sp / 14.sp,
                                color: Colors.white,
                                shadows: [
                                  Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)
                                ],
                              ),
                            ),
                            30.verticalSpaceFromWidth,
                            Center(
                              child: GradientColorBtn(
                                height: 45.w,
                                width: 234.w,
                                text: 'Done & Talk'.tr,
                                onTap: () => controller.done(),
                              ),
                            ),
                            InkWell(
                              onTap: () => controller.share(),
                              child: Padding(
                                padding: EdgeInsets.only(bottom: 20.w, top: 12.w),
                                child: Center(
                                  child: Text(
                                    'clone share'.trArgs([controller.infoModel.modelName ?? '']),
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      height: 18.sp / 14.sp,
                                      color: AppColor.colorsUtil('#F0BE72'),
                                      shadows: [
                                        Shadow(
                                            color: Colors.black.withValues(alpha: 0.5),
                                            blurRadius: 2)
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            Padding(
                              padding: EdgeInsets.only(left: 40.w, right: 45.w),
                              child: Row(
                                children: [
                                  Obx(
                                    () => permissionButton(
                                        onTap: () => controller.selecpermission(0),
                                        selected: controller.selectedPermission.value == 0,
                                        title: 'Private'.tr),
                                  ),
                                  2.horizontalSpace,
                                  if (controller.showHot)
                                    Padding(
                                      padding: EdgeInsets.only(bottom: 3.w),
                                      child: Image.asset(Assets.assetsImagesAmorsHot, width: 50.w),
                                    ),
                                  const Spacer(),
                                  Obx(
                                    () => permissionButton(
                                        onTap: () => controller.selecpermission(1),
                                        selected: controller.selectedPermission.value == 1,
                                        title: 'Public'.tr),
                                  ),
                                ],
                              ),
                            ),
                            10.verticalSpaceFromWidth,
                            Text(
                              'clone note'.tr,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: Colors.white.withValues(alpha: 0.5),
                                shadows: [
                                  Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 20.h + CommonUtil.bottomBarHeight(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              //选择tag
              if (AppService.audit == false)
                Positioned(
                  right: 0,
                  top: 10.h + CommonUtil.statusBarHeight(context),
                  child: ImageBtn(
                      iconSting: Assets.assetsImagesCloneSelectTags,
                      onPressed: () {
                        controller.selectTags();
                      },
                      width: 80.w,
                      height: 36.w),
                ),
            ],
          ),
        ),
      ),
    );
  }

  //权限按钮
  Widget permissionButton(
      {required VoidCallback onTap, required bool selected, required String title}) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Image.asset(
            selected
                ? Assets.assetsImagesClonePermissionSelected
                : Assets.assetsImagesClonePermissionSelect,
            width: 18.w,
          ),
          6.horizontalSpace,
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
              shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)],
            ),
          ),
        ],
      ),
    );
  }

  //打招呼消息
  Widget messageContent() {
    return Stack(
      children: [
        Padding(
          padding: controller.infoModel.welcomeVoice == null
              ? EdgeInsets.zero
              : EdgeInsets.only(bottom: 25.w / 2),
          child: ClipRRect(
            clipBehavior: Clip.antiAlias,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(6.r),
              topRight: Radius.circular(16.r),
              bottomRight: Radius.circular(16.r),
              bottomLeft: Radius.circular(16.r),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
              child: Container(
                constraints: BoxConstraints(maxWidth: 1.sw - 96.w),
                decoration: BoxDecoration(
                  color: AppColor.colorsUtil('#404048').withValues(alpha: 0.5),
                ),
                padding: REdgeInsets.fromLTRB(20, 14, 20, 20),
                child: Text(
                  controller.infoModel.welcomeMsg ?? '',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        if (controller.infoModel.welcomeVoice != null)
          Positioned(
            bottom: 0,
            left: 20.w,
            child: voicePlayButton(),
          ),
      ],
    );
  }

  //播放按钮
  Widget voicePlayButton() {
    return GestureDetector(
      onTap: () {
        controller.playVoice();
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 25.w,
        padding: REdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(4.r),
              topRight: Radius.circular(25.w / 2),
              bottomRight: Radius.circular(25.w / 2),
              bottomLeft: Radius.circular(25.w / 2),
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69351')],
            )),
        child: Obx(
          () => Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              /*
              //下载中
              controller.voicePlayStatus.value == 2
                  ? SizedBox(
                      height: 13.w,
                      width: 13.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primaryText),
                        backgroundColor: AppColor.primaryText.withValues(alpha: 0.2),
                      ),
                    )
                  : */
              controller.playing.value == true
                  ? SizedBox(
                      height: 13.w,
                      width: 17.w,
                      child: Lottie.asset('assets/lottie/session_voice_play.json', animate: true),
                    )
                  : Image.asset(
                      Assets.assetsImagesSessionPagePlay,
                      width: 12.w,
                      height: 12.w,
                    ),
              Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: Text(
                  '${(controller.infoModel.welcomeMsg ?? '').length ~/ 9}″',
                  style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColor.primaryText,
                      height: 1.2,
                      fontWeight: FontWeight.w500),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget tags() {
    List tags = List.from(controller.selectedTagsList);
    if (controller.selectedSpecailTag.isNotEmpty) {
      tags.insert(0, controller.selectedSpecailTag.toUpperCase());
    }
    return Wrap(
      spacing: 7.w,
      runSpacing: 4.w,
      children: List.generate(tags.length, (index) {
        return Container(
          height: 14.w,
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          margin: EdgeInsets.only(bottom: 4.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(4.r),
              bottomLeft: Radius.circular(4.r),
              bottomRight: Radius.circular(4.r),
            ),
            border: (tags[index] == 'NSFW' || tags[index] == 'BDSM')
                ? Border.all(color: Colors.white.withValues(alpha: 0.7), width: 0.5)
                : GradientBoxBorder(
                    gradient: LinearGradient(colors: [
                      Colors.white.withValues(alpha: 0.8),
                      Colors.transparent,
                    ]),
                  ),
            color: AppColor.colorsUtil('#00B0F9'),
            gradient: tags[index] == 'BDSM'
                ? null
                : LinearGradient(
                    colors: tags[index] == 'NSFW'
                        ? [
                            AppColor.colorsUtil('#FF68B9'),
                            AppColor.colorsUtil('#FF3D62'),
                          ]
                        : [
                            AppColor.colorsUtil('#3E2D12').withValues(alpha: 0.8),
                            AppColor.colorsUtil('#1A1000').withValues(alpha: 0.6),
                          ]),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                (tags[index] == 'NSFW' || tags[index] == 'BDSM')
                    ? tags[index]
                    : (tags[index] as TagsItemModel).name,
                style: TextStyle(
                    fontSize: 8.sp,
                    color: (tags[index] == 'NSFW' || tags[index] == 'BDSM')
                        ? AppColor.primaryText
                        : AppColor.colorsUtil('#FFDEAE'),
                    height: 9 / 8),
              )
            ],
          ),
        );
      }),
    );
  }
}
