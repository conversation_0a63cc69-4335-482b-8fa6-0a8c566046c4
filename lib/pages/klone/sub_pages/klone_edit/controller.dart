import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/tags/tags_select_widget.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:share_plus/share_plus.dart';

class KloneEditPageController extends GetxController {
  static KloneEditPageController get to => Get.find();

  String? modelNo;
  //播放器
  late AudioPlayer voicePlayer;
  late ScrollController tagsWidgetScrCtl;
  var playing = false.obs;
  CloneEditModel editModel = CloneEditModel();
  List<CloneEditImageModel> bgImages = [];
  List selectedImageList = [];
  //上传中的图片文件地址
  List uploadImageList = [];
  var selectedPermission = 1.obs; //默认公开
  bool showHot = false;
  var playDuration = 0.obs;
  var voiceDuration = 1.obs;
  var unfold = false.obs;
  var showUnfold = true.obs;

  //标签切换
  var tagSwitchType = [];
  List<TagsItemModel> tagsList = [];
  List<int> selectedTagsIndex = [];
  //是否选中nsfw、bdsm的tag
  String selectedSpecailTag = '';
  //选中的tag
  List<TagsItemModel> selectedTagsList = [];
  @override
  void onInit() {
    super.onInit();
    modelNo = Get.arguments;
    if (modelNo == null) {
      return;
    }
    tagsWidgetScrCtl = ScrollController();
    getData();
    initPlayer();
    ReportUtil.reportViews(page: ReportUtil.cloneMod, action: ReportUtil.view);
  }

  getData() async {
    CloneEditModel? model = await CloneApis.cloneDetail(modelNo: modelNo!);
    if (model != null) {
      editModel = model;
      bgImages = model.bgImage ?? [];
      selectedPermission.value = model.purview ?? 1;
      showHot = editModel.privatePower == false && UserService.to.isVip == false;
      addRound();
      setPlaySource();
      selectedSpecailTag = model.tag?['contentCategory'] ?? '';
      List tags = model.tag?['tags'] ?? [];
      for (var i = 0; i < tags.length; i++) {
        if (tags[i]['label'] == 'nsfw' && tagSwitchType.contains(1) == false) {
          tagSwitchType.add(1);
        }
        if (tags[i]['label'] == 'bdsm' && tagSwitchType.contains(2) == false) {
          tagSwitchType.add(2);
        }
        selectedTagsList.add(TagsItemModel.fromJson(tags[i]));
      }
      update();
      setTagsWidget();
    }
  }

  //选择图片
  selectImage() async {
    String? path = await SelectMediaUtil.selectImage(ratioX: 1.sw, ratioY: 1.sh - 80.w);
    if (path != null) {
      if (path.isEmpty) {
        Loading.toast('Please try again later');
        return;
      }
      uploadImageList.add(path);
      bgImages.add(CloneEditImageModel(img: path, gems: bgImages.isEmpty ? 0 : null));
      editModel.bgImage = bgImages;
      update();
      String? url = await uploadImage(path);
      if (url != null) {
        selectedImageList.add(url);
      }
    }
  }

  //上传图片
  Future<String?> uploadImage(String filePath) async {
    String? imageUrl = await UploadOss.upload(
        path: filePath, format: 'png', callBackFilePath: true, servicePath: ossServicePathClone);
    if (imageUrl == null) {
      Loading.toast('Avatar upload failed');
      return null;
    }
    List list = imageUrl.split('callBackFilePath');
    if (list.length != 2) {
      Loading.toast('Avatar upload failed');
      return null;
    }
    uploadImageList.remove(list.elementAt(0));
    return list.elementAt(1);
  }

  //改变图片价格
  changeImagePrice(String text, int index) {
    CloneEditImageModel model = bgImages.elementAt(index);
    model.gems = int.tryParse(text) ?? 0;
    bgImages.replaceRange(index, index + 1, [model]);
    editModel.bgImage = bgImages;
  }

//选择权限
  selecpermission(int index) {
    if (selectedPermission.value == index) {
      return;
    }
    if (index == 1) {
      selectedPermission.value = index;
      return;
    }
    if (editModel.privatePower == true && UserService.to.isVip == false) {
      selectedPrivateDialog(free: true);
      return;
    }
    if (editModel.privatePower == false) {
      selectedPrivateDialog(free: false);
      return;
    }
    selectedPermission.value = index;
  }

  //首次选择 Private 弹窗免费提示  之后选择Private 如果不是会员 就弹窗提示
  selectedPrivateDialog({required bool free}) async {
    bool? result = await Get.dialog(
      CustomDialogWidget(
        title: "System",
        subTitle:
            free ? 'Free Account gets one private Clone, sure to use it?' : 'Private ones used up.',
        confirmTitle: free ? 'Yes' : 'Go Hot',
        cancelTitle: free ? 'Public' : 'Set Public',
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      if (free) {
        selectedPermission.value = 0;
      } else {
        ReportUtil.reportViews(
            page: ReportUtil.cloneMod, action: ReportUtil.go, value: ReportUtil.hot);
        Analytics().logEvent(Analytics.clickHot,
            screen: Analytics.pageModifyclone, charID: editModel.modelId);
        Analytics().logEvent(
          Analytics.view,
          sourceEvent: Analytics.clickHot,
          sourceChar: editModel.modelId.toString(),
          sourceScreen: Analytics.pageModifyclone,
          screen: Analytics.pageHot,
        );
        await PurchaseSheet.show(buyType: UserService.to.isVip ? 1 : 0, page: ReportUtil.cloneMod);
      }
      return;
    }
  }

  //初始化播放器
  initPlayer() {
    voicePlayer = AudioPlayer();
    //监听播放
    voicePlayer.playerStateStream.listen((playState) {
      if (playState.processingState == ProcessingState.completed) {
        playing.value = false;
        playDuration.value = 0;
      }
    });
    voicePlayer.playingStream.listen((event) {
      playing.value = event;
    });
    voicePlayer.positionStream.listen((event) {
      if (Get.isRegistered<KloneEditPageController>() == false) {
        return;
      }
      if (voicePlayer.playerState.processingState == ProcessingState.completed) {
        playDuration.value = 0;
      } else {
        playDuration.value = event.inSeconds;
      }
    });
  }

  //设置播放数据
  setPlaySource() async {
    if (editModel.greetingUrl != null && editModel.greetingUrl!.isNotEmpty) {
      Duration? duration = await voicePlayer.setAudioSource(
        AudioSource.uri(Uri.parse(editModel.greetingUrl!)),
      );
      if (duration != null) {
        voiceDuration.value = duration.inSeconds;
      }
    }
  }

  //播放
  playVoice() async {
    if (editModel.greetingUrl == null || editModel.greetingUrl!.isEmpty) {
      return;
    }
    if (playing.value == true) {
      voicePlayer.stop();
      playDuration.value = 0;
      return;
    }
    playing.value = true;
    await voicePlayer.setAudioSource(AudioSource.uri(Uri.parse(editModel.greetingUrl!)));
    voicePlayer.play();
  }

  //分享
  share() async {
    Map? shareParam = await CommonApis.commonShare(modelId: editModel.modelId);
    if (shareParam != null) {
      Share.share(shareParam['text']);
    }
  }

  //删除
  delete() async {
    bool? result = await Get.dialog(
      const CustomDialogWidget(
        title: "System",
        subTitle: 'Deletion will be permanent',
        confirmTitle: 'Do it',
        cancelTitle: 'Cancel',
        confirmColor: '#00B0F9',
      ),
    );
    if (result == true) {
      bool delResult = await CloneApis.cloneDel(modelNo: editModel.modelNo!);
      if (delResult == true) {
        Get.back();
      }
    }
  }

  //添加对话示例
  addRound() {
    if (editModel.dialog == null || editModel.dialog!.isEmpty) {
      editModel.dialog = '${editModel.dialog ?? ''}{{char}}: \n{{user}}: \n\n\n';
    } else {
      for (int i = editModel.dialog!.length - 1; i >= 0; i--) {
        if (editModel.dialog!.endsWith('\n')) {
          editModel.dialog = editModel.dialog!.substring(0, i);
        } else {
          String str = '${editModel.dialog ?? ''}\n{{char}}: \n{{user}}: \n\n\n';
          if (str.length > 300) {
            return;
          }
          editModel.dialog = str;
          break;
        }
      }
    }
    update();
  }

  //选择tag
  selectTags() async {
    if (tagsList.isEmpty) {
      List<TagsItemModel> result = await CommonApis.tagsQuery(
        cacheCallBack: (cache) {
          if (cache.isNotEmpty) {
            tagsList = cache;
            showTagsDialog();
          }
        },
      );
      if (tagsList.isEmpty && result.isNotEmpty) {
        tagsList = result;
        showTagsDialog();
      }
    } else {
      showTagsDialog();
    }
  }

  showTagsDialog() async {
    if (selectedTagsIndex.isEmpty) {
      List list = selectedTagsList.map((e) => e.id).toList();
      for (var i = 0; i < tagsList.length; i++) {
        if (list.contains(tagsList[i].id)) {
          selectedTagsIndex.add(i);
        }
      }
    }
    unfold.value = false;
    await Get.dialog(
        TagsSelectWidget(
          tagsList: tagsList,
          selectedTagsIndex: selectedTagsIndex,
          tagSwitchType: tagSwitchType,
          multiple: true,
          backBtn: 'top',
        ),
        useSafeArea: false,
        transitionDuration: Duration.zero);
    selectedTagsList.clear();
    selectedSpecailTag = '';
    for (var element in selectedTagsIndex) {
      if (tagsList[element].label == 'nsfw') {
        selectedSpecailTag = 'nsfw';
      }
      //nsfw优先级高于bdsm
      if (tagsList[element].label == 'bdsm' && selectedSpecailTag != 'nsfw') {
        selectedSpecailTag = 'bdsm';
      }
      selectedTagsList.add(tagsList[element]);
    }
    update();
    setTagsWidget();
  }

  //判断tags展开、收起按钮是否展示
  setTagsWidget() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (tagsWidgetScrCtl.positions.isNotEmpty) {
        showUnfold.value =
            !(tagsWidgetScrCtl.position.maxScrollExtent < 20.w && unfold.value == false);
      }
    });
  }

  //保存
  save() async {
    if (uploadImageList.isNotEmpty) {
      Loading.toast('Please wait');
      return;
    }
    List<CloneEditImageModel> list = bgImages;
    if (selectedImageList.isNotEmpty) {
      for (var i = 0; i < list.length; i++) {
        CloneEditImageModel element = list.elementAt(i);
        if (element.id == null) {
          if (element.gems == null) {
            Loading.toast('The price cannot be empty');
            return;
          } else if (selectedImageList.isNotEmpty) {
            element.img = selectedImageList.first;
            element.id = '${i + 1}';
            list.replaceRange(i, i + 1, [element]);
            selectedImageList.removeAt(0);
          }
        }
      }
    }
    List<Map> imageParam = list.map<Map>((e) => e.toJson()).toList();
    bool result = await CloneApis.cloneEdit(
        purview: selectedPermission.value,
        greeting: editModel.greeting ?? '',
        modelNo: editModel.modelNo!,
        dialog: editModel.dialog ?? '',
        scenario: editModel.scenario ?? '',
        bgImage: imageParam,
        tags: selectedTagsList.map((e) => e.id).toList());
    if (result == true) {
      ReportUtil.reportEvents(page: ReportUtil.cloneMod, action: ReportUtil.finish);
      Get.back();
    }
  }

  @override
  void dispose() {
    super.dispose();
    tagsWidgetScrCtl.dispose();
    voicePlayer.dispose();
    ReportUtil.reportViews(page: ReportUtil.cloneMod, action: ReportUtil.quit);
  }
}
