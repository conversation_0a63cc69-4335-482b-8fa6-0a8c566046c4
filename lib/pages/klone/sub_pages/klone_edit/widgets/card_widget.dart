import 'dart:io';

import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CloneEditCardWidget extends StatelessWidget {
  final CloneEditModel model;
  const CloneEditCardWidget({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.w),
          child: Text(
            'Cards'.tr,
            style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
          ),
        ),
        10.verticalSpace<PERSON>romWidth,
        SizedBox(
          width: 1.sw,
          height: (model.bgImage ?? []).isNotEmpty ? 200.w + 43.w : 200.w,
          child: ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            scrollDirection: Axis.horizontal,
            itemCount: (model.bgImage ?? []).length + 1,
            itemBuilder: (context, index) => Column(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (index == (model.bgImage ?? []).length) {
                        KloneEditPageController.to.selectImage();
                      }
                    },
                    child: Container(
                      width: 112.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: AppColor.colorsUtil('#23212B'),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: index == (model.bgImage ?? []).length
                          ? Center(
                              child: Image.asset(
                                Assets.assetsImagesKloneAddImage,
                                width: 30.w,
                              ),
                            )
                          : model.bgImage!.elementAt(index).img != null
                              ? (model.bgImage!.elementAt(index).img!.contains('https://') ||
                                      model.bgImage!.elementAt(index).img!.contains('http://'))
                                  ? CachedNetworkImage(
                                      imageUrl: model.bgImage!.elementAt(index).img!,
                                      fit: BoxFit.cover,
                                    )
                                  : Image.file(
                                      File(model.bgImage!.elementAt(index).img!),
                                      fit: BoxFit.cover,
                                    )
                              : Container(),
                    ),
                  ),
                ),
                if ((model.bgImage ?? []).isNotEmpty) 10.verticalSpaceFromWidth,
                if ((model.bgImage ?? []).isNotEmpty)
                  index == (model.bgImage ?? []).length
                      ? 33.verticalSpaceFromWidth
                      : Row(
                          children: [
                            Image.asset(Assets.assetsImagesSessionGems, width: 20.w),
                            5.horizontalSpace,
                            Container(
                              width: 77.w,
                              height: 33.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.r),
                                color: Colors.white.withValues(alpha: 0.1),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 10.w),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(bottom: 0),
                                      child: CustomTextField.textField(
                                        text: model.bgImage!.elementAt(index).gems == null
                                            ? null
                                            : '${model.bgImage!.elementAt(index).gems}',
                                        textInputType: TextInputType.number,
                                        fontSize: 14,
                                        enable: index != 0,
                                        textColor: index == 0
                                            ? Colors.white.withValues(alpha: 0.3)
                                            : Colors.white,
                                        onChanged: (value) {
                                          KloneEditPageController.to.changeImagePrice(value, index);
                                        },
                                      ),
                                    ),
                                  ),
                                  if (index == 0)
                                    Text(
                                      'Price'.tr,
                                      style: TextStyle(
                                        fontSize: 10.sp,
                                        color: Colors.white.withValues(alpha: 0.3),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
              ],
            ),
            separatorBuilder: (context, index) => 10.horizontalSpace,
          ),
        ),
        if ((model.bgImage ?? []).length > 1)
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 10.w),
            child: Text(
              'clone card tip'.tr,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white.withValues(alpha: 0.3),
              ),
            ),
          ),
        20.verticalSpaceFromWidth,
      ],
    );
  }
}
