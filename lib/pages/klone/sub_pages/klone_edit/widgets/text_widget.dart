import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/special_text_span/special_text_span_builder.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:get/get.dart';

class CloneEditTextWidget extends StatelessWidget {
  final CloneEditModel model;
  final bool must;
  final bool enableEdit;
  final String title;

  const CloneEditTextWidget(
      {super.key,
      required this.model,
      required this.must,
      required this.enableEdit,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
              ),
              if (must)
                Text(
                  '*',
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColor.colorsUtil('#FF4300'),
                      fontWeight: FontWeight.w700),
                ),
            ],
          ),
          10.verticalSpaceFromWidth,
          Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white.withValues(alpha: 0.1),
              ),
              child: Stack(
                children: [
                  Positioned(
                    child: Padding(
                      padding: EdgeInsets.all(16.w),
                      child: title == 'Example dialogue'.tr
                          ? dialogueInPutWidget(model.dialog ?? '')
                          : CustomTextField.textField(
                              text: title == 'Greetings'.tr
                                  ? model.greeting ?? ''
                                  : title == 'Typical Convo Scenario'.tr
                                      ? model.scenario ?? ''
                                      : title == 'Example dialogue'.tr
                                          ? model.dialog ?? ''
                                          : '',
                              textColor: Colors.white.withValues(alpha: 0.8),
                              showCount: true,
                              maxLength: 300,
                              enable: enableEdit,
                              onChanged: (value) {
                                if (title == 'Greetings'.tr) {
                                  KloneEditPageController.to.editModel.greeting = value;
                                }
                                if (title == 'Typical Convo Scenario'.tr) {
                                  KloneEditPageController.to.editModel.scenario = value;
                                }
                                if (title == 'Example dialogue'.tr) {
                                  KloneEditPageController.to.editModel.dialog = value;
                                }
                              },
                            ),
                    ),
                  ),
                  if (title == 'Example dialogue'.tr)
                    Positioned(
                      bottom: 14.w,
                      left: 16.w,
                      child: ImageBtn(
                          iconSting: Assets.assetsImagesCloneEditAddRound,
                          onPressed: () {
                            KloneEditPageController.to.addRound();
                          },
                          width: 132.w,
                          height: 22.w),
                    ),
                ],
              )),
          20.verticalSpaceFromWidth,
        ],
      ),
    );
  }

  Widget dialogueInPutWidget(String text) {
    return ExtendedTextField(
      specialTextSpanBuilder: AppSpecialTextSpan(preview: true),
      controller: TextEditingController(text: text),
      textInputAction: TextInputAction.newline,
      enabled: true,
      maxLength: 300,
      style: TextStyle(fontSize: 15.sp, color: Colors.white.withValues(alpha: 0.8)),
      maxLines: null,
      onChanged: (value) {
        KloneEditPageController.to.editModel.dialog = value;
      },
      decoration: InputDecoration(
        isCollapsed: true,
        counterStyle: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.2)),
        // counterText: "",
        // contentPadding: REdgeInsets.all(12),
        //去除下划线
        border: const OutlineInputBorder(borderSide: BorderSide.none),
      ),
    );
  }
}
