import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

class CloneEditTagsWidget extends StatelessWidget {
  final List<TagsItemModel> selectedTagsList;
  const CloneEditTagsWidget({super.key, required this.selectedTagsList});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            15.horizontalSpace,
            Text(
              'Tags'.tr,
              style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
            ),
            const Spacer(),
            InkWell(
              onTap: () => KloneEditPageController.to.selectTags(),
              child: Padding(
                padding: EdgeInsets.only(right: 15.w, left: 30.w),
                child: Image.asset(
                  Assets.assetsImagesEditMessage,
                  width: 20.w,
                  height: 20.w,
                ),
              ),
            )
          ],
        ),
        selectedTagsList.isNotEmpty
            ? Obx(
                () => SizedBox(
                  width: double.infinity,
                  height: KloneEditPageController.to.unfold.value == true ? null : 40.w,
                  child: InkWell(
                    onTap: () {
                      if (KloneEditPageController.to.showUnfold.value == true) {
                        KloneEditPageController.to.unfold.toggle();
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(top: 10.w, bottom: 13.w, left: 15.w, right: 15.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              controller: KloneEditPageController.to.tagsWidgetScrCtl,
                              physics: const NeverScrollableScrollPhysics(),
                              child: tags(),
                            ),
                          ),
                          if (KloneEditPageController.to.showUnfold.value == true)
                            Padding(
                              padding: EdgeInsets.only(
                                  bottom:
                                      KloneEditPageController.to.unfold.value == true ? 4.w : 0),
                              child: Image.asset(
                                  KloneEditPageController.to.unfold.value
                                      ? Assets.assetsImagesCloneTagsArrowUp
                                      : Assets.assetsImagesCloneTagsArrowDown,
                                  width: 16.w,
                                  height: 16.w),
                            )
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : 20.verticalSpaceFromWidth,
      ],
    );
  }

  Widget tags() {
    List<TagsItemModel> tags = List.from(selectedTagsList);
    if (KloneEditPageController.to.selectedSpecailTag.isNotEmpty) {
      tags.insert(
          0,
          TagsItemModel.fromJson(
              {'name': KloneEditPageController.to.selectedSpecailTag.toUpperCase()}));
    }
    return Wrap(
      spacing: 7.w,
      runSpacing: 4.w,
      children: List.generate(tags.length, (index) {
        return Container(
          height: 14.w,
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          margin: EdgeInsets.only(bottom: 4.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(4.r),
              bottomLeft: Radius.circular(4.r),
              bottomRight: Radius.circular(4.r),
            ),
            border: (tags[index].name == 'NSFW' || tags[index].name == 'BDSM')
                ? Border.all(color: Colors.white.withValues(alpha: 0.7), width: 1)
                : GradientBoxBorder(
                    gradient: LinearGradient(colors: [
                      Colors.white.withValues(alpha: 0.8),
                      Colors.transparent,
                    ]),
                  ),
            color: AppColor.colorsUtil('#00B0F9'),
            gradient: tags[index].name == 'BDSM'
                ? null
                : LinearGradient(
                    colors: tags[index].name == 'NSFW'
                        ? [
                            AppColor.colorsUtil('#FF68B9'),
                            AppColor.colorsUtil('#FF3D62'),
                          ]
                        : [
                            AppColor.colorsUtil('#3E2D12').withValues(alpha: 0.8),
                            AppColor.colorsUtil('#1A1000').withValues(alpha: 0.6),
                          ]),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                tags[index].name ?? '',
                style: TextStyle(
                    fontSize: 8.sp,
                    color: (tags[index].name == 'NSFW' || tags[index].name == 'BDSM')
                        ? AppColor.primaryText
                        : AppColor.colorsUtil('#FFDEAE'),
                    height: 9 / 8),
              )
            ],
          ),
        );
      }),
    );
  }
}
