import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:get/get.dart';

class CloneEditPrivateWidget extends StatelessWidget {
  final CloneEditModel model;
  const CloneEditPrivateWidget({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Private'.tr,
            style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w700),
          ),
          10.verticalSpaceFromWidth,
          Container(
            width: double.infinity,
            height: 50.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 16.w, right: 100.w),
              child: Row(
                children: [
                  Obx(
                    () => permissionButton(
                        onTap: () => KloneEditPageController.to.selecpermission(0),
                        selected: KloneEditPageController.to.selectedPermission.value == 0,
                        title: 'Private'.tr),
                  ),
                  2.horizontalSpace,
                  if (KloneEditPageController.to.showHot)
                    Padding(
                      padding: EdgeInsets.only(bottom: 3.w),
                      child: Image.asset(Assets.assetsImagesAmorsHot, width: 50.w),
                    ),
                  const Spacer(),
                  Obx(
                    () => permissionButton(
                        onTap: () => KloneEditPageController.to.selecpermission(1),
                        selected: KloneEditPageController.to.selectedPermission.value == 1,
                        title: 'Public'.tr),
                  ),
                ],
              ),
            ),
          ),
          20.verticalSpaceFromWidth,
        ],
      ),
    );
  }

  //权限按钮
  Widget permissionButton(
      {required VoidCallback onTap, required bool selected, required String title}) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Image.asset(
            selected
                ? Assets.assetsImagesClonePermissionSelected
                : Assets.assetsImagesClonePermissionSelect,
            width: 18.w,
          ),
          6.horizontalSpace,
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
              shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 2)],
            ),
          ),
        ],
      ),
    );
  }
}
