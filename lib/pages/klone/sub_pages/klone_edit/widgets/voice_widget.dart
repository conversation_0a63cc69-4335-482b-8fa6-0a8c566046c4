import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:get/get.dart';

class CloneEditVoiceWidget extends StatelessWidget {
  final CloneEditModel model;

  const CloneEditVoiceWidget({
    super.key,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Voice'.tr,
                style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white.withValues(alpha: 0.5),
                    fontWeight: FontWeight.w700),
              ),
              5.horizontalSpace,
              Image.asset(
                Assets.assetsImagesCloneEditLock,
                width: 16.w,
              ),
            ],
          ),
          10.verticalSpace<PERSON>romWidth,
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: AppColor.colorsUtil('#221A1C'),
            ),
            child: Row(
              children: [
                InkWell(
                  onTap: () => KloneEditPageController.to.playVoice(),
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(16.w, 16.w, 10.w, 16.w),
                    child: Obx(
                      () => Image.asset(
                        KloneEditPageController.to.playing.value == true
                            ? Assets.assetsImagesCloneRecordVoicePause
                            : Assets.assetsImagesCloneRecordVoicePlay,
                        width: 18.w,
                        height: 18.w,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 4.w,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.w / 2),
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                    alignment: Alignment.centerLeft,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        double parentWidth = constraints.maxWidth; // 获取父组件的宽度
                        return Obx(
                          () => Container(
                            width: parentWidth *
                                (KloneEditPageController.to.playDuration.value /
                                    KloneEditPageController.to.voiceDuration.value),
                            height: 4.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.w / 2),
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10.w, right: 16.w),
                  child: Obx(
                    () => Text(
                      CommonUtil.durationTransformPro(
                          KloneEditPageController.to.voiceDuration.value -
                              KloneEditPageController.to.playDuration.value),
                      style: TextStyle(fontSize: 12.sp, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
          20.verticalSpaceFromWidth,
        ],
      ),
    );
  }
}
