import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../controller.dart';

class CloneEditHeaderWidget extends StatelessWidget {
  final CloneEditModel model;
  const CloneEditHeaderWidget({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          20.verticalSpaceFromWidth,
          ClipRRect(
            borderRadius: BorderRadius.circular(150.w / 2),
            child: CachedNetworkImage(
              imageUrl: model.cover ?? '',
              placeholder: (context, url) => Container(),
              errorWidget: (context, url, error) => Container(),
              height: 150.w,
              width: 150.w,
            ),
          ),
          5.verticalSpaceFromWidth,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                model.modelName ?? '',
                style: TextStyle(
                    fontSize: 20.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w800,
                    fontStyle: FontStyle.italic),
              ),
              5.horizontalSpace,
              Image.asset(
                model.gender == 0
                    ? Assets.assetsImagesCloneEditSexFemale
                    : model.gender == 1
                        ? Assets.assetsImagesCloneEditSexMale
                        : Assets.assetsImagesCloneEditSexUn,
                width: 20.w,
              ),
            ],
          ),
          if (model.inviteCode != null) 4.verticalSpaceFromWidth,
          if (model.inviteCode != null)
            InkWell(
              onTap: () {
                HapticFeedback.heavyImpact();
                Clipboard.setData(ClipboardData(text: model.inviteCode ?? ''));
                Loading.toast("Code copied! Let's go share!");
                KloneEditPageController.to.share();
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Code:'.trArgs([model.inviteCode ?? '']),
                    style: TextStyle(fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.5)),
                  ),
                  5.horizontalSpace,
                  Image.asset(
                    Assets.assetsImagesCloneCodeCopy,
                    width: 16.w,
                  ),
                ],
              ),
            ),
          20.verticalSpaceFromWidth,
        ],
      ),
    );
  }
}
