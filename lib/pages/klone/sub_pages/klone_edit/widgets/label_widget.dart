import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:get/get.dart';

class CloneEditLabelWidget extends StatelessWidget {
  final CloneEditModel model;
  final String type;
  final String title;
  const CloneEditLabelWidget(
      {super.key, required this.model, required this.type, required this.title});

  @override
  Widget build(BuildContext context) {
    List<String> labelList = [];
    if (type != 'text') {
      if (title == 'Appearances'.tr) {
        labelList = model.appearances!.split(',');
      }
      if (title == 'Personalities'.tr) {
        labelList = model.personaliities!.split(',');
      }
      if (title == 'Preferences'.tr) {
        labelList = model.preferences!.split(',');
      }
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white.withValues(alpha: 0.5),
                    fontWeight: FontWeight.w700),
              ),
              5.horizontalSpace,
              Image.asset(
                Assets.assetsImagesCloneEditLock,
                width: 16.w,
              ),
            ],
          ),
          10.verticalSpaceFromWidth,
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: AppColor.colorsUtil('#221A1C'),
            ),
            padding: EdgeInsets.all(16.w),
            child: type == 'text'
                ? Text(
                    model.intro ?? '',
                    style: TextStyle(fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.5)),
                  )
                : Wrap(
                    spacing: 5.w,
                    runSpacing: 10.w,
                    children: labelList
                        .map(
                          (e) => InputChip(
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            padding: EdgeInsets.zero,
                            labelPadding: EdgeInsets.zero,
                            backgroundColor: Colors.white.withValues(alpha: 0.1),
                            showCheckmark: false,
                            onPressed: () {},
                            //设置圆角
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                            label: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10.w),
                              child: Text(
                                (e).length > 20 ? '${e.substring(0, 20)}...' : e,
                                style: TextStyle(
                                    fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.5)),
                              ),
                            ),
                            side: const BorderSide(width: 0, color: Colors.transparent),
                          ),
                        )
                        .toList(),
                  ),
          ),
          20.verticalSpaceFromWidth,
        ],
      ),
    );
  }
}
