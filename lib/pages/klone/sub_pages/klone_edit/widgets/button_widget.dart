import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CloneEditButtonWidget extends StatelessWidget {
  const CloneEditButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 20.w, bottom: 40.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          buttonWidget(
              onTap: () {
                KloneEditPageController.to.delete();
              },
              title: 'Delete'.tr),
          20.horizontalSpace,
          buttonWidget(
              onTap: () {
                KloneEditPageController.to.save();
              },
              title: 'Save'.tr),
        ],
      ),
    );
  }

  Widget buttonWidget({required VoidCallback onTap, required String title}) {
    return GradientColorBtn(
      height: 45.w,
      width: 140.w,
      text: title,
      onTap: onTap,
      colors: title == 'Delete'.tr
          ? [AppColor.colorsUtil('#3D3D43'), AppColor.colorsUtil('#3D3D43')]
          : null,
    );
  }
}
