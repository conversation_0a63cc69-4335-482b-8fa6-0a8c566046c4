import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/widgets/label_widget.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/widgets/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';
import 'widgets/button_widget.dart';
import 'widgets/card_widget.dart';
import 'widgets/header_widget.dart';
import 'widgets/private_widget.dart';
import 'widgets/tags_widget.dart';
import 'widgets/voice_widget.dart';

class KloneEditPage extends GetView<KloneEditPageController> {
  const KloneEditPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('CloneMod'.tr),
        leading: const CustomBackButton(),
        actions: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: ImageBtn(
                iconSting: Assets.assetsImagesCloneEditShare,
                onPressed: () => controller.share(),
                width: 20.w,
                height: 20.w),
          )
        ],
      ),
      body: GetBuilder<KloneEditPageController>(
        builder: (controller) => SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CloneEditHeaderWidget(model: controller.editModel),
              if (AppService.audit == false)
                CloneEditTagsWidget(selectedTagsList: controller.selectedTagsList),
              CloneEditCardWidget(model: controller.editModel),
              CloneEditPrivateWidget(model: controller.editModel),
              CloneEditTextWidget(
                  model: controller.editModel, must: true, enableEdit: true, title: 'Greetings'.tr),
              CloneEditTextWidget(
                  model: controller.editModel,
                  must: false,
                  enableEdit: true,
                  title: 'Typical Convo Scenario'.tr),
              CloneEditTextWidget(
                  model: controller.editModel,
                  must: false,
                  enableEdit: true,
                  title: 'Example dialogue'.tr),
              if (controller.editModel.greetingUrl != null &&
                  controller.editModel.greetingUrl!.isNotEmpty)
                CloneEditVoiceWidget(model: controller.editModel),
              if (controller.editModel.appearances != null &&
                  controller.editModel.appearances!.isNotEmpty)
                CloneEditLabelWidget(
                    model: controller.editModel, type: 'label', title: 'Appearances'.tr),
              if (controller.editModel.personaliities != null &&
                  controller.editModel.personaliities!.isNotEmpty)
                CloneEditLabelWidget(
                    model: controller.editModel, type: 'label', title: 'Personalities'.tr),
              if (controller.editModel.preferences != null &&
                  controller.editModel.preferences!.isNotEmpty)
                CloneEditLabelWidget(
                    model: controller.editModel, type: 'label', title: 'Preferences'.tr),
              if (controller.editModel.intro != null && controller.editModel.intro!.isNotEmpty)
                CloneEditLabelWidget(model: controller.editModel, type: 'text', title: 'Intro'.tr),
              const CloneEditButtonWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
