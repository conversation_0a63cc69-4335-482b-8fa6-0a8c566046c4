import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class KloneListItem extends StatelessWidget {
  final CloneListModel model;
  const KloneListItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    bool showExpire = (model.expireDay != null && model.expireDay! >= 0 && model.expireDay! < 4);
    return InkWell(
      onTap: () => KlonePageController.to.itemOnTap(model),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: AppColor.colorsUtil('#1F1A21'),
          gradient: model.purview == 0
              ? LinearGradient(
                  begin: const Alignment(-0.5, -1),
                  end: const Alignment(1, 1),
                  colors: [
                    AppColor.colorsUtil('#202021'),
                    AppColor.colorsUtil('#444444'),
                    AppColor.colorsUtil('#585859'),
                    AppColor.colorsUtil('#39393A'),
                    AppColor.colorsUtil('#202021'),
                  ],
                )
              : null,
        ),
        padding: EdgeInsets.fromLTRB(16.w, 0, 0, 16.w),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                16.verticalSpaceFromWidth,
                Row(
                  children: [
                    Container(
                      width: 100.w,
                      height: 100.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: model.cover == null
                                ? Image.asset(
                                    model.gender == 0
                                        ? Assets.assetsImagesKloneListSexFemale
                                        : model.gender == 1
                                            ? Assets.assetsImagesKloneListSexMale
                                            : Assets.assetsImagesKloneListSexNA,
                                    fit: BoxFit.cover,
                                  )
                                : CachedNetworkImage(
                                    imageUrl: model.cover ?? '',
                                    width: 100.w,
                                  ),
                          ),
                          if (showExpire)
                            Positioned(
                              bottom: 0,
                              child: InkWell(
                                onTap: () {
                                  KlonePageController.to.cloneRefreshDialog(model);
                                },
                                child: Container(
                                  width: 100.w,
                                  height: 22.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        AppColor.colorsUtil('#FFDCA4'),
                                        AppColor.colorsUtil('#C8984A'),
                                      ],
                                    ),
                                  ),
                                  child: Text(
                                    'Refresh'.tr,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w700,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () {
                              KlonePageController.to.editModel(model);
                            },
                            child: Row(
                              children: [
                                Text(
                                  model.modelName ?? '',
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600),
                                ),
                                // 6.horizontalSpace,
                                Image.asset(
                                  model.modelProcess!.toUpperCase() != 'COMPLETE'
                                      ? Assets.assetsImagesCloneDraftDel
                                      : Assets.assetsImagesCloneListItemEdit,
                                  width: 28.w,
                                ),
                              ],
                            ),
                          ),
                          8.verticalSpaceFromWidth,
                          Row(
                            children: [
                              Image.asset(
                                Assets.assetsImagesCloneListItemLike,
                                width: 16.w,
                              ),
                              5.horizontalSpace,
                              Text(
                                'Liked num'.trArgs([(CommonUtil.numberUnits(model.likeCount ?? 0))]),
                                style: TextStyle(
                                    fontSize: 14.sp, color: Colors.white.withValues(alpha: 0.6)),
                              ),
                            ],
                          ),
                          10.verticalSpaceFromWidth,
                          Row(
                            children: [
                              Image.asset(
                                Assets.assetsImagesSessionGems,
                                width: 16.w,
                              ),
                              5.horizontalSpace,
                              Text(
                                'Earned'.trArgs(['${model.earned ?? 0}']),
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.6)),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    /*
                    if (model.greetingUrl != null && model.greetingUrl!.isNotEmpty)
                      InkWell(
                        onTap: () {
                          KlonePageController.to.playVoice(model);
                        },
                        child: Container(
                          width: 100.w,
                          height: 36.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(36.w / 2),
                              bottomLeft: Radius.circular(36.w / 2),
                            ),
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                          child: Row(
                            children: [
                              11.horizontalSpace,
                              Obx(
                                () => Image.asset(
                                  KlonePageController.to.playingVoice.value == model.modelNo
                                      ? Assets.assetsImagesCloneVoicePause
                                      : Assets.assetsImagesCloneListItemPlay,
                                  width: 22.w,
                                ),
                              ),
                              5.horizontalSpace,
                              Image.asset(
                                Assets.assetsImagesCloneListItemVoice,
                                width: 20.w,
                              ),
                              7.horizontalSpace,
                              Text(
                                '${(model.greeting ?? '').length ~/ 9}”',
                                style: TextStyle(fontSize: 14.sp, color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    */
                  ],
                ),
                if (model.intro != null)
                  Padding(
                    padding: EdgeInsets.only(right: 16.w, top: 10.w),
                    child: Text(
                      model.intro ?? '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
              ],
            ),
            if (showExpire || model.purview == 0 || model.modelProcess!.toUpperCase() != 'COMPLETE')
              Align(
                alignment: Alignment.topRight,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r),
                    ),
                    color: showExpire
                        ? AppColor.colorsUtil('#FF2323').withValues(alpha: 0.25)
                        : model.purview == 0
                            ? AppColor.colorsUtil('#F0BE72')
                            : AppColor.colorsUtil('#00B0F9'),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.w),
                  child: Text(
                    showExpire
                        ? '${model.expireDay} days to disappear'
                        : model.purview == 0
                            ? 'Private'.tr
                            : 'Draft'.tr,
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontStyle: FontStyle.italic),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
