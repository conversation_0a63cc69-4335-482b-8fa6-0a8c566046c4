import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class KloneBlankPage extends GetView<KlonePageController> {
  const KloneBlankPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Get.arguments == 'creat'
          ? AppBar(
              backgroundColor: Colors.transparent,
              leading: const CustomBackButton(),
            )
          : null,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            child: Image.asset(Assets.assetsImagesKloneTopBg),
          ),
          Positioned(
            top: 50.w + CommonUtil.statusBarHeight(context),
            left: 0,
            right: 0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: CommonUtil.gradientText(
                    text: "Ready to Clone".tr,
                    colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#EAC282')],
                    textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w700),
                  ),
                ),
                2.verticalSpace,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: CommonUtil.gradientText(
                    text: "an AI Doppelgänger".tr,
                    colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
                    textStyle: TextStyle(fontSize: 26.sp, fontWeight: FontWeight.w700),
                  ),
                ),
                85.verticalSpace,
                Container(
                  height: 235.w,
                  alignment: Alignment.center,
                  child: Lottie.asset('assets/lottie/klone_scan/klone_scan.json', animate: true),
                ),
                60.verticalSpace,
                Center(
                  child: Text(
                    'Now, is the original around you?'.tr,
                    style: TextStyle(fontSize: 16.sp, color: Colors.white.withValues(alpha: 0.8)),
                  ),
                ),
                40.verticalSpaceFromWidth,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    button(
                      onTap: () {
                        if (UserService.to.isLogin == false) {
                          UserService.to.loginTip();
                          return;
                        }
                        //震动
                        if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true) {
                          HapticFeedback.mediumImpact();
                        }
                        Get.toNamed(Routes.cloneStepOne, parameters: {'showRecordVoice': 'true'});
                      },
                      title: 'Yes'.tr,
                      colors: [AppColor.colorsUtil('#F0BE72')],
                      width: 130.w,
                    ),
                    30.horizontalSpace,
                    button(
                      onTap: () {
                        if (UserService.to.isLogin == false) {
                          UserService.to.loginTip();
                          return;
                        }
                        //震动
                        if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true) {
                          HapticFeedback.mediumImpact();
                        }
                        Get.toNamed(Routes.cloneStepOne, parameters: {'showRecordVoice': 'false'});
                      },
                      title: 'No'.tr,
                      colors: [AppColor.colorsUtil('#F0BE72')],
                      width: 130.w,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget button({
    required VoidCallback onTap,
    required String title,
    required List<Color> colors,
    required double width,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 45.w,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(45.w / 2),
          color: colors.first,
          gradient: colors.length > 1 ? LinearGradient(colors: colors) : null,
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
            fontWeight: colors.length > 1 ? FontWeight.w700 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
