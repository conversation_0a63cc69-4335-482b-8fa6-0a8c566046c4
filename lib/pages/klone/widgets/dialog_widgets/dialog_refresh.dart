import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

//克隆列表  刷新弹窗
class CloneDialogRefresh extends StatelessWidget {
  final int gems;
  const CloneDialogRefresh({super.key, required this.gems});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.w,
        height: UserService.to.isVip ? 311.w : 292.w,
        decoration: const BoxDecoration(
            image: DecorationImage(image: AssetImage(Assets.assetsImagesSessionBottomDialogBg))),
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        margin: EdgeInsets.only(bottom: 100.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            30.verticalSpaceFromWidth,
            SizedBox(
              width: 76.w,
              height: 76.w,
              child: Stack(
                children: [
                  Positioned.fill(
                    child: Image.asset(
                      Assets.assetsImagesDialogRefreshIcon,
                      fit: BoxFit.contain,
                      width: 76.w,
                      height: 76.w,
                    ),
                  ),
                ],
              ),
            ),
            20.verticalSpace,
            Text(
              'Refresh',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 20.sp, color: AppColor.primaryText, fontWeight: FontWeight.w700),
            ),
            10.verticalSpace,
            Text(
              'HOT members get unlimited refreshes.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.primaryText.withValues(alpha: 0.8),
              ),
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                /*
                if (UserService.to.isVip == false)
                  Expanded(
                    child: InkWell(
                      onTap: () => Get.back(result: 0),
                      child: Container(
                        height: 42.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(42.w / 2),
                          color: CommonUtil.colorsUtil('#575757'),
                        ),
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              Assets.assetsImagesUnlockModelAd,
                              width: 24.w,
                            ),
                            6.horizontalSpace,
                            Text(
                              'Watch',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w400,
                                color: CommonUtil.colorsUtil('#CDCDCD'),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                if (UserService.to.isVip == false) 16.horizontalSpace,
                */
                Expanded(
                  child: customButton(
                      onTap: () {
                        //支付
                        Get.back(result: 1);
                      },
                      title: 'Pay',
                      colors: ['#FFDCA4', '#C8984A'],
                      fontWeight: FontWeight.w600,
                      gems: gems),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget customButton(
      {required VoidCallback onTap,
      required String title,
      required List<String> colors,
      FontWeight? fontWeight,
      int? gems}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        height: 42.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(42.w / 2),
          color: AppColor.colorsUtil(colors.first),
          gradient: colors.length > 1
              ? LinearGradient(colors: colors.map((e) => AppColor.colorsUtil(e)).toList())
              : null,
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 16.sp, fontWeight: fontWeight),
            ),
            if (gems != null)
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 4),
                child: Image.asset(
                  Assets.assetsImagesSessionGems,
                  width: 16.w,
                ),
              ),
            if (gems != null)
              Text(
                NumberFormat('#,###,###').format(gems),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColor.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
