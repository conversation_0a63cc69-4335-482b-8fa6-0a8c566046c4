import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'klone_list_item.dart';

class KloneListPage extends GetView<KlonePageController> {
  const KloneListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: null,
        title: CommonUtil.gradientText(
          text: 'My Clones'.tr,
          colors: [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
          textStyle: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w700),
        ),
        centerTitle: false,
        actions: [
          InkWell(
            onTap: () {
              controller.creatNewClone();
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Image.asset(
                Assets.assetsImagesAddRound,
                width: 25.w,
              ),
            ),
          )
        ],
        backgroundColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Image.asset(Assets.assetsImagesKloneTopBg),
          ),
          Positioned(
            top: CommonUtil.statusBarHeight(context) + CommonUtil.appBarHeight(),
            left: 0,
            right: 0,
            bottom: 0,
            child: EasyRefresh.builder(
              controller: controller.refreshController,
              onRefresh: () => controller.refreshRequest(),
              onLoad: () => controller.loadMoreRequest(),
              childBuilder: (context, physics) => Obx(
                () => ListView.separated(
                  padding: REdgeInsets.fromLTRB(10, 10, 10, CommonUtil.bottomBarHeight() + 60),
                  physics: physics,
                  itemBuilder: (context, index) {
                    return KloneListItem(
                      model: controller.modelList.elementAt(index),
                    );
                  },
                  separatorBuilder: (context, index) => 16.verticalSpaceFromWidth,
                  itemCount: controller.modelList.length,
                ),
              ),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: CommonUtil.bottomBarHeight() + 60.w,
            child: Text(
              'clone list tip'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 11.sp,
                color: Colors.white.withValues(alpha: 0.2),
                shadows: [Shadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 1)],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
