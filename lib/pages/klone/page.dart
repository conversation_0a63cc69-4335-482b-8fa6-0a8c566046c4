import 'package:amor_app/pages/klone/widgets/klone_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';
import 'widgets/klone_blank.dart';

class KlonePage extends GetView<KlonePageController> {
  const KlonePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<KlonePageController>(
      init: KlonePageController(),
      builder: (controller) => Obx(() => controller.loadDone.value == true
          ? (controller.modelList.isEmpty ? const KloneBlankPage() : const KloneListPage())
          : Container()),
    );
  }
}
