import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'controller.dart';

class AnimtedSpalsh extends GetView<AnimatedSplashPageController> {
  const AnimtedSpalsh({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        backgroundColor: AppColor.colorsUtil('#0B0813'),
        body: Obx(
          () => AnimatedDefaultTextStyle(
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: controller.animated.value == 0 ? 108.sp : 68.sp,
                fontWeight: FontWeight.w700),
            duration: controller.animatedDuration,
            onEnd: () {
              controller.animatedEnd = true;
              if (controller.firstOpen == false) {
                Future.delayed(const Duration(milliseconds: 2000), () => controller.toNextPage());
              }
            },
            child: Stack(
              children: [
                AnimatedPositioned(
                  top: (controller.animated.value == 0 ? 136.h : 256.h) +
                      CommonUtil.statusBarHeight(context),
                  left: 0,
                  right: controller.animated.value == 0 ? 0 : 100.w,
                  duration: controller.animatedDuration,
                  child: const Text('Amor'),
                ),
                AnimatedPositioned(
                  top: (controller.animated.value == 0 ? 318.h : 256.h) +
                      CommonUtil.statusBarHeight(context),
                  left: controller.animated.value == 0 ? 0 : 200.w,
                  right: 0,
                  duration: controller.animatedDuration,
                  child: const Text('AI'),
                ),
                AnimatedPositioned(
                  top: (controller.animated.value == 0 ? 590.h : 360.h) +
                      CommonUtil.statusBarHeight(context),
                  left: 0,
                  right: 0,
                  duration: controller.animatedDuration,
                  child: AnimatedDefaultTextStyle(
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: controller.animated.value == 0 ? 12.sp : 20.sp,
                        fontWeight: FontWeight.w500,
                        fontStyle: FontStyle.italic,
                      ),
                      duration: controller.animatedDuration,
                      child: ShaderMask(
                        shaderCallback: (rect) {
                          return LinearGradient(
                            colors: controller.animated.value == 0
                                ? [Colors.white, Colors.white]
                                : [
                                    AppColor.colorsUtil('#C69351'),
                                    AppColor.colorsUtil('#EAC282'),
                                  ],
                          ).createShader(rect);
                        },
                        child: Text('the fun that gets you'.tr),
                      )
                      //  controller.animated.value == 0
                      //     ? const Text('the fun that gets you')
                      //     : CommonUtil.gradientText(text: text, colors: colors, textStyle: textStyle),
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
