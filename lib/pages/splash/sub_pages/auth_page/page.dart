import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class AuthLoginPage extends GetView<AuthLoginPageController> {
  const AuthLoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SafeArea(
          child: Column(
            children: [
              120.verticalSpaceFromWidth,
              EasyRichText(
                'auth page title'.tr,
                textAlign: TextAlign.center,
                defaultStyle:
                    TextStyle(fontSize: 15.sp, color: Colors.white, fontStyle: FontStyle.italic),
                patternList: [
                  EasyRichTextPattern(
                    targetString: 'MUST'.tr,
                    style:
                        const TextStyle(fontWeight: FontWeight.w500, fontStyle: FontStyle.normal),
                  ),
                ],
              ),
              60.verticalSpaceFromWidth,
              Text(
                '18+ Years old'.tr,
                style: TextStyle(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              SizedBox(
                width: 234.w,
                child: GradientColorBtn(
                  height: 45.w,
                  colors: [
                    AppColor.colorsUtil('#FFDCA4'),
                    AppColor.colorsUtil('#C8984A'),
                  ],
                  text: 'YES and Login'.tr,
                  onTap: () {
                    controller.login();
                  },
                ),
              ),
              15.verticalSpaceFromWidth,
              EasyRichText(
                'auth page agree'.tr,
                textAlign: TextAlign.center,
                defaultStyle:
                    TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                patternList: [
                  EasyRichTextPattern(
                    targetString: 'Agree to'.tr,
                    prefixInlineSpan: WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: InkWell(
                        onTap: () {
                          controller.selected.toggle();
                        },
                        child: Container(
                          width: 30.w,
                          height: 50.w,
                          margin: EdgeInsets.only(right: 3.w),
                          alignment: Alignment.centerRight,
                          child: Obx(
                            () => Image.asset(
                              controller.selected.value
                                  ? Assets.assetsImagesGemsShortSelected
                                  : Assets.assetsImagesAuthSelect,
                              width: 14.w,
                              height: 14.w,
                            ),
                          ),
                        ),
                      ),
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        controller.selected.toggle();
                      },
                    style: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.5)),
                  ),
                  EasyRichTextPattern(
                    targetString: 'Terms & Conditions'.tr,
                    style: TextStyle(
                        fontWeight: FontWeight.w500, color: AppColor.colorsUtil('#F0BE72')),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(Routes.webview, parameters: {
                          'url': AppService.configModel.userAgreement ?? (defaultUserAgreement)
                        });
                      },
                  ),
                  EasyRichTextPattern(
                    targetString: 'Privacy Policy'.tr,
                    style: TextStyle(
                        fontWeight: FontWeight.w500, color: AppColor.colorsUtil('#F0BE72')),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(Routes.webview, parameters: {
                          'url': AppService.configModel.privacyPolicy ?? defaultPrivacyPolicy
                        });
                      },
                  ),
                ],
              ),
              60.verticalSpaceFromWidth,
            ],
          ),
        ),
      ),
    );
  }
}
