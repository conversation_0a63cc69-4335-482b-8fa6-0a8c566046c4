import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:get/get.dart';

class AuthLoginPageController extends GetxController {
  var selected = false.obs;
  @override
  void onInit() {
    super.onInit();
    Analytics().logEvent(Analytics.view, screen: Analytics.pageAgesafety);
  }

  login() async {
    if (selected.value == false) {
      bool? result = await ProfileDialog.userDialog(
        title: 'System',
        content: 'Must confirm to Login and Use',
        confirmTitle: 'Yes',
        cancelTitle: 'No',
      );
      if (result == false) {
        return;
      }
    }
    Get.offAllNamed(Routes.tabs);
  }
}
