import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class AnimatedSplashPageController extends GetxController {
  static AnimatedSplashPageController get to => Get.find();
  String nextPage = '';
  var animated = 0.obs;
  bool animatedEnd = false;
  bool firstOpen = false;
  //广告加载超时
  bool adLoadTimeOut = false;
  //广告加载完成
  bool adLoadComplete = false;
  Duration animatedDuration = const Duration(milliseconds: 2000);
  bool showNetworkAlert = false;

  @override
  void onInit() {
    super.onInit();
    nextPage = Routes.tabs;
    deviceLogin();
  }

  //设备登录
  deviceLogin() async {
    firstOpen = Get.find<SPService>().get(spNewUserDeviceLogin) == null;
    //只在第一次打开APP时自动使用设备登录
    if (firstOpen == true) {
      //第一次打开APP 如果没有网络则停留在启动页  ios端会在获得网络权限后调用此方法登录
      if (NetworkUtil().lastConnectResult == ConnectivityResult.none ||
          (GetPlatform.isIOS && NetworkUtil().lastConnectResult == null)) {
        Future.delayed(const Duration(seconds: 3), () async {
          if (NetworkUtil().lastConnectResult == ConnectivityResult.none) {
            showNetworkAlert = true;
            bool? result = await ProfileDialog.userDialog(
              title: 'Amor needs Internet Access',
              content: 'go allow in system settings',
              confirmTitle: 'Yes',
              cancelTitle: 'No',
            );
            showNetworkAlert = false;
            if (result == true) {
              openAppSettings();
            }
          }
        });
        return;
      }
      if (showNetworkAlert == true) {
        Get.back();
      }
      //开始动画
      await Future.delayed(const Duration(milliseconds: 1000), () {
        animated.value = 1;
      });
      bool? result;

      Future.delayed(const Duration(seconds: 6), () {
        //超过6秒后如果登录接口还没有返回结果则展示超时
        if (result == null && Get.currentRoute == Routes.animatedSplash) {
          toNextPage();
          return;
        }
      });
      result = await UserService.to.deviceLogin();
      //首次打开需要等待登录成功，所以不能在页面动画完成后跳转
      Future.delayed(const Duration(milliseconds: 2000), () => toNextPage());
    } else {
      Future.delayed(const Duration(milliseconds: 500), () {
        animated.value = 1;
      });
    }
    cacheData();
  }

  toNextPage() async {
    //如果cloak为true，等待2秒再检查cloak
    if (AppService.audit == true) {
      await Future.delayed(const Duration(milliseconds: 2000));
    }
    if (firstOpen == true) {
      Get.find<SPService>().set(spNewUserDeviceLogin, false);
      if (AppService.audit == true) {
        nextPage = Routes.auth;
      } else {
        toChatPage();
        return;
      }
    }
    Get.offAllNamed(nextPage);
  }

  //首次启动跳转聊天页
  toChatPage() async {
    String? modelId = AppService.configModel.openScreen;
    if (modelId == null) {
      ConfigModel? model = await CommonApis.getInitConfig();
      modelId = model?.openScreen;
    }
    if (modelId != null) {
      Map? params;
      await AmorsApis.createSession(modelId: int.tryParse(modelId) ?? 0, loding: false)
          .then((value) => params = value);
      // await Future.delayed(Duration(milliseconds: animatedEnd ? 0 : 1000));
      Map<String, dynamic>? userLoginInfo =
          Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
      if (userLoginInfo?['register'] == true) {
        await Get.toNamed(Routes.register);
      } else {
        // await Future.delayed(Duration(milliseconds: params != null ? 0 : 1000));
      }
      if (params?['sessionNo'] != null && Get.currentRoute == Routes.animatedSplash) {
        Get.offAllNamed(Routes.session,
            arguments: params?['sessionNo'], parameters: {'launchToChat': 'true'});
        return;
      }
    }
    Get.offAllNamed(nextPage);
  }

  //缓存
  cacheData() {
    if (UserService.to.isLogin) {
      //获取用户信息
      UserService.to.getUserInfo();
      //缓存订阅、内购信息
      CacheSaleService.cacheRequest();
      // 缓存头像、昵称
      CacheSaleService.cacheNicknameAvatar();
    }
  }

  //开屏广告
  showAppOpenAd() async {
    /*
    if (firstOpen == true) {
      return;
    }
    //非vip展示广告
    if (AppService.sp.get(spUserVipInfo) != true) {
      ApplovinUtil.appOpenAdIsReady(
        callBack: (success) {
          adLoadComplete = true;
          if (success == false) {
            animated.value = 1;
          }
        },
        displayCallBack: () {
          adLoadComplete = true;
          toNextPage();
        },
      );
      //设置超时
      Future.delayed(const Duration(seconds: 3), () {
        //3秒后广告还未展示
        if (Get.isRegistered<AnimatedSplashPageController>() && adLoadComplete == false) {
          adLoadTimeOut = true;
          toNextPage();
        }
      });
      return;
    }
    //开始动画
    Future.delayed(const Duration(milliseconds: 100), () {
      animated.value = 1;
    });
      */
  }
}
