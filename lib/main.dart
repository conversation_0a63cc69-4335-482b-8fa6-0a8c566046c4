import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'package:get/get.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  //服务初始化.
  await initService();
  runApp(const AmorApp());
  //设置推送
  FirebaseMessagingService.setOnBackgroundMessage(_firebaseMessagingBackgroundHandler);
}

Future<void> initService() async {
  await Get.putAsync(() => SPService().init());
  AppService.setDevideID();
  await AppService.initHttpUtils();
  await AppService.initSql();
  Get.put(UserService().init());
  AppService.setSystemUi();
  AppService.initThirdPartySdk();
  AppService.initRefresh();
  debugPrint('初始化服务');
}

class AmorApp extends StatelessWidget {
  const AmorApp({super.key});

  @override
  Widget build(BuildContext context) {
    //禁止横屏
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      splitScreenMode: true, //支持分屏尺寸
      builder: (context, child) {
        return GetMaterialApp(
          title: 'Amor AI',
          useInheritedMediaQuery: true,
          //主题
          theme: ThemeData(
            //默认字体
            fontFamily: fontBeVietnamPro,
            splashColor: Colors.transparent,
            // 点击时的高亮效果设置为透明
            highlightColor: Colors.transparent,
            // 长按时的扩散效果设置为透明
            textButtonTheme: TextButtonThemeData(
              // 去掉 TextButton 的水波纹效果
              style: ButtonStyle(
                  splashFactory: NoSplash.splashFactory,
                  textStyle: WidgetStateProperty.all<TextStyle>(
                      const TextStyle(letterSpacing: 0, fontFamily: fontBeVietnamPro)),
                  overlayColor: const WidgetStatePropertyAll(Colors.transparent)),
            ),
            //设置输入框光标颜色
            textSelectionTheme: TextSelectionThemeData(
              cursorColor: AppColor.colorsUtil('F0BE72'),
            ),
            scaffoldBackgroundColor: AppColor.mainBg,
            //页面默认背景色
            brightness: Brightness.dark,
            //白天模式
            primarySwatch: Colors.blue,
            //主题色
            textTheme: Typography.englishLike2021.copyWith(
              bodyMedium: const TextStyle(
                letterSpacing: -0.3,
                color: Colors.white,
              ),
              bodySmall: const TextStyle(
                letterSpacing: -0.3,
                color: Colors.white,
              ),
              bodyLarge: const TextStyle(
                letterSpacing: -0.3,
                color: Colors.white,
              ),
            ),
            appBarTheme: AppBarTheme(
              backgroundColor: AppColor.mainBg, //导航栏颜色
              centerTitle: true,
              elevation: 0, //去除阴影
              scrolledUnderElevation: 0,
              titleTextStyle: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: AppColor.primaryText,
              ),
              //导航栏按钮颜色
              iconTheme: const IconThemeData(
                color: Colors.white,
              ),
            ),
          ),
          //本地化语言
          translations: AmorTraService(),
          locale: AmorTraService.locale,
          fallbackLocale: AmorTraService.fallbackLocale,
          navigatorObservers: [AppRouteObserver().routeObserver],
          //处理到未定义路线的导航
          unknownRoute: Routes.pages.first,
          getPages: Routes.pages,
          //默认路由  启动页
          initialRoute: Routes.animatedSplash,
          // 关闭调试条
          debugShowCheckedModeBanner: false,

          /// 构建
          builder: (context, child) {
            return MediaQuery(
              ///设置文字大小不随系统设置改变
              data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
              child: KeyboardDismissOnTap(
                child: FlutterEasyLoading(child: child),
              ),
            );
          },
        );
      },
    );
  }
}
