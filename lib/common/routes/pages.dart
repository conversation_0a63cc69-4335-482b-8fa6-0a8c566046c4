//路由表
import 'package:amor_app/common/routes/middleware.dart';
import 'package:amor_app/pages/404/page.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/amors/sub_pages/creat_img_video/controller.dart';
import 'package:amor_app/pages/amors/sub_pages/creat_img_video/poage.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/page.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_one/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_one/page.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_one/sub_pages/crop_image/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_one/sub_pages/crop_image/page.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_three/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_three/page.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/page.dart';
import 'package:amor_app/pages/klone/widgets/klone_blank.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/controller.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/page.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/sub_pages/input_code/controler.dart';
import 'package:amor_app/pages/login/sub_pages/account_login/sub_pages/input_code/page.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/about_us/page.dart';
import 'package:amor_app/pages/profile/sub_pages/delete_account/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/delete_account/page.dart';
import 'package:amor_app/pages/profile/sub_pages/edit_userinfo/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/edit_userinfo/page.dart';
import 'package:amor_app/pages/profile/sub_pages/notification/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/notification/page.dart';
import 'package:amor_app/pages/profile/sub_pages/preferences/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/preferences/page.dart';
import 'package:amor_app/pages/profile/sub_pages/register/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/register/page.dart';
import 'package:amor_app/pages/profile/sub_pages/setting/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/setting/page.dart';
import 'package:amor_app/pages/profile/sub_pages/task/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/task/page.dart';
import 'package:amor_app/pages/profile/sub_pages/test_ad/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/test_ad/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/page.dart';
import 'package:amor_app/pages/session/sub_pages/role_details/controller.dart';
import 'package:amor_app/pages/session/sub_pages/role_details/view.dart';
import 'package:amor_app/pages/session/sub_pages/session_set_bg/controller.dart';
import 'package:amor_app/pages/session/sub_pages/session_set_bg/page.dart';
import 'package:amor_app/pages/session/sub_pages/undress/controller.dart';
import 'package:amor_app/pages/session/sub_pages/undress/page.dart';
import 'package:amor_app/pages/session/sub_pages/call/controller.dart';
import 'package:amor_app/pages/session/sub_pages/call/page.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:amor_app/pages/splash/controller.dart';
import 'package:amor_app/pages/splash/page.dart';
import 'package:amor_app/pages/splash/sub_pages/auth_page/controller.dart';
import 'package:amor_app/pages/splash/sub_pages/auth_page/page.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:amor_app/pages/tabs/page.dart';
import 'package:amor_app/pages/webview/controller.dart';
import 'package:amor_app/pages/webview/page.dart';
import 'package:get/get.dart';

class Routes {
//定义常量
//getroute
  static const String notFound = '/404_page';
  static const String webview = '/webview';
  static const String tabs = '/tabs';
  static const String session = '/session';
  static const String cloneStepOne = '/clone_step_one';
  static const String animatedSplash = '/animated_splash';
  static const String register = '/register';
  static const String sessionSetBg = '/session_set_bg';
  static const String aboutus = '/aboutus';
  static const String setting = '/setting';
  static const String deleteAccount = '/delete_account';
  static const String editUserinfo = '/edit_userinfo';
  static const String accountLogin = '/account_login';
  static const String loginInputCode = '/login_input_code';
  static const String notification = '/notification';
  static const String task = '/task';
  static const String auth = '/auth';
  static const String clone = '/clone';
  static const String cropCloneImage = '/crop_clone_image';
  static const String cloneStepTwo = '/clone_step_two';
  static const String cloneStepThree = '/clone_step_three';
  static const String cloneEdit = '/clone_edit';
  static const String roleDetail = '/role_detail';
  static const String preferences = '/preferences';
  static const String test = '/test';
  static const String undress = '/undress';
  static const String voiceCall = '/voice_call';
  static const String creatImgVideo = '/creat_img_video';

//路由映射
  static final pages = [
    GetPage(
      name: notFound,
      page: () => const NotFound(),
    ),
    GetPage(
      name: Routes.webview,
      page: () => const WebviewPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<WebviewPageController>(() => WebviewPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.tabs,
      page: () => const TabsPage(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 500),
      binding: BindingsBuilder(
        () {
          Get.lazyPut<TabsController>(() => TabsController());
          Get.lazyPut<AmorsPageController>(() => AmorsPageController());
          Get.lazyPut<KlonePageController>(() => KlonePageController());
          Get.lazyPut<InnerPageController>(() => InnerPageController());
          Get.lazyPut<ProfilePageController>(() => ProfilePageController());
        },
      ),
      middlewares: [DefaultMiddleware()],
    ),
    GetPage(
      name: Routes.session,
      page: () => const SessionPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<SessionPageController>(() => SessionPageController());
          Get.lazyPut<SessionWidgetsController>(() => SessionWidgetsController());
        },
      ),
    ),
    GetPage(
      name: Routes.cloneStepOne,
      page: () => const KloneStepOnePage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<KloneStepOnePageController>(() => KloneStepOnePageController());
        },
      ),
    ),
    GetPage(
      name: Routes.cloneStepTwo,
      page: () => const KloneStepTwoPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<KloneStepTwoPageController>(() => KloneStepTwoPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.cloneStepThree,
      page: () => const KloneStepThreePage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<KloneStepThreePageController>(() => KloneStepThreePageController());
        },
      ),
    ),
    GetPage(
      name: Routes.animatedSplash,
      page: () => const AnimtedSpalsh(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 500),
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<AnimatedSplashPageController>(() => AnimatedSplashPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.register,
      page: () => const RegisterPage(),
      middlewares: [DefaultMiddleware()],
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 500),
      binding: BindingsBuilder(
        () {
          Get.lazyPut<RegisterPageController>(() => RegisterPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.sessionSetBg,
      page: () => const SessionSetBgPage(),
      // transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<SessionSetBgPageController>(() => SessionSetBgPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.aboutus,
      page: () => const AboutUsPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
    ),
    GetPage(
      name: Routes.setting,
      page: () => const SettingPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<SettingPageController>(() => SettingPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.deleteAccount,
      page: () => const DeleteAccountPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<DeleteAccountPageController>(() => DeleteAccountPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.editUserinfo,
      page: () => const EditUserinfoPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<EditUserinfoPageController>(() => EditUserinfoPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.accountLogin,
      page: () => const AccountLoginPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<AccountLoginPageController>(() => AccountLoginPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.loginInputCode,
      page: () => const LoginInputCodePage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<LoginInputCodeController>(() => LoginInputCodeController());
        },
      ),
    ),
    GetPage(
      name: Routes.notification,
      page: () => const NotificationPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<NotificationPageController>(() => NotificationPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.task,
      page: () => const TaskPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<TaskPageController>(() => TaskPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.clone,
      page: () => const KloneBlankPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
    ),
    GetPage(
      name: Routes.auth,
      page: () => const AuthLoginPage(),
      transition: Transition.fadeIn,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<AuthLoginPageController>(() => AuthLoginPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.cropCloneImage,
      page: () => const CropCloneImagePage(),
      transition: Transition.fadeIn,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<CropCloneImagePageController>(() => CropCloneImagePageController());
        },
      ),
    ),
    GetPage(
      name: Routes.cloneEdit,
      page: () => const KloneEditPage(),
      transition: Transition.rightToLeftWithFade,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<KloneEditPageController>(() => KloneEditPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.roleDetail,
      page: () => const RoleDetailsPage(),
      transition: Transition.rightToLeftWithFade,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<RoleDetailsController>(() => RoleDetailsController());
        },
      ),
    ),
    GetPage(
      name: Routes.preferences,
      page: () => const PreferencesPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<PreferencesPageController>(() => PreferencesPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.test,
      page: () => const TestAdPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<TestAdPageController>(() => TestAdPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.undress,
      page: () => const UndressPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<UndressPageController>(() => UndressPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.voiceCall,
      page: () => const VoiceCallPage(),
      transition: Transition.downToUp,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<VoiceCallPageController>(() => VoiceCallPageController());
        },
      ),
    ),
    GetPage(
      name: Routes.creatImgVideo,
      page: () => const CreatImgVideoPage(),
      transition: Transition.rightToLeft,
      middlewares: [DefaultMiddleware()],
      binding: BindingsBuilder(
        () {
          Get.lazyPut<CreatImgVideoPageController>(() => CreatImgVideoPageController());
        },
      ),
    ),
  ];
  static initController() {
    Get.lazyPut<TabsController>(() => TabsController());
    Get.lazyPut<AmorsPageController>(() => AmorsPageController());
    Get.lazyPut<KlonePageController>(() => KlonePageController());
    Get.lazyPut<InnerPageController>(() => InnerPageController());
    Get.lazyPut<ProfilePageController>(() => ProfilePageController());
  }
}
