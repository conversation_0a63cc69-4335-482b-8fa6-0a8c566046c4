import 'package:flutter/material.dart';
import 'package:get/get.dart';

//跳转的中间件
class DefaultMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // return null; //不做操作
    return super.redirect(route);
    // return const RouteSettings(name: '/404_PAGE', arguments: {}); //
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (page != null) {}
    return super.onPageCalled(page);
  }
}

/*
Redirect

当被调用路由的页面被搜索时，这个函数将被调用。它将RouteSettings作为重定向的结果。或者给它null，就没有重定向了。

RouteSettings redirect(String route) {
  final authService = Get.find<AuthService>();
  return authService.authed.value ? null : RouteSettings(name: '/login')
}

onPageCalled

在调用页面时，创建任何东西之前，这个函数会先被调用。 您可以使用它来更改页面的某些内容或给它一个新页面。

GetPage onPageCalled(GetPage page) {
  final authService = Get.find<AuthService>();
  return page.copyWith(title: 'Welcome ${authService.UserName}');
}

OnBindingsStart

这个函数将在绑定初始化之前被调用。 在这里，您可以更改此页面的绑定。

List<Bindings> onBindingsStart(List<Bindings> bindings) {
  final authService = Get.find<AuthService>();
  if (authService.isAdmin) {
    bindings.add(AdminBinding());
  }
  return bindings;
}

OnPageBuildStart

这个函数将在绑定初始化之后被调用。 在这里，您可以在创建绑定之后和创建页面widget之前执行一些操作。

GetPageBuilder onPageBuildStart(GetPageBuilder page) {
  print('bindings are ready');
  return page;
}

OnPageBuilt

这个函数将在GetPage.page调用后被调用，并给出函数的结果，并获取将要显示的widget。
OnPageDispose

这个函数将在处理完页面的所有相关对象(Controllers, views, ...)之后被调用。
*/