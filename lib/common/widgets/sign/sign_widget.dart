import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';

class SignBoardWidget extends StatelessWidget {
  final List<SignInitItemModel> items;
  //签到了几天
  final int signInTimes;
  const SignBoardWidget({super.key, required this.items, required this.signInTimes});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(
          items.length,
          (index) => Column(
            children: [
              Container(
                height: 55.w,
                width: 30.w,
                decoration: BoxDecoration(
                  color: index + 1 > signInTimes
                      ? AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.5)
                      : Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30.w / 2),
                ),
                child: Column(
                  children: [
                    8.verticalSpaceFromWidth,
                    Text(
                      index == items.length - 1 ? 'Gift' : '${items.elementAt(index).gems ?? 0}',
                      style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w600),
                    ),
                    const Spacer(),
                    Image.asset(
                      index + 1 > signInTimes
                          ? Assets.assetsImagesSessionGems
                          : Assets.assetsImagesGemsShortSelect,
                      width: 20.w,
                    ),
                    5.verticalSpaceFromWidth,
                  ],
                ),
              ),
              10.verticalSpaceFromWidth,
              Text(
                signInTimes == index
                    ? 'Today'
                    : items.length - 1 == index
                        ? 'Bonus'
                        : items.elementAt(index).day == 1
                            ? '1st'
                            : items.elementAt(index).day == 2
                                ? '2nd'
                                : items.elementAt(index).day == 3
                                    ? '3rd'
                                    : '${items.elementAt(index).day}th',
                style: TextStyle(
                  fontSize: 11.sp,
                  color: signInTimes == index
                      ? AppColor.colorsUtil('#F0BE72')
                      : index > signInTimes
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
