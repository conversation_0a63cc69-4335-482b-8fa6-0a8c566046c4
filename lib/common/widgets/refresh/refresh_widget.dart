import 'package:amor_app/common/utils/utils.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';

class RefreshWidget {
  //透明底部
  static Footer transparencyFooter() {
    return ClassicFooter(
      succeededIcon: Container(),
      showMessage: false,
      showText: false,
      noMoreIcon: null,
      pullIconBuilder: (context, state, animation) {
        return Container();
      },
      iconTheme: const IconThemeData(color: AppColor.mainBg),
      triggerOffset: 0,
      safeArea: false,
    );
  }
}
