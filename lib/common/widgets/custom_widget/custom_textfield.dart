import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomTextField {
  static Widget textField({
    TextEditingController? textCtl,
    TextInputAction textInputAction = TextInputAction.done,
    Function(String value)? onChanged,
    int fontSize = 16,
    FontWeight? fontWeight,
    bool autofocus = false,
    String? hintText,
    int maxLength = 600,
    int maxLines = 1,
    TextInputType textInputType = TextInputType.text,
    TextAlign textAlign = TextAlign.start,
    FocusNode? focusNode,
    String? text,
    bool enable = true,
    bool showCount = false,
    Color? textColor = AppColor.primaryText,
  }) {
    return TextField(
      enabled: enable,

      controller: textCtl ??
          TextEditingController.fromValue(TextEditingValue(
              text: text ?? '',
              selection: TextSelection.fromPosition(
                  TextPosition(affinity: TextAffinity.downstream, offset: (text ?? '').length)))),
      // TextEditingController(text: text ?? ''),
      keyboardType: textInputType,
      textInputAction: textInputAction,
      textAlignVertical: TextAlignVertical.center,
      // onEditingComplete: onEditingComplete,
      focusNode: focusNode,
      onSubmitted: (value) {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      onChanged: onChanged,
      style: TextStyle(
        fontSize: fontSize.sp,
        fontWeight: fontWeight,
        fontFamily: fontBeVietnamPro,
        letterSpacing: GetPlatform.isIOS ? -0.5 : 0,
        color: textColor,
        textBaseline: TextBaseline.alphabetic,
      ),
      maxLines: null,
      textAlign: textAlign,
      minLines: 1,
      maxLength: maxLength,
      autofocus: autofocus,
      // cursorColor: AppColor.mainColor,
      decoration: InputDecoration(
        isCollapsed: true,
        counterText: showCount ? null : "",
        counterStyle: TextStyle(fontSize: 12.sp, color: Colors.white.withValues(alpha: 0.2)),
        // counter: Column(
        //   mainAxisAlignment: MainAxisAlignment.center,
        //   children: [
        //     Icon(
        //       Icons.abc,
        //       color: Colors.amber,
        //     ),
        //   ],
        // ),
        border: const OutlineInputBorder(borderSide: BorderSide.none),
        hintText: hintText ?? '',
        hintStyle: TextStyle(
            locale: const Locale('en', 'US'),
            fontFamily: fontBeVietnamPro,
            color: AppColor.primaryText.withValues(alpha: 0.2),
            fontSize: fontSize.sp,
            height: 1.2,
            letterSpacing: GetPlatform.isIOS ? -0.5 : 0,
            fontWeight: FontWeight.normal),
      ),
    );
  }

  static Widget searchTextField(
      {TextEditingController? ctl,
      ValueChanged? onSubmit,
      ValueChanged? onChanged,
      String hintText = 'Search',
      FocusNode? focusNode,
      Widget? suffixIcon,
      bool autofocus = true}) {
    return TextFormField(
      controller: ctl,
      // strutStyle: const StrutStyle(leading: 0.5),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      textAlignVertical: TextAlignVertical.center,
      onEditingComplete: () {},
      onChanged: onChanged,
      onFieldSubmitted: onSubmit,
      style: TextStyle(
        letterSpacing: GetPlatform.isIOS ? -1 : 0,
        fontSize: 16.sp,
        height: 1.2,
        fontFamily: fontBeVietnamPro,
        color: AppColor.primaryText,
      ),
      maxLength: 10,
      focusNode: focusNode,
      autofocus: autofocus,
      // cursorColor: AppColor.mainColor,
      decoration: InputDecoration(
        isCollapsed: true,
        counterText: "",
        border: const OutlineInputBorder(borderSide: BorderSide.none),
        hintText: hintText,
        hintStyle: TextStyle(
            fontFamily: fontBeVietnamPro,
            letterSpacing: GetPlatform.isIOS ? -1 : -1,
            color: AppColor.primaryText.withValues(alpha: 0.2),
            fontSize: 16.sp,
            fontWeight: FontWeight.normal),
        suffixIcon: suffixIcon,
      ),
    );
  }
}
