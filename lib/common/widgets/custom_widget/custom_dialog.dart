import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomDialogWidget extends StatelessWidget {
  final String title;
  final String confirmColor;
  final String confirmTitle;
  final String? subTitle;
  final String? cancelTitle;

  const CustomDialogWidget(
      {super.key,
      required this.title,
      this.confirmColor = '#F54A48',
      required this.confirmTitle,
      this.subTitle,
      this.cancelTitle});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Center(
        child: Container(
          width: 280.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: AppColor.colorsUtil('#1F1F21'),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: REdgeInsets.symmetric(vertical: 20, horizontal: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: REdgeInsets.symmetric(horizontal: 10),
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontSize: 17,
                            height: 1.3,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryText),
                      ),
                    ),
                    if (subTitle != null) 10.verticalSpace,
                    if (subTitle != null)
                      Text(
                        subTitle!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColor.primaryText.withValues(alpha: 0.8),
                        ),
                      ),
                  ],
                ),
              ),
              Divider(
                height: 0.3,
                color: AppColor.colorsUtil('#3B3C3E'),
              ),
              Row(
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back(result: false);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 44.w,
                      width: 140.w - 0.5,
                      child: Text(
                        cancelTitle ?? 'Cancel',
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: AppColor.primaryText.withValues(alpha: 0.3)),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      height: 44.w,
                      color: AppColor.colorsUtil('#3B3C3E'),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back(result: true);
                    },
                    child: Container(
                      height: 44.w,
                      width: 140.w - 0.5,
                      alignment: Alignment.center,
                      child: Text(
                        confirmTitle,
                        style: TextStyle(
                            fontSize: 16,
                            color: AppColor.colorsUtil(confirmColor),
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
