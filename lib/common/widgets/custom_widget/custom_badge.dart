import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';

class CustomBadge {
  static Widget tabBadge(Widget needRedWidget, int newMsgNum) {
    if (newMsgNum < 1) {
      return needRedWidget;
    }
    String msgTips = newMsgNum > 99 ? '99+' : '$newMsgNum';
    return _getBadge(needRedWidget: needRedWidget, msgTips: msgTips, top: 3.w, end: -6.w);
  }

  static Widget mineNoticeBadge({
    required Widget needRedWidget,
    required int newMsgNum,
    double? fontSizeNum,
    double? top,
    double? end,
  }) {
    if (newMsgNum < 1) {
      return needRedWidget;
    }
    String msgTips = newMsgNum > 99 ? '99+' : '$newMsgNum';
    return _getBadge(
      needRedWidget: needRedWidget,
      msgTips: msgTips,
      fontSizeNum: fontSizeNum,
      end: end,
      top: top,
    );
  }

  static Widget _getBadge({
    required Widget needRedWidget,
    required String msgTips,
    double? fontSizeNum,
    double? top,
    double? end,
  }) {
    return badges.Badge(
      position: badges.BadgePosition.topEnd(top: top ?? -8, end: end ?? -10),
      badgeAnimation: const badges.BadgeAnimation.fade(),
      badgeContent: Text(
        msgTips,
        style: TextStyle(
          color: Colors.white,
          fontFamily: fontBeVietnamPro,
          fontWeight: FontWeight.w500,
          fontSize: fontSizeNum ?? 10,
        ),
      ),
      child: needRedWidget,
    );
  }
}
