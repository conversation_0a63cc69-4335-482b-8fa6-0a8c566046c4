import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class CustomBackButton extends StatelessWidget {
  final VoidCallback? back;
  final String img;
  final double width;
  const CustomBackButton(
      {super.key, this.back, this.img = Assets.assetsImagesBackWhite, this.width = 24});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        if (back != null) {
          back!();
        } else {
          // Get.back();
          //解决用 PopScope 不能监听返回按钮的问题
          Navigator.maybePop(context);
        }
      },
      highlightColor: Colors.transparent,
      icon: RotatedBox(
        quarterTurns: AmorTraService.ar == true ? 2 : 0,
        child: Image.asset(
          img,
          width: width,
          height: width,
        ),
      ),
    );
  }
}

class ImageBtn extends StatelessWidget {
  const ImageBtn(
      {super.key,
      required this.iconSting,
      required this.onPressed,
      required this.width,
      required this.height});
  final String iconSting; //图片的地址
  final void Function() onPressed; //执行的方法
  final double width; //宽
  final double height; //高
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onPressed(),
      behavior: HitTestBehavior.translucent,
      child: iconSting.contains('http')
          ? CachedNetworkImage(
              imageUrl: iconSting,
              fit: BoxFit.cover,
              width: width,
              height: height,
            )
          : Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(iconSting),
                  fit: BoxFit.fill,
                ),
              ),
            ),
    );
  }
}

class GradientColorBtn extends StatelessWidget {
  final double height;
  final double? width;
  final List<Color>? colors;
  final String text;
  final TextStyle? textStyle;
  final VoidCallback onTap;
  const GradientColorBtn(
      {super.key,
      required this.height,
      this.colors,
      required this.text,
      this.textStyle,
      required this.onTap,
      this.width});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(height / 2),
          gradient: LinearGradient(
            colors: colors ?? [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')],
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: textStyle ??
              TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}
