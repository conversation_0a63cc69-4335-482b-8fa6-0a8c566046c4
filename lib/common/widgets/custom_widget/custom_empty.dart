import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';

class CustomEmptyWidget extends StatelessWidget {
  const CustomEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 1.sh -
          CommonUtil.appBarHeight() -
          CommonUtil.statusBarHeight(context) -
          120.w -
          CommonUtil.bottomBarHeight(),
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            Assets.assetsImagesEmptyIcon,
            width: 110.w,
          ),
          Text(
            'No Amors here yet',
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColor.colorsUtil('#64616B'),
            ),
          )
        ],
      ),
    );
  }
}

class ImagePlaceholder extends StatelessWidget {
  final double? width;
  const ImagePlaceholder({super.key, this.width});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColor.colorsUtil('#3B383C'),
      alignment: Alignment.center,
      child: Image.asset(
        Assets.assetsImagesMessageImagePlaceholder,
        width: width ?? 48.w,
        height: width ?? 48.w,
      ),
    );
  }
}
