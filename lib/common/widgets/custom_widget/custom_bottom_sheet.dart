import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomBottomSheet {
  static Future<dynamic> show(
      {required List<CustomBottomSheetMenuModel> menus, String? title}) async {
    return await showModalBottomSheet(
      backgroundColor: Colors.transparent,
      context: Get.context!,
      isScrollControlled: true,
      useSafeArea: false,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColor.colorsUtil('#1F1F21'),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          padding: EdgeInsets.fromLTRB(32.w, 0, 32.w, 20.w + CommonUtil.bottomBarHeight()),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (title != null)
                Column(
                  children: [
                    Padding(
                      padding: REdgeInsets.symmetric(vertical: 14),
                      child: Text(
                        title,
                        style: TextStyle(
                            fontSize: 15.sp, color: AppColor.primaryText.withValues(alpha: 0.5)),
                      ),
                    ),
                    Container(
                      height: 1,
                      color: AppColor.colorsUtil('#3B3C3E'),
                    )
                  ],
                ),
              ...List.generate(menus.length + 1, (index) {
                if (index == menus.length) {
                  return InkWell(
                    onTap: () => Get.back(),
                    child: Container(
                      margin: EdgeInsets.only(top: 5.w),
                      height: 45.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(45.w / 2),
                        color: Colors.white.withValues(alpha: 0.1),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        'Cancel'.tr,
                        style: TextStyle(fontSize: 16.sp),
                      ),
                    ),
                  );
                }
                return bottomSheetContent(
                  model: menus.elementAt(index),
                  lineH: index > 0 ? 1 : null,
                  lineColor: '#3B3C3E',
                  onTap: () => Get.back(result: index),
                );
              })
            ],
          ),
        );
      },
    );
  }

  static Widget bottomSheetContent(
      {required CustomBottomSheetMenuModel model,
      double? lineH,
      required VoidCallback onTap,
      String lineColor = '#262729'}) {
    return Column(
      children: [
        lineH != null
            ? Container(
                height: lineH,
                color: AppColor.colorsUtil(lineColor),
              )
            : Container(),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: onTap,
          child: SizedBox(
            height: 60.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                model.rightIcon != null //&& model.leftIcon != null
                    ? 24.horizontalSpace
                    : Container(),
                model.leftIcon != null
                    ? Image.asset(
                        model.leftIcon!,
                        width: 22.w,
                        fit: BoxFit.contain,
                      )
                    : Container(),
                8.horizontalSpace,
                Text(
                  model.menuTitle,
                  style: TextStyle(fontSize: 16.sp, color: Colors.white),
                ),
                8.horizontalSpace,
                model.rightIcon != null
                    ? Image.asset(
                        model.rightIcon!,
                        width: 24.w,
                        fit: BoxFit.contain,
                      )
                    : Container(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class CustomBottomSheetMenuModel {
  String menuTitle;
  String? leftIcon;
  String? rightIcon; //模型名称
  CustomBottomSheetMenuModel({
    required this.menuTitle,
    this.leftIcon,
    this.rightIcon,
  });
}
