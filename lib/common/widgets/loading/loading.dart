import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

class Loading {
  Loading() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..animationDuration = const Duration(milliseconds: 0)
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorType = EasyLoadingIndicatorType.ring
      ..indicatorSize = 35.0
      ..lineWidth = 4
      ..fontSize = 14
      ..textStyle = const TextStyle(fontWeight: FontWeight.w500)
      ..progressColor = Colors.white
      ..backgroundColor = Colors.black
      ..indicatorColor = AppColor.primaryText
      ..textColor = Colors.white
      ..maskColor = Colors.transparent
      ..userInteractions = true
      ..dismissOnTap = false
      ..maskType = EasyLoadingMaskType.custom;
  }

  static void show({
    String? text,
    Widget? indicator,
    EasyLoadingMaskType? maskType,
    bool? dismissOnTap,
  }) {
    EasyLoading.instance.loadingStyle = EasyLoadingStyle.custom;
    EasyLoading.instance.backgroundColor = Colors.black;
    EasyLoading.instance.userInteractions = false;
    EasyLoading.instance.radius = 10.0;
    EasyLoading.instance.contentPadding = const EdgeInsets.symmetric(
      vertical: 15.0,
      horizontal: 20.0,
    );
    EasyLoading.show(
      status: text,
      indicator: indicator,
      maskType: maskType,
      dismissOnTap: dismissOnTap,
    );
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static void toast(
    String text, {
    Duration? duration,
    EasyLoadingToastPosition? toastPosition,
    EasyLoadingMaskType? maskType,
    bool? dismissOnTap,
  }) {
    EasyLoading.instance.userInteractions = true;
    EasyLoading.instance.backgroundColor = const Color(0xE6000A00);
    EasyLoading.instance.radius = 20.0;
    EasyLoading.instance.contentPadding = const EdgeInsets.symmetric(vertical: 10, horizontal: 16);
    EasyLoading.showToast(
      text.tr,
      duration: duration ?? const Duration(milliseconds: 2000),
      // toastPosition: toastPosition ?? EasyLoadingToastPosition.bottom,
      maskType: maskType,
      dismissOnTap: dismissOnTap,
    );
    // FocusManager.instance.primaryFocus?.unfocus();
  }

  static void dismiss({
    bool animation = true,
  }) {
    EasyLoading.instance.userInteractions = true;
    EasyLoading.dismiss(animation: animation);
  }
}
