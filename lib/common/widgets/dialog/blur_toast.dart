import 'dart:ui';

import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BlurToast {
  static bool showToast = false;
  static show({required String content}) async {
    Future.delayed(const Duration(seconds: 2), () {
      if (showToast == true) {
        Get.back();
      }
    });
    showToast = true;
    await Get.dialog(
      BlurToastWidget(content: content),
      barrierDismissible: false,
      barrierColor: Colors.transparent,
    );
    showToast = false;
  }
}

class BlurToastWidget extends StatelessWidget {
  final String content;
  const BlurToastWidget({super.key, required this.content});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 10.0,
              sigmaY: 10.0,
            ),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 14.w),
              color: Colors.white.withValues(alpha: 0.3),
              child: Text(
                content,
                style: TextStyle(fontSize: 14.sp, color: Colors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
