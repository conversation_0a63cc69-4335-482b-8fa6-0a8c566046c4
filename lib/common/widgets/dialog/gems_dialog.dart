import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GemsDialog {
  //领取宝石提示弹窗
  static receiveGemsDialog({
    required int gemsNum,
    required String subTitle,
    required Function({required int result}) callBack,
    String? bottomTitle,
  }) async {
    Get.dialog(
      Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: 176.h,
            width: 300.w,
            height: 292.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: AssetImage(
                    Assets.assetsImagesSessionBottomDialogBg,
                  ),
                ),
              ),
              child: Column(
                children: [
                  65.verticalSpaceFromWidth,
                  EasyRichText(
                    '+$gemsNum Gems',
                    defaultStyle: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w800,
                      fontStyle: FontStyle.italic,
                      color: AppColor.colorsUtil('#F0BE72'),
                    ),
                    patternList: [
                      EasyRichTextPattern(
                          targetString: 'Gems',
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              fontStyle: FontStyle.normal))
                    ],
                  ),
                  Expanded(
                    child: Center(
                      child: Text(
                        subTitle,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.colorsUtil('#EED3AC'),
                        ),
                      ),
                    ),
                  ),
                  10.verticalSpaceFromWidth,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () => callBack.call(result: 0),
                        child: Container(
                          width: 122.w,
                          height: 42.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(42.w / 2),
                            border: Border.all(width: 1, color: AppColor.colorsUtil('#F0BE72')),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtil.gradientText(
                              text: 'Claim',
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A')
                              ],
                              textStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500)),
                        ),
                      ),
                      /*
                      if (UserService.to.isVip == false) 16.horizontalSpace,
                      if (UserService.to.isVip == false)
                        InkWell(
                          onTap: () => callBack.call(result: 1),
                          child: Container(
                            width: 122.w,
                            height: 42.w,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(42.w / 2),
                                gradient: LinearGradient(
                                  colors: [
                                    AppColor.colorsUtil('#FFDCA4'),
                                    AppColor.colorsUtil('#C8984A')
                                  ],
                                )),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  Assets.assetsImagesPlayVideoIcon,
                                  width: 26.w,
                                ),
                                5.horizontalSpace,
                                Text(
                                  'Double',
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        ),
                        */
                    ],
                  ),
                  Container(
                    height: 40.w,
                    alignment: Alignment.center,
                    child: bottomTitle != null
                        ? Text(
                            bottomTitle,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          )
                        : null,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 100.h,
            child: Image.asset(
              Assets.assetsImagesGemsDialogIcon,
              width: 180.w,
            ),
          ),
        ],
      ),
    );
  }
}
