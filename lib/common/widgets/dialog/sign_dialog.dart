import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class SignDialog {
  static late Function({required int result}) signCallBack;
  //领取宝石提示弹窗
  static sign({
    required SignInitModel model,
    required Function({required int result}) callBack,
  }) async {
    signCallBack = callBack;
    Get.dialog(
      Center(
        child: UserService.to.isVip
            ? vipSignWidget(model)
            : (model.signInTimes ?? 0) > 6
                ? commonSignWidget(model)
                : sevenDaysWidget(model),
      ),
    );
  }

  //普通用户前七天签到
  static Widget sevenDaysWidget(SignInitModel model) {
    return Container(
      width: 343.w,
      height: 590.w,
      decoration: const BoxDecoration(
        image:
            DecorationImage(image: AssetImage(Assets.assetsImagesSevenDaySignBg), fit: BoxFit.fill),
      ),
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                10.verticalSpaceFromWidth,
                Image.asset(
                  Assets.assetsImagesSevenDaySignIcon,
                  width: 160.w,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Daily Drop ',
                      style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w800,
                          color: AppColor.colorsUtil('#EED3AC'),
                          fontStyle: FontStyle.italic),
                    ),
                    Text(
                      '❤️',
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ],
                ),
                16.verticalSpaceFromWidth,
                Container(
                  height: 177.w,
                  decoration: BoxDecoration(
                    color: AppColor.colorsUtil('#202021'),
                    borderRadius: BorderRadius.circular(12.r),
                    border:
                        Border.all(color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.2)),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    children: [
                      SignBoardWidget(
                        items: model.items ?? [],
                        signInTimes: model.signInTimes ?? 0,
                      ),
                      AnimatedCrossFade(
                        duration: const Duration(milliseconds: 200),
                        firstChild: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () {
                                signCallBack.call(result: 0);
                              },
                              child: Container(
                                width: 110.w,
                                height: 32.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32.w / 2),
                                  border:
                                      Border.all(width: 1, color: AppColor.colorsUtil('#F0BE72')),
                                ),
                                alignment: Alignment.center,
                                child: CommonUtil.gradientText(
                                    text: 'Claim',
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppColor.colorsUtil('#FFDCA4'),
                                      AppColor.colorsUtil('#C8984A')
                                    ],
                                    textStyle:
                                        TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500)),
                              ),
                            ),
                            /*
                             * 暂时屏蔽广告
                            if (AppService.audit == false)
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 10.w),
                                child: Text(
                                  'or',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    height: 18 / 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            if (AppService.audit == false)
                              InkWell(
                                onTap: () {
                                  Analytics().logEvent(Analytics.clickDouble,
                                      screen: Analytics.pageClaimpop);
                                  ApplovinUtil.loadInterstitialAd(
                                    adUnitId: ApplovinUtil.interstitialAdUnitIdSign,
                                    callBack: (success) {
                                      if (success) {
                                        signCallBack.call(result: 1);
                                      }
                                    },
                                  );
                                },
                                child: Container(
                                  width: 110.w,
                                  height: 32.w,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(32.w / 2),
                                      gradient: LinearGradient(
                                        colors: [
                                          AppColor.colorsUtil('#FFDCA4'),
                                          AppColor.colorsUtil('#C8984A')
                                        ],
                                      )),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        Assets.assetsImagesPlayVideoIcon,
                                        width: 18.w,
                                      ),
                                      5.horizontalSpace,
                                      Text(
                                        'Double',
                                        style: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              */
                          ],
                        ),
                        secondChild: Padding(
                          padding: EdgeInsets.only(top: 7.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                Assets.assetsImagesGemsShortSelected,
                                width: 20.w,
                              ),
                              5.horizontalSpace,
                              Text(
                                'Come Back Tomorrow!',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        crossFadeState: model.status == 0
                            ? CrossFadeState.showFirst
                            : CrossFadeState.showSecond,
                      ),
                    ],
                  ),
                ),
                10.verticalSpaceFromWidth,
                hotDropWidget(gemsNum: model.vipGems ?? 0),
              ],
            ),
          ),
          Positioned(
            top: 16.w,
            right: 16.w,
            child: ImageBtn(
                iconSting: Assets.assetsImagesDialogCloseCircle,
                onPressed: () => Get.back(),
                width: 24.w,
                height: 24.w),
          )
        ],
      ),
    );
  }

  //普通用户签到7天后签到
  static Widget commonSignWidget(SignInitModel model) {
    return Container(
      width: 343.w,
      height: 430.w,
      margin: EdgeInsets.only(bottom: 50.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: const BoxDecoration(
        image:
            DecorationImage(image: AssetImage(Assets.assetsImagesCommonSignBg), fit: BoxFit.fill),
      ),
      child: Column(
        children: [
          30.verticalSpaceFromWidth,
          Text(
            'Claim Your Gems Everyday!',
            style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w800,
                color: AppColor.colorsUtil('#EED3AC'),
                fontStyle: FontStyle.italic),
          ),
          20.verticalSpaceFromWidth,
          Container(
            height: 152.w,
            decoration: BoxDecoration(
              color: AppColor.colorsUtil('#202021'),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.2)),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                10.verticalSpaceFromWidth,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(Assets.assetsImagesGemsShortGemsPacks, width: 84.w),
                    6.horizontalSpace,
                    Column(
                      children: [
                        15.verticalSpaceFromWidth,
                        Text(
                          'Daily Drop',
                          style: TextStyle(
                              fontSize: 15.sp,
                              color: AppColor.colorsUtil('#FFCB7C'),
                              fontWeight: FontWeight.w600,
                              fontStyle: FontStyle.italic),
                        ),
                        4.verticalSpaceFromWidth,
                        Row(
                          children: [
                            Text(
                              '+',
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  height: 20 / 16,
                                  color: AppColor.colorsUtil('#F0BE72'),
                                  fontWeight: FontWeight.w600),
                            ),
                            2.horizontalSpace,
                            Image.asset(
                              Assets.assetsImagesSessionGems,
                              width: 12.w,
                            ),
                            2.horizontalSpace,
                            Text(
                              NumberFormat('#,###,000').format(model.signGems ?? 0),
                              style: TextStyle(
                                  fontSize: 15.sp,
                                  height: 19 / 15,
                                  color: AppColor.colorsUtil('#F0BE72').withValues(alpha: 0.9),
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ],
                    )
                  ],
                ),
                20.verticalSpaceFromWidth,
                AnimatedCrossFade(
                  duration: const Duration(milliseconds: 200),
                  firstChild: Row(
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          signCallBack.call(result: 0);
                        },
                        child: Container(
                          width: 110.w,
                          height: 32.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32.w / 2),
                            border: Border.all(width: 1, color: AppColor.colorsUtil('#F0BE72')),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtil.gradientText(
                              text: 'Claim',
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColor.colorsUtil('#FFDCA4'),
                                AppColor.colorsUtil('#C8984A')
                              ],
                              textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500)),
                        ),
                      ),
                      /*
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: Text(
                          'or',
                          style: TextStyle(
                            fontSize: 14.sp,
                            height: 18 / 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Analytics()
                              .logEvent(Analytics.clickDouble, screen: Analytics.pageClaimpop);
                          ApplovinUtil.loadInterstitialAd(
                            adUnitId: ApplovinUtil.interstitialAdUnitIdSign,
                            callBack: (success) {
                              if (success) {
                                signCallBack.call(result: 1);
                              }
                            },
                          );
                        },
                        child: Container(
                          width: 110.w,
                          height: 32.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(32.w / 2),
                              gradient: LinearGradient(
                                colors: [
                                  AppColor.colorsUtil('#FFDCA4'),
                                  AppColor.colorsUtil('#C8984A')
                                ],
                              )),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                Assets.assetsImagesPlayVideoIcon,
                                width: 18.w,
                              ),
                              5.horizontalSpace,
                              Text(
                                'Double',
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                      */
                    ],
                  ),
                  secondChild: Padding(
                    padding: EdgeInsets.only(top: 7.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.assetsImagesGemsShortSelected,
                          width: 20.w,
                        ),
                        5.horizontalSpace,
                        Text(
                          'Come Back Tomorrow!',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  crossFadeState:
                      model.status == 0 ? CrossFadeState.showFirst : CrossFadeState.showSecond,
                ),
              ],
            ),
          ),
          10.verticalSpaceFromWidth,
          hotDropWidget(gemsNum: model.vipGems ?? 0),
        ],
      ),
    );
  }

  //会员签到
  static Widget vipSignWidget(SignInitModel model) {
    return Container(
      width: 300,
      height: 292.w + 77.w,
      margin: EdgeInsets.only(bottom: 100.h),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: 300.w,
              height: 292.w,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.fill,
                  image: AssetImage(Assets.assetsImagesVipSignBg),
                ),
              ),
              child: Column(
                children: [
                  74.verticalSpaceFromWidth,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '+${NumberFormat('###,000').format(model.signGems ?? 0)}',
                        style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w800,
                          fontStyle: FontStyle.italic,
                          height: 1.1,
                          color: AppColor.colorsUtil('#F0BE72'),
                        ),
                      ),
                      /*
                      6.horizontalSpace,
                      Text('gems',
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColor.colorsUtil('F0BE72'))),
                              */
                    ],
                  ),
                  30.verticalSpaceFromWidth,
                  Text(
                    'Gems for HOT members\nClaim Everyday🤩',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColor.colorsUtil('#EED3AC'),
                    ),
                  ),
                  30.verticalSpace,
                  GradientColorBtn(
                    height: 42.w,
                    width: 200.w,
                    colors: [
                      AppColor.colorsUtil('#FFDCA4'),
                      AppColor.colorsUtil('#C8984A'),
                    ],
                    text: 'Claim',
                    textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
                    onTap: () {
                      signCallBack.call(result: 0);
                    },
                  ),
                ],
              ),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Image.asset(
              Assets.assetsImagesVipSignIcon,
              width: 180.w,
            ),
          ),
        ],
      ),
    );
  }

  static Widget hotDropWidget({required int gemsNum}) {
    return Container(
      height: 172.w,
      decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage(Assets.assetsImagesSignHotDropBg), fit: BoxFit.fill)),
      child: Column(
        children: [
          30.verticalSpaceFromWidth,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(Assets.assetsImagesSignGemIcon, width: 56.w),
              10.horizontalSpace,
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Hot Drop',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: AppColor.colorsUtil('#874600'),
                        fontWeight: FontWeight.w900,
                        fontStyle: FontStyle.italic),
                  ),
                  4.verticalSpaceFromWidth,
                  Row(
                    children: [
                      Text(
                        '+',
                        style: TextStyle(
                            fontSize: 15.sp,
                            height: 20 / 16,
                            color: AppColor.colorsUtil('#874600'),
                            fontWeight: FontWeight.w900),
                      ),
                      2.horizontalSpace,
                      Image.asset(
                        Assets.assetsImagesSessionGems,
                        width: 12.w,
                      ),
                      2.horizontalSpace,
                      Text(
                        NumberFormat('###,000').format(gemsNum),
                        style: TextStyle(
                            fontSize: 15.sp,
                            height: 19 / 15,
                            color: AppColor.colorsUtil('#874600').withValues(alpha: 0.9),
                            fontWeight: FontWeight.w900),
                      ),
                    ],
                  ),
                ],
              )
            ],
          ),
          14.verticalSpaceFromWidth,
          GradientColorBtn(
            height: 32.w,
            width: 122.w,
            colors: [
              AppColor.colorsUtil('#AA7235'),
              AppColor.colorsUtil('#874600'),
            ],
            text: 'Get Now',
            textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
            onTap: () {
              signCallBack.call(result: 2);
            },
          ),
          12.verticalSpaceFromWidth,
          Text(
            'Includes Other Hot Features',
            style: TextStyle(
                fontSize: 12.sp, color: AppColor.colorsUtil('#874600').withValues(alpha: 0.5)),
          )
        ],
      ),
    );
  }
}
