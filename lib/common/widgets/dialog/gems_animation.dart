import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class GemsAnimation {
  static bool showDialog = false;
  static show({int? gem, String? source}) {
    showDialog = true;
    Future.delayed(const Duration(milliseconds: 1600), () {
      if (showDialog == true) {
        Get.back();
      }
    });
    HapticFeedback.mediumImpact();
    Get.dialog(GemsAnimationWidget(source: source),
            useSafeArea: false,
            barrierDismissible: false,
            barrierColor: source == null ? Colors.transparent : null)
        .then((value) => showDialog = false);
  }
}

class GemsAnimationWidget extends StatelessWidget {
  final String? source;
  const GemsAnimationWidget({super.key, this.source});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(source ?? 'assets/lottie/task_gem_animated/task_gem_animated.json',
          animate: true, fit: BoxFit.cover, width: 1.sw, height: 1.sh),
      /*
       source == 'sign'
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  child: Lottie.asset('assets/lottie/sign_gem_animated/sign_gem_animated.json',
                      animate: true, fit: BoxFit.contain, width: 190),
                ),
                6.verticalSpaceFromWidth,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '+$gem',
                      style: TextStyle(
                        fontSize: 32.w,
                        fontWeight: FontWeight.w800,
                        fontStyle: FontStyle.italic,
                        color: AppColor.colorsUtil('#F0BE72'),
                      ),
                    ),
                    6.horizontalSpace,
                    Text(
                      'Gems',
                      style: TextStyle(
                        fontSize: 16.w,
                        fontWeight: FontWeight.w600,
                        color: AppColor.colorsUtil('#F0BE72'),
                      ),
                    ),
                  ],
                )
              ],
            )
          : Lottie.asset('assets/lottie/task_gem_animated/task_gem_animated.json',
              animate: true, fit: BoxFit.contain, width: 1.sw, height: 1.sh),
              */
    );
  }
}
