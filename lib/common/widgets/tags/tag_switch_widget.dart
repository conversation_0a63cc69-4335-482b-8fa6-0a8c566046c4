import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/material.dart';

class TagSwitchWidget extends StatelessWidget {
  final List selected;
  final Function(int index) selectedCallBack;
  const TagSwitchWidget({super.key, required this.selected, required this.selectedCallBack});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 204.w,
      height: 36.w,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(selected.isEmpty
              ? Assets.assetsImagesTagSwitchSfw
              : selected.length == 2
                  ? Assets.assetsImagesTagSwitchAll
                  : selected.first == 1
                      ? Assets.assetsImagesTagSwitchNsfw
                      : Assets.assetsImagesTagSwitchBdsm),
        ),
      ),
      child: Row(
        children: [
          InkWell(
            // onTap: () => selectedCallBack.call(0),
            child: SizedBox(
              width: 50.w,
              height: 36.w,
            ),
          ),
          InkWell(
            onTap: () => selectedCallBack.call(1),
            child: SizedBox(
              width: 80.w,
              height: 36.w,
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () => selectedCallBack.call(2),
              child: SizedBox(
                width: 68.w,
                height: 36.w,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
