import 'dart:ui';

import 'package:amor_app/common/models/amors_models/amors_category_model.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TagsSelectWidget extends StatefulWidget {
  final List tagsList;
  final List selectedTagsIndex;
  final List tagSwitchType;
  final bool multiple;
  final String backBtn;
  const TagsSelectWidget(
      {super.key,
      required this.tagsList,
      required this.selectedTagsIndex,
      required this.tagSwitchType,
      required this.multiple,
      required this.backBtn});
  @override
  State<TagsSelectWidget> createState() => _TagsSelectWidgetState();
}

class _TagsSelectWidgetState extends State<TagsSelectWidget> {
  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0, tileMode: TileMode.repeated),
        child: Container(
          color: AppColor.colorsUtil('#1D1712').withValues(alpha: 0.7),
          width: 1.sw,
          child: SafeArea(
            child: InkWell(
              onTap: () => Get.back(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: CommonUtil.appBarHeight(),
                    padding: EdgeInsets.only(left: 4.w),
                    child: Row(
                      children: [
                        if (widget.backBtn == 'top') const CustomBackButton(),
                      ],
                    ),
                  ),
                  20.verticalSpaceFromWidth,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TagSwitchWidget(
                          selected: widget.tagSwitchType,
                          selectedCallBack: (index) {
                            setState(() {
                              if (widget.tagSwitchType.contains(index)) {
                                widget.tagSwitchType.remove(index);
                                if (widget.multiple == true) {
                                  widget.selectedTagsIndex.removeWhere((element) {
                                    return (widget.tagsList[element] as TagsItemModel).label ==
                                        (index == 1 ? 'nsfw' : 'bdsm');
                                  });
                                }
                              } else {
                                widget.tagSwitchType.add(index);
                              }
                            });
                          },
                        ),
                        if (widget.backBtn == 'right')
                          ImageBtn(
                              iconSting: Assets.assetsImagesHomeTagClose,
                              onPressed: () => Get.back(),
                              width: 10.w,
                              height: 13.w),
                      ],
                    ),
                  ),
                  12.verticalSpaceFromWidth,
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.w),
                      child: tagWidget(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget tagWidget() {
    return Wrap(
      children: List.generate(widget.tagsList.length, (index) {
        String label = '';
        String tagName = '';
        var tagModel = widget.tagsList.elementAt(index);
        if (tagModel is AmorsCategoryModel) {
          tagName = tagModel.cateName ?? '';
          label = tagModel.label ?? '';
        }
        if (tagModel is TagsItemModel) {
          tagName = tagModel.name ?? '';
          label = tagModel.label ?? '';
        }
        bool hidden = false;
        if ((label == 'nsfw' && widget.tagSwitchType.contains(1) == false) ||
            (label == 'bdsm' && widget.tagSwitchType.contains(2) == false)) {
          hidden = true;
        }
        bool selected = widget.selectedTagsIndex.contains(index);
        return hidden
            ? const SizedBox(width: 0)
            : InkWell(
                onTap: () {
                  //单选
                  if (widget.multiple == false) {
                    Get.back(result: {
                      'selectedTagsIndex': [index],
                      'tagSwitchType': widget.tagSwitchType
                    });
                  } else {
                    setState(() {
                      if (widget.selectedTagsIndex.contains(index)) {
                        widget.selectedTagsIndex.remove(index);
                      } else {
                        widget.selectedTagsIndex.add(index);
                      }
                    });
                  }
                },
                child: Container(
                  height: 38.w,
                  // color: Colors.amber,
                  margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.w),
                  constraints:
                      BoxConstraints(minWidth: (label == 'nsfw' || label == 'bdsm') ? 50.w : 0),
                  child: Stack(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        margin: EdgeInsets.only(top: 6.w),
                        height: 32.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(36.w / 2),
                          border: Border.all(
                              color: selected
                                  ? AppColor.colorsUtil('#F0BE72')
                                  : Colors.white.withValues(alpha: 0.4),
                              strokeAlign: BorderSide.strokeAlignOutside),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              tagName,
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: selected == true
                                      ? AppColor.colorsUtil('F0BE72')
                                      : Colors.white.withValues(alpha: 0.4)),
                            )
                          ],
                        ),
                      ),
                      if ((label == 'nsfw' && widget.tagSwitchType.contains(1) == true) ||
                          (label == 'bdsm' && widget.tagSwitchType.contains(2) == true))
                        Positioned(
                          top: 0,
                          left: 2.w,
                          child: Image.asset(
                            label == 'nsfw'
                                ? Assets.assetsImagesTagNsfw
                                : Assets.assetsImagesTagBdsm,
                            width: 48.w,
                          ),
                        ),
                    ],
                  ),
                ),
              );
      }),
    );
  }
}
