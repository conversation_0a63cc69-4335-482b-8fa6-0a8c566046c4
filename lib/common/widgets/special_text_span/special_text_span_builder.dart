import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/app_default_value.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_edit/controller.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';

class AppSpecialTextSpan extends SpecialTextSpanBuilder {
  final bool? preview;
  AppSpecialTextSpan({this.preview = false});

  @override
  SpecialText? createSpecialText(
    String flag, {
    TextStyle? textStyle,
    SpecialTextGestureTapCallback? onTap,
    int? index,
  }) {
    if (flag == '') {
      return null;
    }
    // print('flag$flag');
    if (isStart(flag, SalutationDescriptionSpecialText.startflag)) {
      return SalutationDescriptionSpecialText(
        textStyle!,
        (value) {},
        //点击时光标移动到最后
        start: index! - (SalutationDescriptionSpecialText.startflag.length - 1),
        preview: preview,
      );
    }
    if (isStart(flag, AdvancedDescriptionSpecialText.startflag) && !flag.contains('{{')) {
      return AdvancedDescriptionSpecialText(
        textStyle!,
        (value) {},
        // start: index! - (AdvancedDescriptionSpecialText.startflag.length - 1),
        preview: preview,
      );
    }

    return null;
  }
}

//处理*号包裹的旁白文字
class AdvancedDescriptionSpecialText extends SpecialText {
  static const String startflag = '*';
  static const String endflag = '*';
  AdvancedDescriptionSpecialText(
    TextStyle textStyle,
    SpecialTextGestureTapCallback? onTap, {
    this.start,
    this.preview,
  }) : super(
          startflag,
          endflag,
          textStyle,
          onTap: onTap,
        );
  final int? start;
  //是否是预览页
  final bool? preview;
  // @overridelazy_load_indexed_stack
  // bool isEnd(String value) {
  //   return !value.contains('\n') && super.isEnd(value);
  // }
  @override
  InlineSpan finishText() {
    final String text = toString();
    final TextStyle? textStyle = this.textStyle?.copyWith(
          color: AppColor.primaryText.withValues(alpha: 0.5),
          fontStyle: FontStyle.italic,
          fontFamily: fontBeVietnamPro,
        );
    return SpecialTextSpan(
      text: text,
      actualText: text,
      // start: start,
      style: textStyle,
      deleteAll: false,
    );
  }
}

//处理{{}}号包裹的称呼文字
class SalutationDescriptionSpecialText extends SpecialText {
  static const String startflag = '{{';
  static const String endflag = '}}:';
  SalutationDescriptionSpecialText(
    TextStyle textStyle,
    SpecialTextGestureTapCallback? onTap, {
    this.start,
    this.preview,
  }) : super(
          startflag,
          endflag,
          textStyle,
          onTap: onTap,
        );
  final int? start;
  //是否是预览页
  final bool? preview;
  // @override
  // bool isEnd(String value) {
  //   return !value.contains('\n') && super.isEnd(value);
  // }

  @override
  InlineSpan finishText() {
    final String text = toString();
    final TextStyle? textStyle =
        this.textStyle?.copyWith(color: Colors.white.withValues(alpha: 0.8));
    String contentText = '';
    if (preview == true) {
      contentText =
          text == '{{user}}:' ? '{{user}}:' : '${KloneEditPageController.to.editModel.modelName}:';
    } else {
      // contentText = text == '{{user}}:'
      //     ? AdvancedSetController.to.state.myTitleString.value.isEmpty
      //         ? 'user'
      //         : '${AdvancedSetController.to.state.myTitleString.value}: '
      //     : '${CreateRoleController.to.nameCtl.text}: ';
    }
    return SpecialTextSpan(
      text: contentText,
      actualText: text,
      start: start!,
      style: textStyle,
    );
  }
}
