import 'package:amor_app/common/widgets/local_notification/widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LocalNotification {
  static OverlayEntry? notificationOverlayEntry;

  static void showNotification({
    required String title,
    required String content,
    required String sessionNo,
    String? image,
  }) {
    if (notificationOverlayEntry != null) {
      closeNotification();
    }
    notificationOverlayEntry = OverlayEntry(builder: (context) {
      return CustomNotificationWidget(
        title: title,
        content: content,
        image: image,
        sessionNo: sessionNo,
      );
    });
    Overlay.of(Get.context!).insert(notificationOverlayEntry!);
  }

  static closeNotification() {
    if (notificationOverlayEntry != null) {
      notificationOverlayEntry!.remove();
      notificationOverlayEntry = null;
    }
  }
}
