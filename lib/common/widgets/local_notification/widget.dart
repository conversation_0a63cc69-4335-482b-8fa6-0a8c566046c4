import 'dart:async';
import 'dart:ui';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'local_notification.dart';

class CustomNotificationWidget extends StatefulWidget {
  final String title;
  final String content;
  final String sessionNo;
  final String? image;
  const CustomNotificationWidget(
      {super.key, required this.title, required this.content, this.image, required this.sessionNo});

  @override
  CustomNotificationWidgetState createState() => CustomNotificationWidgetState();
}

class CustomNotificationWidgetState extends State<CustomNotificationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // Animation controller setup
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Animation setup
    _animation = Tween<double>(begin: -100, end: 0).animate(_controller);
    // Start animation
    _controller.forward();

    // Timer to automatically dismiss after 10 seconds
    _timer = Timer(const Duration(seconds: 10), () {
      _dismiss();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _dismiss() {
    _controller.reverse().then((_) {
      LocalNotification.closeNotification();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Positioned(
          top: _animation.value + MediaQuery.of(context).padding.top + 10.w,
          left: 17.w,
          right: 17.w,
          child: GestureDetector(
            onPanUpdate: (details) {
              // 只要y轴方向的移动距离小于-10，就触发dismiss
              if (details.delta.dy < -10) {
                _dismiss();
              }
            },
            child: InkWell(
              onTap: () {
                _dismiss();
                if (Get.currentRoute == Routes.session) {
                  SessionPageController.to.replaceSession(sessionNo: widget.sessionNo);
                } else {
                  Get.offNamedUntil(Routes.session, (route) => route.settings.name == Routes.tabs,
                      arguments: widget.sessionNo);
                }
              },
              child: ClipRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0, tileMode: TileMode.repeated),
                  child: Container(
                    height: 80.w,
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    decoration: BoxDecoration(
                      color: CommonUtil.colorsUtil('#2C272E').withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(8.r),
                      border: GradientBoxBorder(
                        gradient: LinearGradient(
                          colors: [
                            CommonUtil.colorsUtil('#C8984A'),
                            CommonUtil.colorsUtil('#FFDCA4')
                          ],
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        if (widget.image != null)
                          Container(
                            width: 48.w,
                            height: 48.w,
                            margin: EdgeInsets.only(right: 8.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24.w),
                            ),
                            clipBehavior: Clip.antiAlias,
                            child: CachedNetworkImage(
                              imageUrl: widget.image!,
                              fit: BoxFit.fill,
                            ),
                          ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.title,
                                style: TextStyle(
                                  fontSize: 20.sp,
                                  fontWeight: FontWeight.w600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                              // 2.verticalSpaceFromWidth,
                              Text(
                                widget.content,
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w400,
                                    fontStyle: FontStyle.italic,
                                    color: Colors.white.withValues(alpha: 0.85)),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
