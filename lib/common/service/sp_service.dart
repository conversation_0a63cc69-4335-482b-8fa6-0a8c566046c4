import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SPService extends GetxService {
  late SharedPreferences _prefs;

  Future<SPService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  /// 判断是否是JSON字符串
  _isJson(String value) {
    try {
      const JsonDecoder().convert(value);
      return true;
    } catch (error) {
      return false;
    }
  }

  /// 存储数据
  Future<void> set(String key, Object value) async {
    // String type = value.runtimeType.toString();
    if (value is String) {
      await _prefs.setString(key, value);
    } else if (value is Map) {
      String json = jsonEncode(value);
      await _prefs.setString(key, json);
    } else if (value is double) {
      await _prefs.setDouble(key, value);
    } else if (value is int) {
      await _prefs.setInt(key, value);
    } else if (value is bool) {
      await _prefs.setBool(key, value);
    } else if (value is List) {
      if ((value is List<String>) == false) {
        value = value.map((e) => e.toString()).toList();
      }
      await _prefs.setStringList(key, value as List<String>);
    }
  }

  /// 获取数据
  Object? get<T>(String key) {
    final value = _prefs.get(key);
    if (value.runtimeType.toString() == "String") {
      if (_isJson(value as String)) {
        return json.decode(value);
      }
    }
    return value;
  }

  /// 移除指定数据
  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  /// 清空数据
  Future<void> clear() async {
    await _prefs.clear();
  }

  /// 获取持久化数据中所有存入的key
  Set<String>? getKeys() {
    return _prefs.getKeys();
  }

  /// 获取持久化数据中是否包含某个key
  bool contains(String key) {
    return _prefs.containsKey(key) == true;
  }
}
