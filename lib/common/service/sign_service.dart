import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:get/get.dart';

class SignService {
  static SignInitModel? signinModel;
  static var signState = 0.obs;
  //获取签到信息
  static getSignInfo({required bool showDialog}) async {
    signinModel = await CommonApis.signinInit(loading: false);
    signState.value = signinModel?.status ?? 0;
    signState.refresh();
    if (signinModel != null && signState.value == 0 && showDialog == true) {
      int curDate = CommonUtil.currentTimeMillis();
      int date = (Get.find<SPService>().get(spSigninDialog) ?? 0) as int;
      bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
      if (!isSameDay) {
        showSignDialog();
        Get.find<SPService>().set(spSigninDialog, curDate);
      }
    }
  }

//签到
  static showSignDialog() async {
    if (signinModel == null) {
      getSignInfo(showDialog: true);
      return;
    }
    if (signState.value != 0) {
      return;
    }
    ReportUtil.reportEvents(
        page: ReportUtil.profile, action: ReportUtil.drop, value: '${signinModel!.signGems}');
    SignDialog.sign(
      model: signinModel!,
      callBack: ({required int result}) {
        //购买会员
        if (result == 2) {
          Analytics().logEvent(Analytics.clickHot, screen: Analytics.pageClaimpop);
          Analytics().logEvent(
            Analytics.view,
            sourceEvent: Analytics.clickHot,
            sourceChar: null,
            sourceScreen: Analytics.pageClaimpop,
            screen: Analytics.pageHot,
          );
          PurchaseSheet.show(page: ReportUtil.drop);
          return;
        } else {
          String signInType = 'GENERAL';
          //AD签到
          if (result == 1) {
            signInType = 'AD';
          } else {
            Analytics().logEvent(Analytics.clickClaim, screen: Analytics.pageClaimpop);
          }
          signRequest(signInType: signInType);
        }
      },
    );

    ReportUtil.reportViews(page: ReportUtil.drop, action: ReportUtil.quit);
  }

  static signRequest({required String signInType}) async {
    Map? signinResult = await CommonApis.signin(signInType: signInType);
    if (signinResult != null) {
      GemsAnimation.show();
      Future.delayed(const Duration(milliseconds: 1800), () {
        Get.back();
      });
      signState.value = 1;
      signState.refresh();
      UserService.to.getUserInfo();
    }
  }
}
