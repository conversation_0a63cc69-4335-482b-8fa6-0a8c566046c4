//初始化相关
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/db_util/app_sqlite.dart';
import 'package:amor_app/common/utils/http_util/http_util.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/firebase_options.dart';
import 'package:app_links/app_links.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppService {
  static SPService sp = Get.find<SPService>();
  static bool firstOpen = false;
  static bool audit = true; //默认审核模式
  static bool serviceModel = true;
  static PackageInfoModel packageModel =
      PackageInfoModel.fromJson(sp.get(spAppInfo) as Map<String, dynamic>);
  static ConfigModel configModel = ConfigModel();
  //是否已经上传设备信息
  static bool get deviceInfoCommit => Get.find<SPService>().get(spDeviceInfoCommit) != null;
  static late AppLinks appLinks;
  static StreamSubscription<Uri>? linkSubscription;
  //会话等级配置信息
  static List sessionLevelConfig = [];

  ///APP信息
  static Future<void> initPackageInfo() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    Map<String, String> packageInfoMap = {
      'appName': packageInfo.appName,
      'packageName': packageInfo.packageName,
      'version': packageInfo.version,
      'buildNumber': packageInfo.buildNumber,
    };
    configModel = ConfigModel.fromJson({
      'privacyPolicy': defaultPrivacyPolicy,
      'userAgreement': defaultUserAgreement,
      'ossUrl': defaultOss,
      'appName': packageInfo.appName,
      'avatar': '$defaultOss/avatar/default/avatar.png',
      'endpoint': defaultOssEndpoint,
      'bucket': defaultOssBucket,
      'voiceUrl': defaultTtsService,
    });
    sp.set(spAppInfo, packageInfoMap);
  }

  /// 存储设备数据
  static Future<void> initDeviceInfo() async {
    if (sp.get(spDeviceInfo) == null) {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      AndroidDeviceInfo? androidInfo;
      IosDeviceInfo? iosInfo;
      if (Platform.isIOS) {
        iosInfo = await deviceInfoPlugin.iosInfo;
      } else {
        androidInfo = await deviceInfoPlugin.androidInfo;
      }
      Map<String, dynamic> deviceData = <String, dynamic>{
        //手机品牌加型号
        "brand": Platform.isIOS
            ? iosInfo?.localizedModel
            : "${androidInfo?.brand} ${androidInfo?.model}",
        "manufacturer": Platform.isIOS ? iosInfo?.localizedModel : androidInfo?.brand,
        //当前系统版本
        "systemVersion": Platform.isIOS ? iosInfo?.systemVersion : androidInfo?.version.release,
        //系统名称
        "platform": Platform.isIOS ? iosInfo?.systemName : "Android",
      };
      await sp.set(spDeviceInfo, deviceData);
    }
    devideInfoUpload();
  }

//全局配置 刷新头部和底部样式
  static initRefresh() {
    EasyRefresh.defaultHeaderBuilder = () => ClassicHeader(
        processedDuration: const Duration(milliseconds: 500),
        textStyle: TextStyle(
          color: AppColor.colorsUtil('#999999'),
        ),
        messageStyle: TextStyle(
          color: AppColor.colorsUtil('#999999'),
        ),
        iconTheme: IconThemeData(color: AppColor.colorsUtil('#999999')),
        dragText: 'Pull to refresh'.tr,
        armedText: 'Release ready'.tr,
        readyText: 'Refreshing...'.tr,
        processingText: 'Refreshing...'.tr,
        processedText: 'Succeeded'.tr,
        noMoreText: 'No more'.tr,
        failedText: 'Failed'.tr,
        messageText: 'Last updated at %T'.tr,
        safeArea: false);
    EasyRefresh.defaultFooterBuilder = () => ClassicFooter(
          processedDuration: const Duration(milliseconds: 500),
          textStyle: TextStyle(
            color: AppColor.colorsUtil('#999999'),
          ),
          iconTheme: IconThemeData(color: AppColor.colorsUtil('#999999')),
          dragText: 'Pull to load'.tr,
          armedText: 'Release ready'.tr,
          readyText: 'Loading...'.tr,
          processingText: 'Loading...'.tr,
          processedText: 'Succeeded'.tr,
          noMoreText: 'No more'.tr,
          failedText: 'Failed'.tr,
          messageText: 'Last updated at %T'.tr,
          showMessage: false,
          triggerOffset: 50,
          safeArea: false,
        );
  }

  //初始化第三方SDK
  static initThirdPartySdk() async {
    //深度链接配置
    initDeepLinks();
    //Adjus初始化
    AdjustUtil.init();
    //获取app信息
    await initPackageInfo();
    //获取设备信息
    initDeviceInfo();
    setAuditMode();
    //TBA初始化
    TbaUtil().init();
    //获取app配置信息
    getCoonfigData();
    //firebase 初始化
    firebaseInit();
  }

  //初始化firebase
  static firebaseInit() async {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    // 第二次打开APP时初始化推送  第一次打开APP 初始化在收到一条消息后
    if (Get.find<SPService>().get(spDeviceInfoCommit) != null && firstOpen == false) {
      FirebaseMessagingService().init();
    }
    //RemoteConfig初始化
    RemoteConfigUtil.init();
    //Analytics初始化
    Analytics();
    // Crashlytics.
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  //设置APP风格
  static void setSystemUi() {
    Loading();
  }

  //获取配置信息
  static getCoonfigData() async {
    ConfigModel? model = await CommonApis.getInitConfig();
    if (model != null) {
      //手动设置默认头像
      model.avatar = '$defaultOss/avatar/default/avatar.png';
      model.fromServer = true;
      configModel = model;
      if (model.chatLevelConf != null) {
        sessionLevelConfig = jsonDecode(configModel.chatLevelConf!) ?? [];
      }
    }
  }

  //设置审核模式
  static setAuditMode() {
    if (sp.get(spFromDeepLink) != null) {
      audit = false;
    }
    if (kDebugMode) {
      // audit = false;
      // sp.set(spFromDeepLink, true);
    }
    // if (Get.isRegistered<AmorsPageController>() == true) {
    //   AmorsPageController.to.state.showSearch.value = !audit;
    //   if (audit == false && AmorsPageController.to.state.tagsList.length <= 1) {
    //     AmorsPageController.to.getCategorys();
    //   }
    // }
  }

  //设备信息上报
  static devideInfoUpload() async {
    if (Get.find<SPService>().get(spDeviceInfoCommit) == null) {
      bool success = await CommonApis.deviceInfo();
      if (success) {
        Get.find<SPService>().set(spDeviceInfoCommit, true);
      }
    }
  }

  //初始化网络请求
  static Future initHttpUtils() async {
    //初始化网络
    HttpUtils();
    //初始化网络监听
    NetworkUtil().init();
  }

  //初始化数据库
  static Future initSql() async {
    await AppSqlite.forFeature();
  }
/*
  //设置桌面角标
  static setBadge({required int num}) async {
    if (FirebaseMessagingService.initialized == false) {
      return;
    }
    if (await FlutterAppBadger.isAppBadgeSupported()) {
      FlutterAppBadger.updateBadgeCount(num);
    }
  }
  */

  //应用评价
  static appReview({bool inApp = false}) async {
    final InAppReview inAppReview = InAppReview.instance;
    //应用内评分
    if (inApp == true) {
      if (await inAppReview.isAvailable()) {
        inAppReview.requestReview();
      }
      return;
    }
    //应用市场评价
    inAppReview.openStoreListing(
      appStoreId: iosAppStoreId,
    );
  }

//第一次启动app 生成设备ID
  static setDevideID() async {
    // String uuid =
    //     "${CommonUtil.randomLetters(20)}${DateTime.now().microsecondsSinceEpoch}";
    // Get.find<SPService>().set(spDeviceId, uuid);
    // return;
    if (Get.find<SPService>().get(spDeviceId) == null) {
      String uuid = await CommonUtil.getDevideID();
      Get.find<SPService>().set(spDeviceId, uuid);
      firstOpen = true;
    }
  }

  //初始化深度链接
  static Future<void> initDeepLinks() async {
    debugPrint('appLink 初始化');
    appLinks = AppLinks();
    // Handle links
    linkSubscription = appLinks.uriLinkStream.listen((uri) {
      debugPrint('onAppLink: $uri');
      if (uri.toString().contains('amor.go.link') || uri.toString().contains('amorai')) {
        sp.set(spFromDeepLink, true);
        audit = false;
      }
    });
  }
}
