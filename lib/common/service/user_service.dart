import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/user_info_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/db_util/audio_entity.dart';
import 'package:amor_app/common/utils/db_util/http_entity.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/controller.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:amor_app/pages/klone/controller.dart';
import 'package:amor_app/pages/profile/controller.dart';
import 'package:amor_app/pages/profile/widgets/profile_dialog.dart';
import 'package:amor_app/pages/tabs/controller.dart';
import 'package:get/get.dart';

class UserService extends GetxService {
  static UserService get to => Get.find();
  String? token;
  String? shareCode;
  //设备登录、账号登录
  String? userLoginType;
  //已弹出一键登录弹窗
  bool? showOneClickLogin;
  var userinfo = UserInfo().obs;
  //个人中心 角色数量信息
  var chatNumData = {}.obs;

  final _isLogin = false.obs;
  set isLogin(value) => _isLogin.value = value;
  bool get isLogin => _isLogin.value;
  //设备登录用户
  bool get isTempUser => isLogin && userLoginType == 'TEMP_NA';
  bool get isVip =>
      isLogin && (userinfo.value.vipType == 'vip' || userinfo.value.vipType == 'svip');
  // bool get isVip => true;
  //是否开启自动翻译
  bool get isAutoTranslation => userinfo.value.autoTranslate == true;

  UserService init() {
    if (Get.find<SPService>().get(spLoginInfo) != null) {
      Map<String, dynamic> userLoginInfo =
          Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>;
      if (userLoginInfo['token'] != null) {
        isLogin = true;
        token = userLoginInfo['token'];
        shareCode = userLoginInfo['shareCode'];
        userLoginType = userLoginInfo['loginType'] ?? '';
      }
    }
    return this;
  }

  Future<bool> saveLoginInfo(Map userInfo) async {
    if (isLogin == true) {
      //切换用户
      clearUserInfo(switchUser: true);
    }
    await Get.find<SPService>().set(spLoginInfo, userInfo);
    init();
    refreshPageData();
    FirebaseMessagingService.submitPushToken();
    AdjustUtil.uploadAdid();
    return true;
  }

  bool toEdit = false;

  //获取用户信息
  Future getUserInfo() async {
    if (isLogin == true) {
      UserInfo? model = await ProfileApis.getUserInfo();
      if (model != null) {
        userinfo.value = model;
        Get.find<SPService>().set(spUserVipInfo, isVip);
      }
      Map userLoginInfo = (Get.find<SPService>().get(spLoginInfo) ?? {}) as Map;
      if (Get.isRegistered<ProfilePageController>() == true && TabsController.to.page == 3) {
        SignService.getSignInfo(showDialog: userLoginInfo['register'] == false);
      }
    }
  }

  //退出登录
  Future userLogout() async {
    await ProfileApis.logout();
    await clearUserInfo();
  }

  //登录或退出登录后刷新页面数据
  refreshPageData() {
    if (Get.isRegistered<AmorsPageController>() == true) {
      AmorsPageController.to.refreshPage();
    }
    if (Get.isRegistered<InnerPageController>() == true) {
      InnerPageController.to.refreshPage();
    }
    if (Get.isRegistered<KlonePageController>() == true) {
      KlonePageController.to.refreshRequest();
    }
  }

  //设备登录
  Future<bool> deviceLogin() async {
    Map<String, dynamic>? userInfo = await LoginApi.deviceLogin();
    if (userInfo != null && userInfo.containsKey('token')) {
      await saveLoginInfo(userInfo);
      return true;
    }
    return false;
  }

  //设备登录用户 充值成为会员后 提示绑定第三方账号
  //isRecharge 充值后弹窗，不用判断每日弹出次数条件
  deviceUserVipTipBind({required bool isRecharge}) async {
    if ((UserService.to.isTempUser == true && isVip == true)) {
      int curDate = CommonUtil.currentTimeMillis();
      if (isRecharge == false) {
        int date = (Get.find<SPService>().get(spDeviceUserLoginTip) ?? 0) as int;
        bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
        if (isSameDay) {
          return;
        }
      }
      Get.find<SPService>().set(spDeviceUserLoginTip, curDate);
      bool? result = await ProfileDialog.accountWarning();
      if (result == true) {
        //绑定第三方账号
        Get.toNamed(Routes.editUserinfo);
      }
    }
  }

  //未登录用户提示设备登录
  loginTip() async {
    if (showOneClickLogin == true || Get.currentRoute == Routes.animatedSplash) {
      return;
    }
    showOneClickLogin = true;
    bool? result = await ProfileDialog.oneClickLogin();
    showOneClickLogin = false;
    if (result == true) {
      Loading.show();
      bool login = await deviceLogin();
      if (login) {
        getUserInfo();
      }
      Loading.dismiss();
    }
  }

  //清除用户信息
  Future clearUserInfo({bool switchUser = false}) async {
    //切换用户时不改变登录状态
    if (switchUser == false) {
      isLogin = false;
      token = null;
      userinfo.value = UserInfo();
      chatNumData.value = {};
    }
    SPService sp = Get.find<SPService>();
    //清除用户信息
    await sp.remove(spLoginInfo);
    await sp.remove(spKloneDialog);
    await sp.remove(spAdjustIdEvent);
    await sp.remove(spExposureModel);
    await sp.remove(spInAppPurchaseOrder);
    await sp.remove(spKloneDialog);
    await sp.remove(spSigninDialog);
    await sp.remove(spUserVipInfo);
    await sp.remove(spSubscriptionAnimation);

    SignService.signState.value = 0;
    SignService.signinModel = null;
    if (switchUser == false) {
      refreshPageData();
    }
    HttpEntity.instan().remove(null);
    AudioEntity.instan().remove(null);
  }

  //判断是否是永久会员
  bool isLifetimeVip() {
    return (isVip &&
        userinfo.value.endDate != null &&
        DateTime.now()
                .difference(DateTime.fromMillisecondsSinceEpoch(userinfo.value.endDate!))
                .inDays
                .abs() >
            (365 * 10));
  }
}
