import 'package:amor_app/common/values/language/ar_SA.dart';
import 'package:amor_app/common/values/language/de_DE.dart';
import 'package:amor_app/common/values/language/en_US.dart';
import 'package:amor_app/common/values/language/es_ES.dart';
import 'package:amor_app/common/values/language/fr_FR.dart';
import 'package:amor_app/common/values/language/ja_JP.dart';
import 'package:amor_app/common/values/language/ko_KR.dart';
import 'package:amor_app/common/values/language/pt_BR.dart';
import 'package:amor_app/common/values/language/pt_PT.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AmorTraService extends Translations {
  static Locale? get locale => Get.deviceLocale;
  static const fallbackLocale = Locale('en', 'US');
  //阿拉伯语
  static bool get ar => Get.deviceLocale?.languageCode == 'ar';
  @override
  Map<String, Map<String, String>> get keys => {
        'en_US': en_US,
        'ar_SA': ar_SA,
        'fr_FR': fr_FR,
        'de_DE': de_DE,
        'es_ES': es_ES,
        'ja_JP': ja_JP,
        'ko_KR': ko_KR,
        'pt_BR': pt_BR,
        'pt_PT': pt_PT,
      };
}
