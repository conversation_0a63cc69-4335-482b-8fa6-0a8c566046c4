import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class FileUtil {
  /// 获取临时文件路径
  static Future<String> getTempDirPath() async {
    Directory? tempDir = await getTemporaryDirectory();
    return tempDir.path;
  }

  /// 获取文档目录
  static Future<String> localfilePath() async {
    Directory documentDir = await getApplicationDocumentsDirectory();
    return documentDir.path;
  }

  /// 获取文档
  static Future<File> localfile() async {
    String path = await localfilePath();
    return File('$path/channel.txt');
  }

  /// 保存内容到文本
  static saveFile(String val) async {
    try {
      File file = await localfile();
      IOSink sink = file.openWrite(mode: FileMode.append);
      sink.write(val);
      sink.close();
      // ignore: empty_catches
    } catch (e) {}
  }

  /// 录音写入文件
  static Future<String?> saveVoiceFile(Uint8List decode, String fileType) async {
    Directory tempDir = await getTemporaryDirectory();
    File file =
        await File("${tempDir.path}/voice.$fileType").writeAsBytes(decode, mode: FileMode.write);
    return file.path;
  }

  /// 通话音频写入文件
  static Future<String?> saveCallVoiceFile(Uint8List decode) async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    Directory newDirectory = Directory('${documentsDirectory.path}/callVoice');
    if (!newDirectory.existsSync()) {
      newDirectory.createSync(recursive: true);
    }
    String name = CommonUtil.randomLetters(10);
    File file = File("${newDirectory.path}/$name.mp3");
    file.writeAsBytesSync(decode, mode: FileMode.write);
    return file.path;
  }

  ///删除通话音频目录
  static Future delCallVoiceFile() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    Directory directory = Directory('${documentsDirectory.path}/callVoice');
    if (directory.existsSync()) {
      // int size = await getDirectorySize();
      // print('目录大小');
      // print(formatBytes(size));
      // debugPrint('删除目录');
      directory.deleteSync(recursive: true);
    } else {
      // print('没有目录');
    }
  }

  static Future<int> getDirectorySize() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    Directory directory = Directory('${documentsDirectory.path}/callVoice');

    String directoryPath = directory.path; // 目录的路径

    int totalSize = 0;

    await for (FileSystemEntity entity in Directory(directoryPath).list(recursive: true)) {
      if (entity is File) {
        totalSize += await entity.length();
      }
    }

    return totalSize;
  }

  static String formatBytes(int bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes == 0) return '0 B';
    int i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(2)} ${sizes[i]}';
  }
}
