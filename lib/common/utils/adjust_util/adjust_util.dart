import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:adjust_sdk/adjust_ad_revenue.dart';
// import 'package:adjust_sdk/adjust_attribution.dart';
import 'package:adjust_sdk/adjust_config.dart';
import 'package:adjust_sdk/adjust_event.dart';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class AdjustUtil {
  static init() {
    String? distinctId = (Get.find<SPService>().get(spDeviceId) ?? '').toString();
    Adjust.addGlobalCallbackParameter('customer_user_id', distinctId);
    AdjustConfig config = AdjustConfig(
        adjustAppToken, kDebugMode ? AdjustEnvironment.sandbox : AdjustEnvironment.production);
    // AdjustConfig config = AdjustConfig(adjustAppToken, AdjustEnvironment.sandbox);
    //延迟深度链接
    config.deferredDeeplinkCallback = (String? deeplink) async {
      await AppService.sp.set(spFromDeepLink, true);
      AppService.setAuditMode();
    };
    Adjust.initSdk(config);
    Future.delayed(const Duration(seconds: 3), () => uploadAdid());
  }

  static getIdfa() {
    Adjust.getIdfa();
  }

  //上传 adjust ID
  static uploadAdid() async {
    if (UserService.to.isLogin == true && Get.find<SPService>().get(spAdjustIdEvent) == null) {
      String? adid = await Adjust.getAdid();
      String? gpsAdid;
      if (Platform.isAndroid) {
        try {
          gpsAdid = await Adjust.getGoogleAdId();
        } catch (e) {
          gpsAdid = null;
        }
      }
      if (adid != null) {
        bool result = await CommonApis.registerAdid(adid: adid, gpsAdid: gpsAdid);
        if (result == true) {
          Get.find<SPService>().set(spAdjustIdEvent, true);
        }
      }
    }
  }

  static adjustEvent({required String event}) {
    AdjustEvent adjustEvent = AdjustEvent(event);
    Adjust.trackEvent(adjustEvent);
  }

  //订阅、内购收入
  static adjustRevenuEnent({required double value, required String currencyCode}) {
    AdjustEvent adjustEvent = AdjustEvent(paid);
    adjustEvent.setRevenue(value, currencyCode);
    Adjust.trackEvent(adjustEvent);
  }

  static adjustADEvent({required double value, String? currencyCode, String? adNetwork}) {
    AdjustAdRevenue adRevenue = AdjustAdRevenue('applovin_max_sdk');
    adRevenue.setRevenue(value, currencyCode ?? 'USD');
    adRevenue.adRevenueNetwork = adNetwork;
    Adjust.trackAdRevenue(adRevenue);
  }

  static setPushToken({required String token}) {
    // Adjust.setPushToken(token);
  }

  //购买事件
  static adjustSubscription({Map? param}) {
    //由服务器上报 adjust
    /*
    if (param == null) {
      return;
    }
    //事件埋点
    AdjustEvent adjustEvent = AdjustEvent(paid);
    adjustEvent.setRevenue(double.tryParse(param['price'] ?? '0') ?? 0, param['currency'] ?? 'USD');
    Adjust.trackEvent(adjustEvent);
    */
/*
订阅跟踪
    if (GetPlatform.isIOS) {
      AdjustAppStoreSubscription subscription = AdjustAppStoreSubscription(
          param['price'], param['currency'], param['transactionId'], param['receipt']);
      subscription.setTransactionDate(param['transactionDate'] ?? '');
      subscription.setSalesRegion(param['salesRegion'] ?? '');
      Adjust.trackAppStoreSubscription(subscription);
    }
    if (GetPlatform.isAndroid) {
      AdjustPlayStoreSubscription subscription = AdjustPlayStoreSubscription(
          param['price'],
          param['currency'],
          param['sku'],
          param['orderId'],
          param['signature'],
          param['purchaseToken']);
      subscription.setPurchaseTime('${param['purchaseTime'] ?? ''}');
      Adjust.trackPlayStoreSubscription(subscription);
    }
    */
  }

  static const String select = '9qfbcf';
  static const String clickAd = 'o8s5nb';
  static const String paid = '6gdcu2';
}
