import 'dart:async';

import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/splash/controller.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NetworkUtil {
  static final NetworkUtil _networkUtil = NetworkUtil._internal();

  factory NetworkUtil() {
    return _networkUtil;
  }

  NetworkUtil._internal();

  String playingUrl = '';
  late StreamSubscription<List<ConnectivityResult>> subscription;
  ConnectivityResult? lastConnectResult;

  /// 初始化网络监听
  init() async {
    // 网络监听
    subscription = Connectivity().onConnectivityChanged.listen((result) {
      // if (result != ConnectivityResult.none) {
      //   reConnectDo();
      // }
      debugPrint("网络状态:$result");
      if (lastConnectResult != result.first) {
        if (result.first == ConnectivityResult.none) {
          // Loading.dismiss();
          // Loading.toast('Network error, try again later');
        } else if (result.first != ConnectivityResult.none &&
            lastConnectResult == ConnectivityResult.none) {
          debugPrint('网络恢复后刷新');
          lastConnectResult = result.first;
          if (Get.find<SPService>().get(spNetworkStatusTime) == null ||
              CommonUtil.timeDifference(CommonUtil.currentTimeMillis(),
                      (Get.find<SPService>().get(spNetworkStatusTime) ?? 0) as int,
                      unit: 'mill') >
                  50) {
            debugPrint('网络恢复后刷新');
            reConnectDo();
          }
        }
        lastConnectResult = result.first;
      }
    });
  }

  //网络恢复后刷新接口
  Future<void> reConnectDo() async {
    if (Get.find<SPService>().get(spNewUserDeviceLogin) == null &&
        Get.isRegistered<AnimatedSplashPageController>()) {
      //只在开屏页 判断网络恢复情况，并初始化第三方sdk
      if (AppService.configModel.fromServer != true) {
        AppService.initThirdPartySdk();
      }
      AnimatedSplashPageController.to.deviceLogin();
    }

    /*
    //刷新聊天列表
    if (Get.isRegistered<ChatListController>()) {
      if (ChatListController.to.sessionList.isEmpty) {
        ChatListController.to.refreshRequest();
      }
    }
    //刷新模型
    if (Get.isRegistered<ChatRoleModelController>()) {
      if (ChatRoleModelController.to.categorys.isEmpty) {
        ChatRoleModelController.to.getCategorys();
      }
    }
    //首次启动时 如果设备登录失败 则重新登录
    if (Get.find<SPService>().get(kNewUserDeviceLogin) == null) {
      if (Get.isRegistered<TabsController>()) {
        TabsController.to.firstOpenDevice();
      }
    }
    if (AppService.configModel.fromServer != true) {
      AppService.initThirdPartySdk();
    }
    */
  }
}
