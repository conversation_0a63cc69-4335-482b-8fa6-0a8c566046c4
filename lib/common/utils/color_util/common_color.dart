import 'package:flutter/material.dart';

class AppColor {
  /// 主要文本颜色
  static const Color primaryText = Color(0xFFFFFFFF);

  /// 二级文本颜色
  static const Color secondaryText = Color(0xFF999999);

  // /// app主要颜色
  // static const Color mainColor = Color(0xFF6A34E6);

  // /// app主要颜色
  // static const Color newMainColor = Color(0xFF6130F2);

  /// app主要背景颜色
  static const Color mainBg = Color(0xFF0B0813);

  /// app次要背景颜色
  static const Color secondBg = Color(0xFF262729);
//16进制颜色
  static Color colorsUtil(String hexStr, {double alpha = 1}) {
    hexStr = hexStr.toUpperCase().replaceAll("#", "");
    int hex = int.parse(hexStr, radix: 16);
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    return Color.fromRGBO(
        (hex & 0xFF0000) >> 16, (hex & 0x00FF00) >> 8, (hex & 0x0000FF) >> 0, alpha);
  }
}
