import 'package:amor_app/common/widgets/widgets.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';

class FilePickerUtil {
  static Future<String?> pickAudio() async {
    //iOS系统展示菜单，选择相册视频或在文件中选取
    /*
    List<CustomBottomSheetMenuModel> menus = [
      CustomBottomSheetMenuModel(menuTitle: 'Choose from File'),
      CustomBottomSheetMenuModel(
        menuTitle: 'Choose from Album',
      ),
    ];
    int type = 0;
    if (GetPlatform.isIOS) {
      var menuIndex = await CustomBottomSheet.show(menus: menus, title: "Upload Voice");
      if (menuIndex == null) {
        return null;
      }
      type = menuIndex;
    }

        */

    List<String> extensions = ['aac', 'M4A', 'm4a', 'wav', 'mp3', 'ogg', 'flac', 'ape'];
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: GetPlatform.isIOS ? FileType.custom : FileType.audio,
      allowedExtensions: GetPlatform.isIOS ? extensions : null,
      allowMultiple: false,
      onFileLoading: (p0) {
        p0 == FilePickerStatus.picking ? Loading.show() : Loading.dismiss();
        // print(p0.toString());
      },
    );

    if (result != null) {
      PlatformFile file = result.files.first;
      String? path = file.path;
      return path;
      /*
      if (path != null) {
        if (type == 0) {
          String fileType = (extension(path)).substring(1);
          if (extensions.contains(fileType)) {
            return path;
          }
          Loading.toast("File format error".tr);
        } else {
          return path;
        }
      }
      */
    }
    return null; // 用户取消了文件选择
  }
}
