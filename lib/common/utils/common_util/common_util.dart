import 'dart:io';
import 'dart:math';

// import 'package:crypto/crypto.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:device_uuid/device_uuid.dart';
import 'package:encrypt/encrypt.dart' as ce;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart' hide TextDirection;
// import 'package:encrypt/encrypt.dart';
// import 'package:encrypt/encrypt_io.dart';
// ignore: depend_on_referenced_packages
import 'package:pointycastle/asymmetric/api.dart';
import 'package:url_launcher/url_launcher.dart';

class CommonUtil {
  //状态栏高度
  static double statusBarHeight(BuildContext? context) {
    if (context == null) {
      return ScreenUtil().statusBarHeight;
    }
    return MediaQueryData.fromView(View.of(context)).padding.top;
  }

  //底部安全区域高度
  static double bottomBarHeight() {
    return ScreenUtil().bottomBarHeight;
  }

  //AppBar高度
  static double appBarHeight() {
    return AppBar().preferredSize.height;
  }

//关闭应用
  static void cloeApp() {
    if (Platform.isAndroid) {
      SystemNavigator.pop();
    } else if (Platform.isIOS) {
      exit(0);
    }
  }

//16进制颜色
  static Color colorsUtil(String hexStr, {double alpha = 1}) {
    hexStr = hexStr.toUpperCase().replaceAll("#", "");
    int hex = int.parse(hexStr, radix: 16);
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    return Color.fromRGBO((hex & 0xFF0000) >> 16, (hex & 0x00FF00) >> 8, (hex & 0x0000FF) >> 0, alpha);
  }

  //获取当前时间戳
  static int currentTimeMillis() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  //时间戳转换时间
  static String timeMillisTransition({required int timestamp, String? format}) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat(format ?? 'yyyy-MM-dd').format(date);
  }

  //判断两个时间戳是否是同一天
  static bool isSameDay(int timestamp1, int timestamp2) {
    DateTime dateTime1 = DateTime.fromMillisecondsSinceEpoch(timestamp1);
    DateTime dateTime2 = DateTime.fromMillisecondsSinceEpoch(timestamp2);
    return dateTime1.year == dateTime2.year && dateTime1.month == dateTime2.month && dateTime1.day == dateTime2.day;
  }

  //时间戳转时间格式
  static DateTime timestampToDate(int timestamp) {
    DateTime dateTime = DateTime.now();

    ///如果是十三位时间戳返回这个
    if (timestamp.toString().length == 13) {
      dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp.toString().length == 16) {
      ///如果是十六位时间戳
      dateTime = DateTime.fromMicrosecondsSinceEpoch(timestamp);
    }
    return dateTime;
  }

  //计算时间差
  static int timeDifference(int timestamp, int otherTimestamp, {required String unit, bool abs = true}) {
    var difference = timestampToDate(timestamp).difference(timestampToDate(otherTimestamp));
    // print('时间相差${difference.inMinutes}分钟');
    if (unit == 'seconds') {
      if (abs) {
        return difference.inSeconds.abs(); //绝对值
      }
      return difference.inSeconds;
    }
    return difference.inMinutes.abs(); //绝对值
  }

  //phone+时间戳 加密  发送验证码
  static String aesTimestamp(int timestamp, String phone) {
    String str = '$phone:$timestamp';
    //AES加密
    final key = ce.Key.fromUtf8(msgAESKEY);
    final iv = ce.IV.fromUtf8(aAESIV);
    final encrypt = ce.Encrypter(ce.AES(key, mode: ce.AESMode.cbc));
    final encrypted = encrypt.encrypt(str, iv: iv);
    return encrypted.base16.toUpperCase(); //转换为大写
  }

//数字版本号
  static String getVersionWithNumStr(String version) {
    List<String> versionList = version.split(".");

    if (versionList.length > 2) {
      if (versionList.elementAt(2).length < 2) {
        /// 修改值，下标 含头不含尾
        versionList.fillRange(2, 3, '0${versionList.elementAt(2)}'); //最后一位前加0
        return versionList.join('');
      } else {
        return versionList.join('');
      }
    }
    return version;
  }

//截取字符串中的表情和旁白，转语音时忽略
  static String extractEmojis(String text, {bool onlyEmoji = false}) {
    if (text.isEmpty) {
      return text;
    }
    //只替换表情
    text = text.replaceAll(
        RegExp(r"[^\u4e00-\u9fa5a-zA-Z0-9\s\u3000-\u303F\uFF00-\uFFEF!#$%&()*+,-./:;<=>?@[\]^_`{|}~'.*';（）()]"), '');
    if (onlyEmoji == true) {
      return text;
    }
    return text.replaceAll(RegExp(r'\*.*?\*|\(.*?\)'), '');
  }

/*
  //保存图片
  static saveImage({String? url, Uint8List? data}) async {
    // await Permission.storage.request().isGranted;
    if (await PermissionHelper.getPermissionStatus(Permission.storage) == false) {
      Loading.toast('Please go to settings to enable permissions');
      return;
    }
    Loading.show();
    Uint8List imageData;
    if (url != null) {
      DefaultCacheManager manager = DefaultCacheManager();
      File file = await manager.getSingleFile(url);
      imageData = await file.readAsBytes();
      // print('文件大小${imageData.lengthInBytes / 1024}KB');
    } else {
      imageData = data!;
    }

    final result = await ImageGallerySaver.saveImage(imageData, quality: 100);
    if (Platform.isIOS) {
      Map data = result;
      if (data['isSuccess'] == true) {
        Loading.toast('成功保存到相册');
      } else {
        Loading.toast('保存失败');
      }
    } else {
      if (result != null) {
        Loading.toast('成功保存到相册');
      } else {
        Loading.toast('保存失败');
      }
    }
  }
*/
  //数据转换
  static Future<List<int>> voiceDataChange(List<Uint8List> binaryIntList) async {
    List<int> list = [];
    for (var element in binaryIntList) {
      list.addAll(element);
    }
    return list;
  }

  //时间转换 将秒转换为分钟：秒
  static String durationTransform(int seconds) {
    var d = Duration(seconds: seconds);
    List<String> parts = d.toString().split(':');
    return '${parts[1]}:${parts[2].substring(0, 2)}';
  }

  //时间转换 将秒转换为小时、秒
  static String durationTransformPro(int seconds) {
    // int days = seconds ~/ (24 * 3600);
    // seconds = seconds % (24 * 3600);
    // int hours = seconds ~/ 3600;
    // seconds = seconds % 3600;

    int minutes = seconds ~/ 60;
    seconds = seconds % 60;
    String formattedTime = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  // 是否中文
  static bool isChinese(String? input) {
    const String chineseRegex = "[\u4e00-\u9fa5]";
    if (input == null || input.isEmpty) return false;
    return RegExp(chineseRegex).hasMatch(input);
  }

  //数字 加单位
  static String numberUnits(int num) {
    if (num < 1000) {
      return num.toString();
    }
    if (num < 1000000) {
      String str = formatNum(num / 1000, 1);
      if (str.contains('.0')) {
        str = str.substring(0, str.length - 2);
      }
      return '${str}K';
    }
    String str = formatNum(num / 1000000, 1);
    if (str.contains('.0')) {
      str = str.substring(0, str.length - 2);
    }
    return '${str}M';
  }

  //保留小数，不四舍五入
  static String formatNum(double num, int fractionDigits) {
    if (fractionDigits == 0) {
      return num.toStringAsFixed(fractionDigits).substring(0, num.toString().lastIndexOf(".")).toString();
    }
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) < fractionDigits) {
      //小数点后有几位小数
      return num.toStringAsFixed(fractionDigits)
          .substring(0, num.toString().lastIndexOf(".") + fractionDigits + 1)
          .toString();
    } else {
      return num.toString().substring(0, num.toString().lastIndexOf(".") + fractionDigits + 1).toString();
    }
  }

  //正则表达式 截取两个字符之间的内容
  static List<String>? getTextBetweenStr({required String text, required List exp, int include = 1}) {
    List<String> resultList = [];
    for (var element in exp) {
      RegExp regExp = RegExp(element);
      Iterable<Match> matches = regExp.allMatches(text);
      for (final Match m in matches) {
        String? matchStr = m.group(element == r'\((.*?)\)' ? 0 : 1); //0包含前后字符 1 不包含
        resultList.add(matchStr ?? '');
      }
    }

    return resultList.isNotEmpty ? resultList : null;
  }

  //统计字符串中某个字符出现的次数
  static int countOccurrences(String source, String target) {
    int count = 0;
    int startIndex = 0;

    while (startIndex < source.length) {
      int index = source.indexOf(target, startIndex);
      if (index != -1) {
        count++;
        startIndex = index + target.length;
      } else {
        break;
      }
    }
    return count;
  }

  /// 随机字符
  static String randomLetters(int len) {
    String alphabet = 'QWERTYUIOPASDFGHJKLZXCVBNM1234567890qwertyuiopasdfghjklzxcvbnm';
    String r = '';
    for (var i = 0; i < len; i++) {
      final index = Random().nextInt(alphabet.length);
      r += alphabet[index];
    }
    return r;
  }

  static int lastClickTime = 0;

  ///点击事件的防抖动
  static bool isFastClick() {
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    int interval = currentTime - lastClickTime;
    if (interval < 500) {
      return true;
    }
    lastClickTime = currentTime;
    return false;
  }

  static String convertTimestamp(int timestamp) {
    // DateTime now = DateTime.now();
    // DateTime yesterday = now.subtract(Duration(days: 365));
    // timestamp= yesterday.millisecondsSinceEpoch;
    const int daysPerWeek = 7;
    const int daysPerYear = 365;
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    int timeDifference = currentTime - timestamp;
    if (timeDifference < 0) {
      return "";
    }
    if (timeDifference < Duration.minutesPerHour * Duration.millisecondsPerMinute) {
      // 一小时以内，使用分钟表示
      int minutesAgo = (timeDifference / Duration.millisecondsPerMinute).round();
      if (minutesAgo == 0) {
        DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
        return DateFormat().add_Hm().format(date);
      }
      return '${minutesAgo}m ago';
    } else if (timeDifference < Duration.hoursPerDay * Duration.millisecondsPerHour) {
      int hAgo = (timeDifference / Duration.millisecondsPerHour).round();
      return '${hAgo}h ago';
    } else if (timeDifference < daysPerWeek * Duration.hoursPerDay * Duration.millisecondsPerHour) {
      // 一周以内，使用星期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateFormat('E').format(date);
    } else if (timeDifference < daysPerYear * Duration.hoursPerDay * Duration.millisecondsPerHour) {
      // 一年以内，使用月份+日期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateFormat('MM/dd').format(date);
    } else {
      // 一年以后，使用年份+月份+日期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateFormat('yy/MM/dd').format(date);
    }
  }

  static String convertTimes(int timestamp) {
    // DateTime now = DateTime.now();
    // DateTime yesterday = now.subtract(Duration(days: 366));
    // timestamp = yesterday.millisecondsSinceEpoch;

    const int daysPerWeek = 7;
    const int daysPerYear = 365;
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    int timeDifference = currentTime - timestamp;
    if (timeDifference < 0) {
      return "";
    }
    if (timeDifference < Duration.hoursPerDay * Duration.millisecondsPerHour) {
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateFormat().add_Hm().format(date);
    } else if (timeDifference < daysPerWeek * Duration.hoursPerDay * Duration.millisecondsPerHour) {
      // 一周以内，使用星期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return "${DateFormat().add_E().format(date)} ${DateFormat().add_Hm().format(date)}";
    } else if (timeDifference < daysPerYear * Duration.hoursPerDay * Duration.millisecondsPerHour) {
      // 一年以内，使用月份+日期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return "${DateFormat('MM/dd').format(date)} ${DateFormat().add_Hm().format(date)}";
    } else {
      // 一年以后，使用年份+月份+日期表示
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return "${DateFormat('MM/dd/yy').format(date)} ${DateFormat().add_Hm().format(date)}";
    }
  }

  // 不区分大小写 匹配字符串
  static bool containsIgnoreCase(String source, String pattern) {
    RegExp regExp = RegExp(pattern, caseSensitive: false);
    return regExp.hasMatch(source);
  }

  //判断小数点前后有几位
  static int getNumDigits({required String number, required String type}) {
    List<String> parts = number.split(".");
    if (parts.length == 2) {
      int digitsBeforeDecimal = parts[0].length;
      int digitsAfterDecimal = parts[1].length;
      if (type == 'after') {
        return digitsAfterDecimal;
      } else {
        return digitsBeforeDecimal;
      }
    } else {
      return 0;
    }
  }

  //文字渐变色
  static gradientText(
      {required String text,
      Alignment? begin,
      Alignment? end,
      required List<Color> colors,
      required TextStyle textStyle}) {
    return ShaderMask(
      shaderCallback: (rect) {
        return LinearGradient(
          begin: begin ?? Alignment.centerLeft,
          end: end ?? Alignment.centerRight,
          colors: colors,
        ).createShader(rect);
      },
      child: Text(
        text,
        style: textStyle,
      ),
    );
  }

  //获取设备标识
  static Future<String> getDevideID() async {
    String uuid = '';
    const storage = FlutterSecureStorage();

    if (Platform.isAndroid) {
      final deviceUuidPlugin = DeviceUuid();
      try {
        uuid = await deviceUuidPlugin.getUUID() ?? '';
      } on PlatformException {
        uuid = '';
      }
      if (uuid.isEmpty) {
        uuid = "${DateTime.now().millisecondsSinceEpoch}${randomLetters(10)}";
      }
    } else {
      uuid =
          await storage.read(key: "AmorDeviceUUID") ?? "${DateTime.now().millisecondsSinceEpoch}${randomLetters(10)}";
      await storage.write(key: "AmorDeviceUUID", value: uuid);
    }
    return uuid;
  }

  //打开应用市场
  static launchStore() {
    if (Platform.isAndroid || Platform.isIOS) {
      final appId = Platform.isAndroid ? packageName : iosAppStoreId;
      final url = Uri.parse(
        Platform.isAndroid ? "market://details?id=$appId" : "https://apps.apple.com/app/id$appId",
      );
      launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  static String? signAtureStr;

  ///语音通话扣费签名
  static String? getApiSignature() {
    if (signAtureStr != null) {
      return signAtureStr;
    }
    try {
      final shareCode = UserService.to.userinfo.value.shareCode;
      if (shareCode == null) {
        return null;
      }
      const derEncodedPublicKey =
          'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLWMEjJb703WZJ5Nqf7qJ2wefSSYvbmQZM0CgHGrYstUaj4Mlz+P06mCqpVAYmyf3dJxLrEsUiobWvhi1Ut5W+PY0yrzEsIOJ5lJrIt1pm0/kcPsPj2d4cEl9S7DTEIJVQTGMzquAlhEkgbA0yDVXNtqqf4MECCADU/WM3WTCH2QIDAQAB';
      const publicKey = '${'-----BEGIN PUBLIC KEY-----\n$derEncodedPublicKey'}\n-----END PUBLIC KEY-----';
      final encrypter = ce.Encrypter(ce.RSA(publicKey: (ce.RSAKeyParser().parse(publicKey) as RSAPublicKey)));
      final encryptedMessage = encrypter.encrypt(shareCode);
      return encryptedMessage.base64;
    } catch (e) {
      return null;
    }
  }

  ///文本宽高
  // static double calculateTextHeight(
  //     String value, fontSize, FontWeight fontWeight, double maxWidth, int maxLines) {
  //   value = filterText(value);
  //   TextPainter painter = TextPainter(

  //       ///AUTO：华为手机如果不指定locale的时候，该方法算出来的文字高度是比系统计算偏小的。
  //       locale: Localizations.localeOf(GlobalStatic.context, nullOk: true),
  //       maxLines: maxLines,
  //       textDirection: TextDirection.ltr,
  //       text: TextSpan(
  //           text: value,
  //           style: TextStyle(
  //             fontWeight: fontWeight,
  //             fontSize: fontSize,
  //           )));
  //   painter.layout(maxWidth: maxWidth);

  //   ///文字的宽度:painter.width
  //   return painter.height;
  // }
}

// //获取组件在屏幕的坐标
// extension BuildContextExt on BuildContext {
//   /// 获取当前组件的 RenderBox
//   RenderBox? renderBox() {
//     return findRenderObject() is RenderBox ? (findRenderObject() as RenderBox) : null;
//   }

//   /// 获取当前组件的 position
//   Offset? position({Offset offset = Offset.zero}) {
//     return renderBox()?.localToGlobal(offset);
//   }
// }

// extension GlobalKeyExt on GlobalKey {
//   /// 获取当前组件的 RenderBox
//   RenderBox? renderBox() => currentContext?.renderBox();

//   /// 获取当前组件的 position
//   Offset? position({Offset offset = Offset.zero}) => currentContext?.position(offset: offset);

//   /// 获取当前组件的 Size
//   Size? get size => currentContext?.size;
// }
