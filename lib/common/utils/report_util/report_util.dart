import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/foundation.dart';

/// 上传服务器埋点信息
class ReportUtil {
  static reportEvents(
      {required String page, required String action, String? value}) {
    if (!UserService.to.isLogin || kDebugMode) {
      return;
    }
    CommonApis.operationReport(
        page: page, action: action, value: value, eventLogType: 'events');
  }

  static reportViews(
      {required String page, required String action, String? value}) {
    if (!UserService.to.isLogin || kDebugMode) {
      return;
    }
    CommonApis.operationReport(
        page: page, action: action, value: value, eventLogType: 'views');
  }

  static reportPurchase(
      {required String page, required String action, String? value}) {
    if (!UserService.to.isLogin || kDebugMode) {
      return;
    }
    CommonApis.operationReport(
        page: page, action: action, value: value, eventLogType: 'purchase');
  }

  ///page
  static const String chatwin = 'chatwin';
  static const String cards = 'cards';
  static const String amor = 'amor';
  static const String profile = 'profile';
  static const String setting = 'setting';
  static const String quests = 'quests';
  static const String notice = 'notice';
  static const String earningPop = 'earningPop';
  static const String shortgem = 'shortgem';
  static const String drop = 'drop';
  static const String cloneInfo = 'cloneInfo';
  static const String cloneExi = 'cloneExi';
  static const String cloneVT = 'cloneVT';
  static const String opening = 'opening';
  static const String kyc = 'kyc';
  static const String account = 'account';
  static const String inner = 'inner';
  static const String clone = 'clone';
  static const String cloneinfo = 'cloneinfo';
  static const String cloneFT = 'cloneFT';
  static const String cloneWrap = 'cloneWrap';
  static const String cloneMod = 'cloneMod';
  static const String hot = 'hot';
  static const String gem = 'gem';

  ///action
  static const String pay = 'pay';
  static const String unlock = 'unlock';
  static const String clickGem = 'clickGem';
  static const String like = 'like';
  static const String expand = 'expand';
  static const String play = 'play';
  static const String rate = 'rate';
  static const String select = 'select';
  static const String extra = 'extra';
  static const String idea = 'idea';
  static const String regen = 'regen';
  static const String reset = 'reset';
  static const String share = 'share';
  static const String actionCards = 'cards';
  static const String actionReport = 'report';
  static const String send = 'send';
  static const String questPop = 'questPop';
  static const String goHot = 'goHot';
  static const String cancel = 'cancel';
  static const String search = 'search';
  static const String actionChats = 'chats';
  static const String theme = 'theme';
  static const String collapse = 'collapse';
  static const String card = 'card';
  static const String chat = 'chat';
  static const String code = 'code';
  static const String actionQuests = 'quests';
  static const String email = 'email';
  static const String actionSetting = 'setting';
  static const String notif = 'notif';
  static const String actionDrop = 'drop';
  static const String actionSwitch = 'switch';
  static const String oneclick = 'oneclick';
  static const String hiptics = 'hiptics';
  static const String logout = 'logout';
  static const String deleteacct = 'deleteacct';
  static const String start = 'start';
  static const String claim = 'claim';
  static const String double = 'double';
  static const String click = 'click';
  static const String invite = 'invite';
  static const String addPhoto = 'addPhoto';
  static const String noPhoto = 'noPhoto';
  static const String refresh = 'refresh';
  static const String upload = 'upload';
  static const String record = 'record';
  static const String letsgo = 'letsgo';
  static const String actionClone = 'clone';
  static const String actionInner = 'inner';
  static const String actionProfile = 'profile';
  static const String view = 'view';
  static const String finish = 'finish';
  static const String quit = 'quit';
  static const String go = 'go';
  static const String restore = 'restore';
  static const String coupon = 'coupon';
  static const String subscribe = 'subscribe';
  static const String get = 'get';
}
