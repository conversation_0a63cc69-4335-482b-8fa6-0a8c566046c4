import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';

abstract class EntityPlus {
  static const String _dbName = "amor_data";
  static const int _newVersion = 1;
  static int _oldVersion = 0;
  static String? _dbBasePath;

  static Database? _database;

  ///表名称
  abstract String tableName;

  ///表是否存在
  bool exists = false;

  ///数据库实例化完成
  onReload(Database db, int version) {}

  ///创建表
  Future<void> onCreate(Database db, int version);

  ///更新表
  onUpgrade(Database db, int oldVersion, int newVersion) {}

  ///数据库降级
  onDowngrade(Database db, int oldVersion, int newVersion) {}

  EntityPlus() {
    _initDatabase();
  }

  ///创建数据库
  Future<Database> _initDatabase() async {
    _dbBasePath ??= "${await getDatabasesPath()}/$_dbName.db";
    _database ??= await openDatabase(
      _dbBasePath!,
      version: _newVersion,
      // onConfigure: (db) { },
      // onCreate: onCreate,
      onUpgrade: (db, old, newV) {
        _oldVersion = old;
      },
      onDowngrade: (db, old, newV) {
        _oldVersion = old;
      },
      // onOpen: onOpen,
    );

    onReload(_database!, _newVersion);

    //判断表是否存在
    exists = await tableExists();
    if (!exists) {
      await onCreate(_database!, _newVersion);
      exists = true;
    }

    if (_oldVersion != 0) {
      if (_oldVersion > _newVersion) {
        debugPrint("_oldVersion === $_oldVersion");
        debugPrint("_newVersion === $_newVersion");
        //数据库降级了
        await onDowngrade(
          _database!,
          await _database!.getVersion(),
          _newVersion,
        );
      } else if (_oldVersion < _newVersion) {
        debugPrint("_oldVersion === $_oldVersion");
        debugPrint("_newVersion === $_newVersion");
        //数据库升级了
        await onUpgrade(
          _database!,
          await _database!.getVersion(),
          _newVersion,
        );
      }
    }

    return _database!;
  }

  ///表是否存在
  Future<bool> tableExists() async {
    //内建表sqlite_master
    var res = await _database!.rawQuery(
      "SELECT * FROM sqlite_master WHERE TYPE = 'table' AND NAME = '$tableName'",
    );
    return res.isNotEmpty;
  }

  ///表列是否存在
  Future<bool> columnExists(String columnName) async {
    var result = await _database!.rawQuery("""
      SELECT sql FROM sqlite_master WHERE type='table' AND name='$tableName' COLLATE NOCASE limit 1
    """);
    String sql = result[0]["sql"] as String;
    int startIndex = sql.indexOf("(") + 1;
    int endIndex = sql.indexOf(")");
    sql = sql.substring(startIndex, endIndex);

    List<String> sqlList = sql.split(",").map((e) => e.trim()).toList();
    bool exists = false;
    for (int j = 0; j < sqlList.length; j++) {
      var rowStr = sqlList[j].trim().split(",").join("");
      var colName = rowStr.split(" ")[0].trim();
      if (colName == columnName) {
        exists = true;
        break;
      }
    }
    return exists;
  }

  ///新增列
  Future addColumn(String columnName, String type) async {
    return await _database!.rawQuery("""
      ALTER TABLE $tableName ADD  $columnName $type
    """);
  }

  ///删表
  dropTable() async {
    if (_database == null) {
      await _initDatabase();
    }
    await _database!.execute("""
      drop table if exists $tableName;
    """);
  }

  Database get database => _database!;

  ///插入数据
  insert(Map<String, Object?> values) async {
    debugPrint('插入数据');
    // debugPrint(jsonEncode(values));
    return database.insert(tableName, values);
  }

  ///删除数据
  remove(int? id) async {
    debugPrint('删除数据');
    if (id == null) {
      return database.delete(
        tableName,
      );
    }
    return database.delete(
      tableName,
      where: "id = ?",
      whereArgs: [id],
    );
  }

  ///修改数据
  Future<int> update(Map<String, Object?> json, int id) async {
    // List<String> keys = json1.keys.toList();
    // List<String> where = [];
    // for (int i = 0; i < keys.length; i++) {
    //   String key = keys[i];
    //   if (json1[key].runtimeType == String) {
    //     where.add("$key='${json1[key]}'");
    //   } else {
    //     where.add("$key=${json1[key]}");
    //   }
    // }
    debugPrint('更新数据');
    int count = await database.update(
      tableName,
      json,
      where: "id = ?",
      whereArgs: [id],
    );
    return count;
  }

  ///查找数据
  Future<List<Map<String, Object?>>> find({
    Map<String, dynamic>? where,
    int? page,
    int? pageSize,
  }) async {
    debugPrint('查询数据');
    // print(where);
    List<String> keys = where?.keys.toList() ?? [];
    List<String> whereList = [];
    for (int i = 0; i < keys.length; i++) {
      String key = keys[i];
      if (where![key].runtimeType == String) {
        whereList.add("$key='${where[key]}'");
      } else {
        whereList.add("$key=${where[key]}");
      }
    }
    String sql = whereList.join(" and ");
    var result = await database.query(
      tableName,
      where: sql.isEmpty ? null : sql,
      offset: page == null ? null : (page - 1) * (pageSize ?? 1),
      limit: pageSize,
    );

    return result;
  }

  Future<List<Map<String, dynamic>>> fetchData() async {
    return database.query(tableName);
  }

  rawQuery(String sql) async {
    return database.rawQuery(sql);
  }
}
