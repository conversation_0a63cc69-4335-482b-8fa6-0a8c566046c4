import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:sqflite/sqflite.dart';

import 'audio_entity.dart';
import 'session_entity.dart';
import 'http_entity.dart';

class AppSqlite {
  static forFeature() async {
    var list = [
      HttpEntity.instan(),
      AudioEntity.instan(),
      SessionEntity.instan(),
    ];
    for (int i = 0; i < list.length; i++) {
      var entity = list[i];
      //exists 字段,判断表是否创建完成
      while (!entity.exists) {
        //等待数据表创建完成
        await Future.delayed(const Duration(milliseconds: 60), () {});
      }
    }
  }

  static Future<int> getDatabaseSize() async {
    final dbPath = await databaseFactory.getDatabasesPath();
    final databaseFilePath = path.join(dbPath, 'amor_data.db');
    final File dbFile = File(databaseFilePath);
    if (await dbFile.exists()) {
      final fileStat = await dbFile.stat();
      return fileStat.size;
    }
    return 0;
  }
}
