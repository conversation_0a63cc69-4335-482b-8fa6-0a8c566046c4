import 'package:flutter/material.dart';

import 'db_util.dart';

class HttpEntity extends EntityPlus {
  static HttpEntity? _ins;

  HttpEntity._();

  static HttpEntity instan() {
    if (_ins == null) {
      debugPrint("HttpEntity实例化");
    }
    return _ins ??= HttpEntity._();
  }

  @override
  String tableName = "http_request_data";
  //建表函数,当数据库中没有这个表时,基类会触发这个函数
  @override
  onCreate(db, version) async {
    debugPrint("创建 $tableName 数据表");
    await db.execute("""
      CREATE TABLE $tableName (
        id integer primary key autoincrement,
        response TEXT,
        param TEXT,
        url TEXT
      )
    """);
  }

  ///当数据库升级时,基类会触发的函数
  @override
  onUpgrade(db, oldVersion, newVersion) {}

  ///当数据库降级,基类会触发的函数
  @override
  onDowngrade(db, oldVersion, newVersion) {}
}
