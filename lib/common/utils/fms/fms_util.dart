import 'dart:async';
import 'dart:convert';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/local_notification/local_notification.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class FirebaseMessagingService {
  static bool initialized = false;
  Future<void> init() async {
    initialized = true;
    NotificationSettings notiSet = await FirebaseMessaging.instance.requestPermission();
    if (notiSet.authorizationStatus != AuthorizationStatus.authorized) {
      return;
    }
    await setupFlutterNotifications();
    String? token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      await Get.find<SPService>().set(spPushToken, token);
      submitPushToken();
      // print('push token====$token');
    }
    FirebaseMessaging.onMessage.listen(_showFlutterNotification);
    //应用在打开之前处于后台状态
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('后台状态收到通知打开APP: ${message.data}');
      Map<String, dynamic> params = {};
      if (message.data['content'] != null) {
        params = jsonDecode(message.data['content']);
      }
      if (params['sessionNo'] != null) {
        Future.delayed(const Duration(milliseconds: 500), () {
          //当前正在聊天
          if (Get.currentRoute == Routes.session) {
            //判断当前会话是否是收到通知给的会话
            if (SessionPageController.to.state.sessionNo == params['sessionNo']) {
              //是当前会话 震动提示/刷新聊天记录列表
              HapticFeedback.mediumImpact();
              SessionPageController.to.state.page = 1;
              SessionPageController.to.getmessageList(useCache: false);
            } else {
              //非当前会话 替换
              SessionPageController.to.replaceSession(sessionNo: params['sessionNo']);
            }
            return;
          }
          Get.offNamedUntil(Routes.session, (route) => route.settings.name == Routes.tabs,
              arguments: params['sessionNo']);
        });
      }
    });
    //应用在打开之前处于终止状态
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      debugPrint('终止状态收到通知打开APP: ${initialMessage.data}');
      Map<String, dynamic> params = {};
      if (initialMessage.data['content'] != null) {
        params = jsonDecode(initialMessage.data['content']);
      }
      if (params['sessionNo'] != null) {
        //先保存会话ID，进入首页时再跳转
        AppService.sp.set(spNotificationSessionNo, params['sessionNo']);
      }
    }
    // FirebaseMessaging.instance.subscribeToTopic('common');
  }

  static submitPushToken() async {
    if (UserService.to.isLogin == true) {
      bool result = await CommonApis.submitPushToken();
      debugPrint('cid上报: $result');
    }
  }

  static setOnBackgroundMessage(BackgroundMessageHandler handler) {
    FirebaseMessaging.onBackgroundMessage(handler);
  }

  Future<void> setupFlutterNotifications() async {
    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  //展示应用内通知
  Future<void> _showFlutterNotification(RemoteMessage message) async {
    debugPrint('展示应用内通知');
    Map<String, dynamic> params = {};
    if (message.data['content'] != null) {
      params = jsonDecode(message.data['content']);
    }
    if (params['sessionNo'] == null) {
      return;
    }
    //当前正在聊天
    if (Get.currentRoute == Routes.session) {
      //判断当前会话是否是收到通知给的会话
      if (SessionPageController.to.state.sessionNo == params['sessionNo']) {
        //是当前会话 震动提示/刷新聊天记录列表
        HapticFeedback.mediumImpact();
        SessionPageController.to.state.page = 1;
        SessionPageController.to.getmessageList(useCache: false);
        return;
      }
    }
    //有图片的情况 需要先缓存图片
    if (params['image'] != null) {
      await DefaultCacheManager().getSingleFile(params['image']);
    }
    //其他情况弹自定义通知
    LocalNotification.showNotification(
      title: params['title'] ?? '',
      content: params['body'] ?? '',
      image: params['image'],
      sessionNo: params['sessionNo'],
    );

    // final notification = message.notification;
    // final content = message.data["alert"];
    // String? largeIconPath;
    // if (content["icon"] != null) {
    //   // largeIconPath = await _downloadAndSaveFile(content["icon"]!, 'largeIcon');
    // }
  }
}
