import 'dart:io';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_oss_aliyun/flutter_oss_aliyun.dart';

class UploadOss {
  /// properties
  static CancelToken? cancelToken;

  static Future<String?> upload(
      {String? path,
      required String format,
      String? fileName,
      String servicePath = 'clone', //默认
      //回调文件路径
      bool callBackFilePath = false,
      Uint8List? fileBytes,
      final ProgressCallback? onSendProgress}) async {
    cancelToken = CancelToken();
    if (path != null) {
      File file = File(path);
      fileBytes = await file.readAsBytes();
    }
    debugPrint('上传文件大小: ${FileUtil.formatBytes(fileBytes!.length)}');
    String baseUrl = Api.baseUrl;
    Client.init(
        stsUrl: '$baseUrl/amor/common/ali-oss', //
        ossEndpoint: AppService.configModel.endpoint ?? defaultOssEndpoint,
        bucketName: AppService.configModel.bucket ?? defaultOssBucket,
        dio: Dio(
          BaseOptions(headers: {'token': UserService.to.token}),
        ));
    DateTime today = DateTime.now();
    fileName ??=
        '$servicePath/${today.year.toString()}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}/${CommonUtil.randomLetters(10)}${DateTime.now().millisecondsSinceEpoch}.$format';
    try {
      Response response = await Client().putObject(fileBytes, fileName,
          cancelToken: cancelToken, option: PutRequestOption(onSendProgress: onSendProgress));
      if (response.statusCode == 200) {
        debugPrint('上传成功：$fileName');
        if (callBackFilePath && path != null) {
          return '${path}callBackFilePath/$fileName';
        }
        return '/$fileName';
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  //取消上传
  static cancelUpload() {
    cancelToken?.cancel("cancel the uploading.");
  }
}
