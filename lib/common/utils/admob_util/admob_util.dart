// import 'package:amor_app/common/utils/utils.dart';
// import 'package:amor_app/common/widgets/widgets.dart';
// import 'package:get/get.dart';
// import 'package:google_mobile_ads/google_mobile_ads.dart';

// class AdMobUtil {
//   static final AdMobUtil _adMobUtil = AdMobUtil._internal();

//   factory AdMobUtil() {
//     return _adMobUtil;
//   }
//   AdMobUtil._internal();
//   //插页激励视频
//   final androidAdUnitId = 'ca-app-pub-5813568089245528/3414290304';
//   final iosAdUnitId = 'ca-app-pub-5813568089245528/7130174037';
//   String? adLoadIp;
//   String? adShowIp;
//   RewardedInterstitialAd? _rewardeInterstitialdAd;
//   bool earnedReward = false;

//   init() async {
//     await MobileAds.instance.initialize();
//   }

//   playAd(Function(bool result) earnedRewardCallBack) async {
//     Loading.show();
//     String? adUnitId = AppService.configModel.adId;
//     if (adUnitId == null || adUnitId.isEmpty) {
//       adUnitId = GetPlatform.isIOS ? iosAdUnitId : androidAdUnitId;
//     }
//     //测试ID
//     // adUnitId = 'ca-app-pub-3940256099942544/5354046379';
//     //测试设备
//     // await MobileAds.instance.updateRequestConfiguration(
//     //     RequestConfiguration(testDeviceIds: ['74B53EA1483348F5C2B46A46ADF81978']));
//     // adLoadIp = await IpAddress.getIp();
//     earnedReward = false;
//     RewardedInterstitialAd.load(
//         adUnitId: adUnitId,
//         request: const AdRequest(),
//         rewardedInterstitialAdLoadCallback: RewardedInterstitialAdLoadCallback(
//           // Called when an ad is successfully received.
//           onAdLoaded: (ad) {
//             ad.fullScreenContentCallback = FullScreenContentCallback(
//               // Called when the ad showed the full screen content.
//               onAdShowedFullScreenContent: (ad) {},

//               // Called when an impression occurs on the ad.
//               onAdImpression: (ad) {},
//               // Called when the ad failed to show full screen content.
//               onAdFailedToShowFullScreenContent: (ad, err) {
//                 // Dispose the ad here to free resources.
//                 ad.dispose();
//               },
//               // Called when the ad dismissed full screen content.
//               onAdDismissedFullScreenContent: (ad) {
//                 // Dispose the ad here to free resources.
//                 ad.dispose();

//                 earnedRewardCallBack(earnedReward);
//               },
//               // Called when a click is recorded for an ad.
//               onAdClicked: (ad) {},
//             );

//             ad.onPaidEvent = (ad, valueMicros, precision, currencyCode) {
//               AdjustUtil.adjustADEvent(
//                   value: '$valueMicros',
//                   currencyCode: currencyCode,
//                   adNetwork: (ad as RewardedInterstitialAd)
//                       .responseInfo
//                       ?.loadedAdapterResponseInfo
//                       ?.adSourceName);
//               TbaUtil().adEvent(
//                   adEcpm: valueMicros,
//                   currency: currencyCode,
//                   precisionType: precision,
//                   adCode: adUnitId ?? '',
//                   adLoadIp: adLoadIp,
//                   adShowIp: adShowIp,
//                   adNetwork: (ad)
//                       .responseInfo
//                       ?.loadedAdapterResponseInfo
//                       ?.adSourceName);
//             } as OnPaidEventCallback;
//             _rewardeInterstitialdAd = ad;
//             Loading.dismiss();
//             showAd();
//           },
//           // Called when an ad request failed.
//           onAdFailedToLoad: (LoadAdError error) {
//             // print(error.toString());
//             Loading.dismiss();
//             earnedRewardCallBack(false);
//           },
//         ));
//   }

//   showAd() async {
//     // adShowIp = await IpAddress.getIp();
//     _rewardeInterstitialdAd?.show(
//         onUserEarnedReward: (AdWithoutView ad, RewardItem rewardItem) {
//       earnedReward = true;
//     });
//   }
// }
