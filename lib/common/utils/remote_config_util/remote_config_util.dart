import 'package:amor_app/common/utils/facebook/fb_util.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

class RemoteConfigUtil {
  static late FirebaseRemoteConfig remoteConfig;

  static init() async {
    remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(hours: 12),
      minimumFetchInterval: const Duration(hours: 12),
    ));
    //设置默认值
    await remoteConfig.setDefaults(const {undressShowCount: 5, callGems: 300, createImgGems: 8, createVideoGems: 10});
    //监听
    remoteConfig.onConfigUpdated.listen(
      (event) async {
        await remoteConfig.activate();
      },
    );
    //获取值
    try {
      await remoteConfig.fetchAndActivate();

      // 使用封装的方法从远程配置初始化Facebook SDK
      await FBUtil.initializeWithRemoteConfig();
    } catch (e) {
      print('远程配置初始化Facebook SDK失败: $e');
    }
  }

  static T getConfig<T>({required String key}) {
    dynamic value;
    if (T == bool) {
      value = remoteConfig.getBool(key);
    }
    if (T == String) {
      value = remoteConfig.getString(key);
    }
    if (T == double) {
      value = remoteConfig.getDouble(key);
    }
    if (T == int) {
      value = remoteConfig.getInt(key);
    }
    // print('remoteConfig:\nkey:$key\ntype:$T\nvalue:$value');
    return value;
  }
}
