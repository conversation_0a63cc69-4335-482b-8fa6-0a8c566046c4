// import 'dart:io';
// import 'package:amor_app/common/utils/utils.dart';
// import 'package:amor_app/common/values/values.dart';
// import 'package:amor_app/common/widgets/widgets.dart';
// import 'package:amor_app/pages/splash/controller.dart';
// import 'package:applovin_max/applovin_max.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// class ApplovinUtil {
//   static String sdkKey =
//       "ylLUNgWLMpXco3LeAqOSET53m0QfEtSk1mMHhBilrsHLW7mwwFeBPiJ9tEBm4XvMKsTHRZvuInT_cmwVZdMoXG";
//   //克隆收益
//   static String interstitialAdUnitIdClone =
//       Platform.isAndroid ? "c1e35a89418f73df" : "IOS_INTER_AD_UNIT_ID";
//   //签到双倍
//   static String interstitialAdUnitIdSign =
//       Platform.isAndroid ? "c1e35a89418f73df" : "IOS_INTER_AD_UNIT_ID";
//   //其它任务双倍
//   static String interstitialAdUnitIdDouble =
//       Platform.isAndroid ? "c1e35a89418f73df" : "IOS_INTER_AD_UNIT_ID";
//   //角色解锁
//   static String rewardedAdUnitIdUnlock =
//       Platform.isAndroid ? "300263fd6fa2a952" : "IOS_REWARDED_AD_UNIT_ID";
//   //第1次广告 任务
//   static String rewardedAdUnitIdTask1 =
//       Platform.isAndroid ? "c314a7fff153222f" : "IOS_REWARDED_AD_UNIT_ID";
//   //第2-3次广告 任务
//   static String rewardedAdUnitIdTask2_3 =
//       Platform.isAndroid ? "16b4b6f399fd5dd5" : "IOS_REWARDED_AD_UNIT_ID";
//   //第>4次广告 任务
//   static String rewardedAdUnitIdTask4 =
//       Platform.isAndroid ? "154d4d946b533096" : "IOS_REWARDED_AD_UNIT_ID";
//   //克隆角色刷新
//   static String rewardedAdUnitIdCloneRefresh =
//       Platform.isAndroid ? "512cda4008dc4036" : "IOS_REWARDED_AD_UNIT_ID";
//   //开屏
//   static String appOpenAdUnitId = Platform.isAndroid ? "ad95827a575fe1b4" : "IOS_BANNER_AD_UNIT_ID";
//   //是否初始化
//   static bool isInitialized = false;
//   //全屏广告状态
//   static var interstitialLoadState = AdLoadState.adLoadNotAttempted;
//   //全屏广告重试次数
//   static var interstitialRetryAttempt = 0;
//   //视频广告状态
//   static var rewardedAdLoadState = AdLoadState.adLoadNotAttempted;
//   //视频广告重试次数
//   static var rewardedAdRetryAttempt = 0;
//   //最多重试次数
//   static int maxExponentialRetryCount = 2;
//   //当前使用的全屏广告ID
//   static String curInterstitialAdUnitId = "";
//   //当前使用的视频广告ID
//   static String curRewardedAdUnitId = "";
//   //广告关闭回调
//   static Function(bool success)? onAdCompleteCallback;
//   //广告展示回调
//   static Function()? onAdDisplayedCallback;
//   //使用视频广告解锁角色
//   static int? modelId;
//   //刷新克隆的角色NO
//   static String? modelNo;
//   //广告任务的ID
//   static int? taskId;
//   //是否超时
//   static bool isTimeout = false;
//   //超时时间
//   static int timeoutNum = 10;
//   //加载失败提示文字
//   static String loadFailedTipStr = 'Load failed';
//   //初始化
//   static init() async {
//     AppLovinMAX.setDoNotSell(false);
//     AppLovinMAX.setHasUserConsent(true);
//     AppLovinMAX.setPrivacyPolicyUrl(AppService.configModel.userAgreement ?? (defaultUserAgreement));
//     AppLovinMAX.setTermsOfServiceUrl(AppService.configModel.privacyPolicy ?? defaultPrivacyPolicy);
//     //启用MAX条款和隐私策略流。
//     // AppLovinMAX.setTermsAndPrivacyPolicyFlowEnabled(true);
//     //启用详细日志记录
//     // AppLovinMAX.setVerboseLogging(true);
//     //选择性初始化
//     AppLovinMAX.setInitializationAdUnitIds([
//       interstitialAdUnitIdClone,
//       interstitialAdUnitIdSign,
//       interstitialAdUnitIdDouble,
//       rewardedAdUnitIdUnlock,
//       rewardedAdUnitIdTask1,
//       rewardedAdUnitIdTask2_3,
//       rewardedAdUnitIdTask4,
//       rewardedAdUnitIdCloneRefresh,
//       appOpenAdUnitId
//     ]);
//     MaxConfiguration? configuration = await AppLovinMAX.initialize(sdkKey);
//     if (configuration != null) {
//       isInitialized = true;
//       attachAdListeners();
//       if (Get.isRegistered<AnimatedSplashPageController>()) {
//         AnimatedSplashPageController.to.showAppOpenAd();
//       }
//     }
//   }

//   static logStatus(String text) {
//     debugPrint('AD LOG: $text');
//   }

//   //加载全屏广告
//   static loadInterstitialAd(
//       {required String adUnitId,
//       required Function(bool success) callBack,
//       Function()? displayCallBack}) async {
//     if (isInitialized != true) {
//       return;
//     }
//     Loading.show();
//     interstitialRetryAttempt = 0;
//     curInterstitialAdUnitId = adUnitId;
//     onAdCompleteCallback = callBack;
//     onAdDisplayedCallback = displayCallBack;
//     AppLovinMAX.loadInterstitial(curInterstitialAdUnitId);
//   }

//   //展示全屏广告
//   static showInterstitialAd(String adUnitId) async {
//     bool? isReady = await AppLovinMAX.isInterstitialReady(adUnitId);
//     if (isReady == true) {
//       AppLovinMAX.showInterstitial(adUnitId);
//     } else {
//       Loading.dismiss();
//     }
//   }

//   //加载视频广告
//   static loadRewardedAd(
//       {required String adUnitId,
//       int? unlockModelId,
//       int? adTaskId,
//       String? refreshModelNo,
//       required Function(bool success) callBack,
//       Function()? displayCallBack}) async {
//     if (isInitialized != true) {
//       return;
//     }
//     modelId = unlockModelId;
//     taskId = adTaskId;
//     modelNo = refreshModelNo;
//     rewardedAdRetryAttempt = 0;
//     Loading.show();
//     curRewardedAdUnitId = adUnitId;
//     onAdCompleteCallback = callBack;
//     onAdDisplayedCallback = displayCallBack;
//     AppLovinMAX.loadRewardedAd(curRewardedAdUnitId);
//   }

//   //展示视频广告
//   static showRewardedAd(String adUnitId) async {
//     if (UserService.to.isLogin) {
//       AppLovinMAX.setUserId(UserService.to.shareCode ?? '');
//     }
//     bool? isReady = await AppLovinMAX.isRewardedAdReady(adUnitId);
//     if (isReady == true) {
//       AppLovinMAX.showRewardedAd(adUnitId,
//           customData: 'modelId=${modelId ?? ''};taskId=${taskId ?? ''};modelNo=${modelNo ?? ''}');
//     } else {
//       Loading.dismiss();
//     }
//   }

//   //加载开屏广告
//   static loadAppOpenAd() async {
//     if (isInitialized != true) {
//       onAdCompleteCallback!.call(false);
//       return;
//     }
//     AppLovinMAX.loadAppOpenAd(appOpenAdUnitId);
//   }

//   //判断开屏广告是否加载完成
//   static appOpenAdIsReady(
//       {required Function() displayCallBack, required Function(bool success) callBack}) async {
//     onAdCompleteCallback = callBack;
//     onAdDisplayedCallback = displayCallBack;
//     bool isReady = await AppLovinMAX.isAppOpenAdReady(appOpenAdUnitId) ?? false;
//     if (isReady == false) {
//       loadAppOpenAd();
//     } else {
//       showAppOpenAd();
//     }
//   }

//   //展示开屏广告
//   static showAppOpenAd() async {
//     if (Get.isRegistered<AnimatedSplashPageController>() &&
//         AnimatedSplashPageController.to.adLoadTimeOut == false) {
//       AppLovinMAX.showAppOpenAd(appOpenAdUnitId);
//     }
//   }

//   //监听
//   static attachAdListeners() {
//     /// Interstitial Ad Listeners
//     AppLovinMAX.setInterstitialListener(
//       InterstitialListener(
//         onAdLoadedCallback: (ad) {
//           interstitialLoadState = AdLoadState.adLoaded;
//           logStatus('Interstitial ad loaded from ${ad.networkName}');
//           interstitialRetryAttempt = 0;
//           showInterstitialAd(curInterstitialAdUnitId);
//         },
//         onAdLoadFailedCallback: (adUnitId, error) {
//           interstitialLoadState = AdLoadState.adFailedToLoad;
//           logStatus('Interstitial ad failed to load with code ${error.code}');
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//           Loading.toast(loadFailedTipStr);

//           /*
//           interstitialRetryAttempt = interstitialRetryAttempt + 1;
//           if (interstitialRetryAttempt > maxExponentialRetryCount) {
//             logStatus('Interstitial ad failed to load with code ${error.code}');
//             if (onAdCompleteCallback != null) {
//               onAdCompleteCallback!.call(false);
//             }
//             Loading.dismiss();
//             return;
//           }
//           int retryDelay = pow(2, min(maxExponentialRetryCount, interstitialRetryAttempt)).toInt();
//           logStatus(
//               'Interstitial ad failed to load with code ${error.code} - retrying in ${retryDelay}s');
//           Future.delayed(Duration(milliseconds: retryDelay * 1000), () {
//             interstitialLoadState = AdLoadState.adFailedToLoad;
//             logStatus('Interstitial ad retrying to load...');
//             AppLovinMAX.loadInterstitial(curInterstitialAdUnitId);
//           });
//           */
//         },
//         onAdDisplayedCallback: (ad) {
//           logStatus('Interstitial ad displayed');
//           if (onAdDisplayedCallback != null) {
//             onAdDisplayedCallback!.call();
//           }
//           Loading.dismiss();
//         },
//         onAdDisplayFailedCallback: (ad, error) {
//           interstitialLoadState = AdLoadState.adFailedToLoad;
//           logStatus(
//               'Interstitial ad failed to display with code ${error.code} and message ${error.message}');
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//           Loading.toast(loadFailedTipStr);
//         },
//         onAdClickedCallback: (ad) {
//           logStatus('Interstitial ad clicked');
//         },
//         onAdHiddenCallback: (ad) {
//           interstitialLoadState = AdLoadState.adFailedToLoad;
//           logStatus('Interstitial ad hidden');
//           Loading.dismiss();
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(true);
//           }
//         },
//         //收入回调
//         onAdRevenuePaidCallback: (ad) {
//           logStatus('Interstitial ad revenue paid: ${ad.revenue}');
//           AdjustUtil.adjustADEvent(value: ad.revenue);
//         },
//       ),
//     );

//     /// Rewarded Ad Listeners
//     AppLovinMAX.setRewardedAdListener(
//       RewardedAdListener(
//         onAdLoadedCallback: (ad) {
//           rewardedAdLoadState = AdLoadState.adLoaded;
//           // Rewarded ad is ready to be shown. AppLovinMAX.isRewardedAdReady(rewarded_ad_unit_id) will now return 'true'
//           logStatus('Rewarded ad loaded from ${ad.networkName}');
//           // Reset retry attempt
//           rewardedAdRetryAttempt = 0;
//           showRewardedAd(curRewardedAdUnitId);
//         },
//         onAdLoadFailedCallback: (adUnitId, error) {
//           rewardedAdLoadState = AdLoadState.adFailedToLoad;
//           logStatus('Rewarded ad failed to load with code ${error.code}');
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//           Loading.toast(loadFailedTipStr);
//           // Rewarded ad failed to load
//           // We recommend retrying with exponentially higher delays up to a maximum delay (in this case 64 seconds)
//           /*
//           rewardedAdRetryAttempt = rewardedAdRetryAttempt + 1;
//           if (rewardedAdRetryAttempt > maxExponentialRetryCount) {
//             logStatus('Rewarded ad failed to load with code ${error.code}');
//             if (onAdCompleteCallback != null) {
//               onAdCompleteCallback!.call(false);
//             }
//             Loading.dismiss();
//             return;
//           }
//           int retryDelay = pow(2, min(maxExponentialRetryCount, rewardedAdRetryAttempt)).toInt();
//           logStatus(
//               'Rewarded ad failed to load with code ${error.code} - retrying in ${retryDelay}s');

//           Future.delayed(Duration(milliseconds: retryDelay * 1000), () {
//             rewardedAdLoadState = AdLoadState.adFailedToLoad;
//             logStatus('Rewarded ad retrying to load...');
//             AppLovinMAX.loadRewardedAd(curRewardedAdUnitId);
//           });
//           */
//         },
//         onAdDisplayedCallback: (ad) {
//           logStatus('Rewarded ad displayed');
//           if (onAdDisplayedCallback != null) {
//             onAdDisplayedCallback!.call();
//           }
//           Loading.dismiss();
//         },
//         onAdDisplayFailedCallback: (ad, error) {
//           rewardedAdLoadState = AdLoadState.adFailedToLoad;
//           logStatus(
//               'Rewarded ad failed to display with code ${error.code} and message ${error.message}');
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//           Loading.toast(loadFailedTipStr);
//         },
//         onAdClickedCallback: (ad) {
//           logStatus('Rewarded ad clicked');
//         },
//         onAdHiddenCallback: (ad) {
//           rewardedAdLoadState = AdLoadState.adFailedToLoad;
//           logStatus('Rewarded ad hidden');
//           Loading.dismiss();
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(true);
//           }
//         },
//         onAdReceivedRewardCallback: (ad, reward) {
//           logStatus('Rewarded ad granted reward');
//         },
//         //收入回调
//         onAdRevenuePaidCallback: (ad) {
//           logStatus('Rewarded ad revenue paid: ${ad.revenue}');
//           AdjustUtil.adjustADEvent(value: ad.revenue);
//         },
//       ),
//     );

//     /// AppOpen Ad Listeners
//     AppLovinMAX.setAppOpenAdListener(
//       AppOpenAdListener(
//         onAdLoadedCallback: (ad) {
//           showAppOpenAd();
//         },
//         onAdLoadFailedCallback: (adUnitId, error) {
//           logStatus('appopen ad failed to load with code ${error.toString()}');
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//         },
//         onAdDisplayedCallback: (ad) {
//           if (onAdDisplayedCallback != null) {
//             onAdDisplayedCallback!.call();
//           }
//         },
//         onAdDisplayFailedCallback: (ad, error) {
//           // loadAppOpenAd();
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(false);
//           }
//         },
//         onAdClickedCallback: (ad) {},
//         onAdHiddenCallback: (ad) {
//           // loadAppOpenAd();.
//           if (onAdCompleteCallback != null) {
//             onAdCompleteCallback!.call(true);
//           }
//         },
//         onAdRevenuePaidCallback: (ad) {
//           AdjustUtil.adjustADEvent(value: ad.revenue);
//         },
//       ),
//     );
//   }
// }
