import 'dart:async';

import 'package:amor_app/common/utils/utils.dart';
import 'package:flutter/foundation.dart';
import 'package:record/record.dart';

class RecordUtil {
  AudioRecorder? audioRecorder;
  StreamSubscription<RecordState>? recordSub;
  RecordState recordState = RecordState.stop;
  init({required Function(RecordState state) onStateChanged}) {
    audioRecorder = AudioRecorder();
    //监听录音状态
    recordSub = audioRecorder?.onStateChanged().listen((recordState) {
      _updateRecordState(recordState);
      onStateChanged.call(recordState);
    });
  }

  Future<void> start() async {
    try {
      if (await audioRecorder!.hasPermission()) {
        const encoder = AudioEncoder.aacLc;
        if (!await _isEncoderSupported(encoder)) {
          return;
        }
        final devs = await audioRecorder?.listInputDevices();
        debugPrint(devs.toString());
        const config = RecordConfig(encoder: encoder, numChannels: 1);
        String tempDirPath = await FileUtil.getTempDirPath();
        String tempPath;
        tempPath = "$tempDirPath/${CommonUtil.randomLetters(20)}.m4a";
        // Record to file
        await audioRecorder?.start(config, path: tempPath);
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<String?> stop() async {
    final path = await audioRecorder?.stop();
    return path;
  }

  dispose() {
    recordSub?.cancel();
    audioRecorder?.dispose();
  }

  void _updateRecordState(RecordState state) {
    recordState = state;
    /*
  
    switch (recordState) {
      case RecordState.pause:
        _timer?.cancel();
        break;
      case RecordState.record:
        _startTimer();
        break;
      case RecordState.stop:
        _timer?.cancel();
        _recordDuration = 0;
        break;
    }
    */
  }

  Future<bool> _isEncoderSupported(AudioEncoder encoder) async {
    final isSupported = await audioRecorder!.isEncoderSupported(
      encoder,
    );

    if (isSupported == false) {
      debugPrint('${encoder.name} is not supported on this platform.');
      debugPrint('Supported encoders are:');

      for (final e in AudioEncoder.values) {
        if (await audioRecorder!.isEncoderSupported(e)) {
          debugPrint('- ${encoder.name}');
        }
      }
    }

    return isSupported;
  }
}
