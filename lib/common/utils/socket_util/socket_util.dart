import 'dart:async';

import 'package:flutter/material.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// 连接状态枚举
enum ConnectStatusEnum {
  //已连接
  connect,
  //连接中
  connecting,
  //已关闭
  close,
  //关闭中
  closing
}

/// 接收到消息后的回调
typedef ListenMessageCallback = void Function(String msg);

/// 错误回调
typedef ErrorCallback = void Function(Exception error);

/// WebSocket管理类
class WebSocketManager {
  /// 连接状态，默认为关闭
  ConnectStatusEnum _connectStatus = ConnectStatusEnum.close;

  /// WebSocket通道
  WebSocketChannel? _webSocketChannel;

  /// WebSocket通道的流
  Stream<dynamic>? webSocketChannelStream;

  ListenMessageCallback? recivedCallback;
  //重复心跳
  Timer? timer;
  //心跳间隔
  final int intervalSeconds = 5;
  // 重新连接最大次数
  final int maxConnectTime = 30;
  // 当前重新连接的次数
  int currentConnectTime = 0;
  String socketUrl = '';

  /// 发起连接，Url实例："ws://echo.websocket.org";
  Future<bool> connect(String url, {required ListenMessageCallback messageCallback}) async {
    recivedCallback = messageCallback;
    socketUrl = url;
    debugPrint('socket连接:$socketUrl');
    await disconnect();
    if (_connectStatus == ConnectStatusEnum.connect) {
      //已连接，不需要处理
      return false;
    } else if (_connectStatus == ConnectStatusEnum.close) {
      //未连接，发起连接
      _connectStatus = ConnectStatusEnum.connecting;
      var connectUrl = Uri.parse(url);
      _webSocketChannel = IOWebSocketChannel.connect(connectUrl);
      webSocketChannelStream = _webSocketChannel!.stream;
      listen(recivedCallback!);
      _connectStatus = ConnectStatusEnum.connect;
      startCountdownTimer();
      debugPrint('socket 连接');
      return true;
    } else {
      return false;
    }
  }

  /// 关闭连接
  Future disconnect() async {
    if (_connectStatus == ConnectStatusEnum.connect) {
      _connectStatus = ConnectStatusEnum.closing;
      await _webSocketChannel?.sink.close(status.normalClosure, "normalClosure");
      _connectStatus = ConnectStatusEnum.close;
    }
  }

  /// 重连
  void reconnect(String url) async {
    currentConnectTime++;
    debugPrint("socket 重连 第$currentConnectTime次");
    if (currentConnectTime > maxConnectTime) {
      //关闭心跳定时器
      if (timer != null) {
        timer?.cancel();
        timer = null;
      }
      return;
    }
    if (currentConnectTime <= maxConnectTime && _connectStatus != ConnectStatusEnum.connect) {
      await disconnect();
      await connect(url, messageCallback: recivedCallback!);
    }
  }

  /// 监听消息
  void listen(ListenMessageCallback messageCallback, {ErrorCallback? onError}) {
    webSocketChannelStream!.listen(
      (message) {
        currentConnectTime = 0;
        messageCallback.call(message);
      },
      onError: (error) {
        //连接异常
        debugPrint('socket 连接错误  onError');
        _connectStatus = ConnectStatusEnum.close;
        if (onError != null) {
          onError.call(error);
        }
      },
      onDone: () {
        //连接异常
        debugPrint('socket 连接关闭');
        _connectStatus = ConnectStatusEnum.close;
      },
    );
  }

  /// 发送消息
  bool sendMsg(String text) {
    if (_connectStatus == ConnectStatusEnum.connect) {
      _webSocketChannel?.sink.add(text);
      return true;
    }
    return false;
  }

  ///开启心跳
  void startCountdownTimer() {
    debugPrint('开启心跳');
    timer ??= Timer.periodic(Duration(seconds: intervalSeconds), (timer) {
      if (sendMsg('heartbeat') == false) {
        //心跳发送失败后重连
        reconnect(
            socketUrl.replaceAll(RegExp(r'connectionType=start'), 'connectionType=reconnect'));
      }
    });
  }

  /// 获取当前连接状态
  ConnectStatusEnum getCurrentStatus() {
    if (_connectStatus == ConnectStatusEnum.connect) {
      return ConnectStatusEnum.connect;
    } else if (_connectStatus == ConnectStatusEnum.connecting) {
      return ConnectStatusEnum.connecting;
    } else if (_connectStatus == ConnectStatusEnum.close) {
      return ConnectStatusEnum.close;
    } else if (_connectStatus == ConnectStatusEnum.closing) {
      return ConnectStatusEnum.closing;
    }
    return ConnectStatusEnum.closing;
  }

  /// 销毁通道
  void dispose() {
    //关闭心跳定时器
    if (timer != null) {
      timer?.cancel();
      timer = null;
    }
    //断开连接
    disconnect();
  }
}
