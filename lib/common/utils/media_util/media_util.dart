import 'dart:io';

import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils.dart';

class SelectMediaUtil {
  static Future<String?> selectImage({
    double? width,
    double? height,
    double? ratioX,
    double? ratioY,
    bool noticePermissions = true,
    ImageSource source = ImageSource.gallery,
    bool crop = true,
  }) async {
    if ((source == ImageSource.gallery &&
            await PermissionHelper.getPermissionStatus(
                    GetPlatform.isAndroid ? Permission.storage : Permission.photos,
                    showToast: false) ==
                false) ||
        (source == ImageSource.camera &&
            await PermissionHelper.getPermissionStatus(Permission.camera, showToast: false) ==
                false)) {
      if (noticePermissions) {
        Loading.toast('Please go to settings to enable permissions');
        return null;
      }
      return 'no permissions';
    }
    ImagePicker imagePicker = ImagePicker();
    XFile? image;
    String? path;
    try {
      image = await imagePicker.pickImage(source: source);
    } catch (e) {
      image = null;
      path = '';
    }

    if (image != null) {
      if (crop == false) {
        return image.path;
      }
      return Future.delayed(const Duration(milliseconds: 100), () async {
        return await cropImage(
          image: image!.path,
          width: width,
          height: height,
          ratioX: ratioX,
          ratioY: ratioY,
        );
      });
    } else {
      return path;
    }
  }

  ///选择音视频
  static Future<String?> selectVideo() async {
    if (await PermissionHelper.getPermissionStatus(
            GetPlatform.isAndroid ? Permission.storage : Permission.photos) ==
        false) {
      Loading.toast('Please go to settings to enable permissions');
      return null;
    }

    ImagePicker imagePicker = ImagePicker();
    final XFile? image = await imagePicker.pickVideo(source: ImageSource.gallery);
    if (image != null) {
      return image.path;
    }
    return null;
  }

  ///裁切图片
  ///[image] 图片路径或文件
  ///[width] 宽度
  ///[height] 高度
  ///[aspectRatio] 比例
  ///[androidUiSettings]UI 参数
  ///[iOSUiSettings] ios的ui 参数
  static Future<String?> cropImage(
      {required image,
      double? width,
      double? height,
      double? ratioX,
      double? ratioY,
      androidUiSettings,
      iOSUiSettings}) async {
    String imagePth = "";
    if (image is String) {
      imagePth = image;
    } else if (image is File) {
      imagePth = image.path;
    } else {
      throw ("文件路径错误");
    }
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imagePth,
      maxWidth: width?.toInt(),
      maxHeight: height?.toInt(),
      aspectRatio: CropAspectRatio(ratioX: ratioX ?? 1.0, ratioY: ratioY ?? 1.0),
      uiSettings: [
        androidUiSettings ??
            AndroidUiSettings(
                toolbarTitle: 'Picture cropping',
                toolbarColor: AppColor.mainBg,
                toolbarWidgetColor: Colors.white,
                initAspectRatio: CropAspectRatioPreset.original,
                hideBottomControls: false,
                showCropGrid: false,
                lockAspectRatio: true),
        iOSUiSettings ??
            IOSUiSettings(
              title: 'Picture cropping',
              doneButtonTitle: 'Done',
              cancelButtonTitle: 'Cancel',
              aspectRatioLockEnabled: true,
              resetAspectRatioEnabled: false,
              rotateClockwiseButtonHidden: true,
            ),
      ],
    );
    if (croppedFile != null) {
      return croppedFile.path;
    }
    return null;
  }
}
