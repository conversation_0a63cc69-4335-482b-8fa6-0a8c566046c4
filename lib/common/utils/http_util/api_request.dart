import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/utils/db_util/audio_entity.dart';
import 'package:amor_app/common/utils/db_util/http_entity.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

import 'http_util.dart';

extension ApiRequest on HttpUtils {
  static bool showLoading = false;
  static Timer? loadingTimer;
  static int loadingSeconds = 0;

  /// Get请求
  static Future<BaseResponse?> get({
    required String api,
    Map<String, dynamic> params = const {},
    bool loding = true,
    Function(BaseResponse? cacheResp)? cacheCallBack,
  }) async {
    showLoading = loding;
    if (loding) {
      Loading.show();
      startLoadingTimer();
    }
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = [];
    if (cacheCallBack != null) {
      cacheData =
          await httpEntity.find(where: {'param': jsonEncode(params), 'url': '${Api.baseUrl}$api'});
      if (cacheData.isNotEmpty) {
        cacheCallBack.call(BaseResponse.fromJson(jsonDecode(cacheData.first['response'])));
      }
    }
    final data = await HttpUtils().get(api: api, params: params);
    if (data != null && cacheCallBack != null) {
      if (cacheData.isEmpty) {
        httpEntity.insert(setCahatParam(api: api, params: params, data: data));
      } else {
        Map<String, dynamic> rafreshData =
            setCahatParam(api: api, params: params, data: data, id: cacheData.first['id']);
        await httpEntity.update(rafreshData, cacheData.first['id']);
      }
    }
    if (data == null && cacheData.isNotEmpty) {
      return BaseResponse.fromJson(jsonDecode(cacheData.first['response']));
    }
    stopLoadingTimer();
    return data;
  }

  /// Post请求
  static Future<BaseResponse?> post(
      {required String api,
      Map<String, dynamic> params = const {},
      Map<String, dynamic> headers = const {},
      String? xmlParams,
      String? responseType = 'json',
      Function(BaseResponse? cacheResp)? cacheCallBack,
      bool loding = true}) async {
    if (api != Api.operationReport) {
      showLoading = loding;
      if (loding) {
        Loading.show();
        startLoadingTimer();
      }
    }
    HttpEntity httpEntity = HttpEntity.instan();
    List<Map<String, dynamic>> cacheData = [];
    if (cacheCallBack != null) {
      cacheData =
          await httpEntity.find(where: {'param': jsonEncode(params), 'url': '${Api.baseUrl}$api'});

      if (cacheData.isNotEmpty) {
        cacheCallBack.call(BaseResponse.fromJson(jsonDecode(cacheData.first['response'])));
      }
    }
    final data = await HttpUtils().post(
        api: api,
        params: params,
        headers: headers,
        xmlParams: xmlParams,
        responseType: responseType);
    if (data != null && cacheCallBack != null) {
      if (cacheData.isEmpty) {
        httpEntity.insert(setCahatParam(api: api, params: params, data: data));
      } else {
        Map<String, dynamic> rafreshData =
            setCahatParam(api: api, params: params, data: data, id: cacheData.first['id']);
        await httpEntity.update(rafreshData, cacheData.first['id']);
      }
    }
    if (data == null && cacheData.isNotEmpty) {
      return BaseResponse.fromJson(jsonDecode(cacheData.first['response']));
    }
    stopLoadingTimer();
    return data;
  }

  ///设置缓存
  static Map<String, dynamic> setCahatParam({
    required String api,
    required Map<String, dynamic> params,
    required BaseResponse data,
    int? id,
  }) {
    return {
      'param': jsonEncode(params),
      'url': '${Api.baseUrl}$api',
      'response': jsonEncode(
        data.toJson(),
      ),
      'id': id,
    };
  }

  ///下载音频
  static Future<Uint8List?> downloadVoice(String url, {String? path}) async {
    Directory tempDir = await getTemporaryDirectory();
    String tempDirPath = tempDir.path;
    String tempPath;
    if (path == null) {
      tempPath = "$tempDirPath/${CommonUtil.currentTimeMillis()}.mp3";
    } else {
      tempPath = "$tempDirPath/$path.mp3";
    }
    await Dio().download(url, tempPath, options: Options(responseType: ResponseType.bytes));
    Uint8List data = await File(tempPath).readAsBytes();
    final double sizeInMB = data.length / (1024 * 1024);
    if (data.isNotEmpty) {
      AudioEntity audioEntity = AudioEntity.instan();
      List<Map<String, dynamic>> cacheData = await audioEntity.find(where: {'url': url});
      //大于1M 存储文件路径
      if (sizeInMB > 1) {
        // 缓存列表有数据就更新 没有数据才插入
        if (cacheData.isNotEmpty) {
          audioEntity.update({'url': url, 'audio': tempPath}, cacheData.first['id']);
        } else {
          audioEntity.insert({'url': url, 'audio': tempPath});
        }
      } else {
        // 缓存列表有数据就更新 没有数据才插入
        if (cacheData.isNotEmpty) {
          audioEntity.update({'url': url, 'audio': data}, cacheData.first['id']);
        } else {
          audioEntity.insert({'url': url, 'audio': data});
        }
      }
      return data;
    }
    return null;
  }

  static startLoadingTimer() {
    loadingSeconds = 0;
    stopLoadingTimer();
    loadingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (loadingSeconds > 10) {
        Loading.dismiss();
      } else {
        loadingSeconds++;
      }
    });
  }

  static stopLoadingTimer() {
    if (loadingTimer != null) {
      loadingTimer!.cancel();
      loadingTimer = null;
    }
  }
}
