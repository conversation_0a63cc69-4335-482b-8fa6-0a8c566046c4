import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'http_interceptors.dart';

// 这个是用来判断是否是生产环境
const bool inProduction = bool.fromEnvironment("dart.vm.product");

class HttpUtils {
  /// 连接超时时间
  static const int connectTimeout = 2 * 10000;

  /// 响应超时时间
  static const int receiveTimeout = 2 * 10000;
  static HttpUtils? _instance;
  static Dio _dio = Dio();

  Dio get dio => _dio;

  HttpUtils._internal() {
    _instance = this;
    _instance!._init();
  }

  factory HttpUtils() => _instance ?? HttpUtils._internal();

  static HttpUtils? getInstance() {
    _instance ?? HttpUtils._internal();
    return _instance;
  }

  _init() async {
    /// 初始化基本选项
    String baseUrl = Api.baseUrl;
    BaseOptions options = BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(milliseconds: connectTimeout),
        receiveTimeout: const Duration(milliseconds: receiveTimeout));

    /// 初始化dio
    _dio = Dio(options);

    /// 添加拦截器
    _dio.interceptors.add(DioInterceptors());

    ///日志打印
    _dio.interceptors.add(LogInterceptor(responseBody: true));
  }

  // Get请求
  Future<BaseResponse?> get(
      {required String api,
      Map<String, dynamic> params = const {},
      Map<String, dynamic> headers = const {}}) async {
    // getHeaderOptions(api).headers?.addAll(headers);//添加header 暂时用不到
    try {
      Response response = await _dio.get(api, queryParameters: params);
      return parseResponse(response);
    } catch (e) {
      if (ApiRequest.showLoading) {
        Loading.dismiss();
      }
      return null;
    }
  }

  // Post请求
  Future<BaseResponse?> post(
      {required String api,
      Map<String, dynamic> params = const {},
      String? xmlParams,
      String? responseType = 'json',
      Map<String, dynamic> headers = const {}}) async {
    debugPrint('$api 参数：$params');
    Options? options = Options(
        headers: headers,
        responseType:
            responseType != 'json' ? ResponseType.bytes : ResponseType.json);
    try {
      Response response =
          await _dio.post(api, data: xmlParams ?? params, options: options);
      return parseResponse(response);
    } catch (e) {
      if (ApiRequest.showLoading) {
        Loading.dismiss();
      }
      return null;
    }
  }

  /// handler
  Future<BaseResponse?> parseResponse(Response response) async {
    // var connect = await Connectivity().checkConnectivity();
    // if (connect == ConnectivityResult.none) {
    // Loading.toast("Network error, let's try again");
    // }
    //文字转语音以二进制格式解析
    if (response.requestOptions.responseType == ResponseType.bytes) {
      // String? path = await FileUtil.saveVoiceFile(response.data);
      return BaseResponse(
          data: response.data, success: true, code: 0, msg: '', page: null);
    }
    if (response.statusCode == HttpStatus.ok ||
        response.statusCode == HttpStatus.notModified) {
      Map<String, dynamic> resp =
          (response.data is String) ? jsonDecode(response.data) : response.data;
      return BaseResponse.fromJson(resp);
    }

    return null;
  }
}
