import 'dart:io';

import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:get/get.dart';

class HttpHeaderBuilder {
  static Map<String, dynamic>? headerParam;
  static Map<String, dynamic> setHeader() {
    SPService sp = Get.find<SPService>();
    if (headerParam == null ||
        headerParam?["packageName"] == null ||
        headerParam?["osname"] == null) {
      Map<String, dynamic> headers = {};
      headers['clientType'] = Platform.isAndroid ? 'android' : 'ios';
      if (sp.get(spAppInfo) != null) {
        final packageModel = PackageInfoModel.fromJson(
            sp.get(spAppInfo) as Map<String, dynamic>);
        //数字版本号
        headers["versionCode"] =
            CommonUtil.getVersionWithNumStr(packageModel.version!);
        //包名
        headers["packageName"] = packageModel.packageName ?? '';
      }
      headers['Accept-Language'] =
          '${Get.deviceLocale?.languageCode ?? 'en'}_${Get.deviceLocale?.countryCode ?? 'US'}';
      if (sp.get(spDeviceInfo) != null) {
        final devideModel = DeviceInfoModel.fromJson(
            sp.get(spDeviceInfo) as Map<String, dynamic>);
        //手机型号
        headers["device"] = devideModel.brand;
        //系统名称
        headers["osname"] =
            '${devideModel.platform} ${devideModel.systemVersion}';
      }
      headerParam = headers;
    }
    //cloak
    headerParam!["cloak"] = AppService.audit;
    //设备码
    headerParam!["deviceId"] = sp.get(spDeviceId);
    // 头部添加token
    if (Get.isRegistered<UserService>() == true && UserService.to.isLogin) {
      headerParam!["token"] = Get.find<UserService>().token;
    } else {
      headerParam!["token"] = null;
    }
    return headerParam!;
  }
}
