import 'dart:convert';
import 'dart:io';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_socket_recived.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;

import 'header_builder.dart';

//请求和响应拦截器
class DioInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.headers = HttpHeaderBuilder.setHeader();
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (ApiRequest.showLoading) {
      if (response.realUri.toString().contains('save-analysis-log') ||
          response.realUri.toString().contains('session/list')) {
        return;
      }
      //统计api不控制loading
      Loading.dismiss();
    }
    //文字转语音以二进制格式解析
    if (response.requestOptions.responseType == ResponseType.bytes) {
      handler.next(response);
      return;
    }
    // if (!response.realUri.toString().contains('knows')) {}
    if (response.statusCode == HttpStatus.ok || response.statusCode == HttpStatus.notModified) {
      Map<String, dynamic> resp =
          (response.data is String) ? jsonDecode(response.data) : response.data;
      BaseResponse model = BaseResponse.fromJson(resp);

      if (model.code == -10) {
        await UserService.to.clearUserInfo();
        UserService.to.loginTip();
      } else if (model.code != 0) {
        if (model.msg != null && model.msg!.isNotEmpty && model.code != -6) {
          Loading.toast(model.msg!);
        }
        if (model.code == -3 || model.code == -7) {
          //充值会员
          Future.delayed(
              Duration(milliseconds: (model.msg != null && model.msg!.isNotEmpty) ? 2000 : 0), () {
            if ((UserService.to.isLogin == false)) {
              UserService.to.loginTip();
              return;
            }
            Analytics().logEvent(Analytics.view,
                screen: Analytics.pageHot,
                sourceScreen: getx.Get.isRegistered<SessionPageController>() == true
                    ? Analytics.pageChatwin
                    : null,
                sourceChar: getx.Get.isRegistered<SessionPageController>() == true
                    ? SessionPageController.to.state.modelId.toString()
                    : null);
            PurchaseSheet.show(
                page: ReportUtil.chatwin,
                modelId: getx.Get.isRegistered<SessionPageController>() == true
                    ? SessionPageController.to.state.modelId
                    : null);
          });
        }
        if (model.code == -6) {
          if (getx.Get.isRegistered<SessionPageController>()) {
            SessionSocketRecived.showGemsDialog(saveFailedMsg: false);
          }
        }
      }
    } else {
      Loading.toast("Network error, let's try again");
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      // 连接服务器超时
      case DioExceptionType.connectionTimeout:
        {
          // 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;
      // 响应超时
      case DioExceptionType.receiveTimeout:
        {
          // 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;
      // 发送超时
      case DioExceptionType.sendTimeout:
        {
          // 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;
      // 请求取消
      case DioExceptionType.cancel:
        {
          // 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;
      // 404/503错误
      case DioExceptionType.badResponse:
        {
          // 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;

      case DioExceptionType.badCertificate:
        break;
      case DioExceptionType.connectionError:
        break;
      case DioExceptionType.unknown:
        break;
    }
    //  return handler
    //       .resolve(Response(requestOptions: e.requestOptions, data: null));
    // 如果你想完成请求并返回一些自定义数据，可以resolve 一个`Response`,如`handler.resolve(response)`。
    // 这样请求将会被终止，上层then会被调用，then中返回的数据将是你的自定义response.
    super.onError(err, handler);
  }
}
