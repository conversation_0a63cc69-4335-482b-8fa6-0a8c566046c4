import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

class SttUtil {
  static final SttUtil _speechToTextUtil = SttUtil._internal();
  factory SttUtil() {
    return _speechToTextUtil;
  }
  final SpeechToText speechToText = SpeechToText();

  bool speechEnabled = false;
  String resultString = '';
  Function(SpeechRecognitionError error)? errorCallBack;
  SttUtil._internal();
  Future<bool> checkAvailable() async {
    if (await PermissionHelper.getPermissionStatus(Permission.microphone,
            needAwait: true) ==
        false) {
      return false;
    }
    if (speechEnabled == false) {
      speechEnabled = await initSpeech();
      if (speechEnabled == false) {
        Loading.toast('Please try again later');
        return false;
      }
    }
    return speechEnabled;
  }

  Future<bool> initSpeech() async {
    bool enable = false;
    try {
      enable = await speechToText.initialize(
        onError: (errorNotification) {
          // print('stt Error:${errorNotification.toJson()}');
          if (errorCallBack != null) {
            errorCallBack!(errorNotification);
          }
          stopListening();
        },
        onStatus: (status) {
          // print('stt status:$status');
        },
      );
      return enable;
    } catch (e) {
      print('初始化语音转文字失败');
      return enable;
    }
  }

  void startSpeech({
    required Function(SpeechRecognitionResult onResult) textCallBack,
    required Function(double level) levelCallBack,
    required Function(SpeechRecognitionError error) errCallBack,
  }) async {
    errorCallBack = errCallBack;
    await speechToText.listen(
      //最大录音时长
      listenFor: const Duration(seconds: 30),
      //静默时长
      pauseFor: Duration(seconds: 6),
      //地区
      localeId: 'en_US',
      //目前Android和iOS之间的值不同，还不能从Android文档中确定值的含义。在iOS上，返回的值以分贝为单位。
      onSoundLevelChange: (level) {
        levelCallBack(level);
      },
      listenOptions: SpeechListenOptions(
        // onDevice: true,
        //错误时自动取消侦听。默认为false,当为false时，应该从错误处理程序调用cancel。
        cancelOnError: true,
        //监听每个输入(true)还是回调最终结果
        partialResults: true,
        listenMode: ListenMode.dictation,
        //iOS 自动标点
        autoPunctuation: true,
      ),
      onResult: textCallBack,
    );
  }

  void stopListening() async {
    errorCallBack = null;
    await speechToText.cancel();
  }

  /// This is the callback that the SpeechToText plugin calls when
  /// the platform returns recognized words.
  void onSpeechResult(SpeechRecognitionResult result) {
    resultString = result.recognizedWords;
  }
}
