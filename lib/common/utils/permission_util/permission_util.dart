import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionHelper {
  //检查权限
  static Future<bool> getPermissionStatus(Permission permission,
      {bool needAwait = false, bool showToast = true, bool request = true}) async {
    //granted 通过，denied 被拒绝，permanentlyDenied 拒绝且不在提示
    if (permission == Permission.storage) {
      if (GetPlatform.isAndroid) {
        String? systemVersion;
        if (Get.find<SPService>().get(spDeviceInfo) != null) {
          final devideModel = DeviceInfoModel.fromJson(
              Get.find<SPService>().get(spDeviceInfo) as Map<String, dynamic>);
          systemVersion = devideModel.systemVersion;
        }
        if (double.parse(systemVersion ?? '13') > 12) {
          //安卓13以上系统版本权限细分，使用photos权限
          permission = Permission.photos;
        }
      }
    }

    PermissionStatus status = await permission.status;
    // print('权限');
    // print(permission);
    // print(status);
    if (request == false) {
      return status.isGranted ? true : false;
    }

    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      if (permission == Permission.microphone) {
        if (needAwait) {
          return await requestPermission(permission);
        } else {
          requestPermission(permission);
          return false; //先返回false 不弹窗录音界面
        }
      }
      return requestPermission(permission);
    } else if (status.isPermanentlyDenied) {
      if (showToast) {
        Loading.toast('No Permissions');
      }
      // openAppSettings();
      return false;
    } else if (status.isRestricted) {
      if (permission == Permission.microphone) {
        requestPermission(permission);
        return false; //先返回false 不弹窗录音界面
      }
      return requestPermission(permission);
    } else {}
    return false;
  }

  ///申请权限
  static Future<bool> requestPermission(Permission permission) async {
    PermissionStatus status = await permission.request();
    if (status.isGranted) {
      return true;
    }
    if (status.isPermanentlyDenied) {
      // openAppSettings();
      // Loading.toast('No permissions');
    }
    return false;
  }
}
