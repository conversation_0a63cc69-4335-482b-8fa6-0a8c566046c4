import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
part 'register_model.gs.dart';

// @JsonSerializable()
class RegisterModel {
  String? audioUrl;
  int? questionId;
  String? questionType;
  int? selectType;
  String? title;
  String? subTitle;
  @_OptionRespsModelConverter()
  List<OptionRespsModel>? optionResps;

  RegisterModel(
      {this.audioUrl,
      this.optionResps,
      this.questionId,
      this.questionType,
      this.selectType,
      this.subTitle,
      this.title});
  factory RegisterModel.fromJson(Map<String, dynamic> json) => _$RegisterModelFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterModelToJson(this);
}

class _OptionRespsModelConverter implements JsonConverter<List<OptionRespsModel>?, String?> {
  const _OptionRespsModelConverter();

  @override
  List<OptionRespsModel>? fromJson(String? json) {
    final List<OptionRespsModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(OptionRespsModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<OptionRespsModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class OptionRespsModel {
  String? content;
  int? optionId;
  String? image;
  String? subContent;
  bool? selected;

  OptionRespsModel({
    this.content,
    this.optionId,
    this.image,
    this.subContent,
    this.selected,
  });
  factory OptionRespsModel.fromJson(Map<String, dynamic> json) => _$OptionRespsModelFromJson(json);

  Map<String, dynamic> toJson() => _$OptionRespsModelToJson(this);
}
