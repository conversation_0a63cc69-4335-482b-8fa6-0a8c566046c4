// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegisterModel _$RegisterModelFromJson(Map<String, dynamic> json) => RegisterModel(
      audioUrl: json['audioUrl'] as String?,
      optionResps: const _OptionRespsModelConverter().fromJson(jsonEncode(json['optionResps'])),
      questionId: json['questionId'] as int?,
      questionType: json['questionType'] as String?,
      selectType: json['selectType'] as int?,
      subTitle: json['subTitle'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$RegisterModelToJson(RegisterModel instance) => <String, dynamic>{
      'audioUrl': instance.audioUrl,
      'questionId': instance.questionId,
      'questionType': instance.questionType,
      'selectType': instance.selectType,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'optionResps': const _OptionRespsModelConverter().toJson(instance.optionResps),
    };

OptionRespsModel _$OptionRespsModelFromJson(Map<String, dynamic> json) => OptionRespsModel(
    content: json['content'] as String?,
    subContent: json['subContent'] as String?,
    optionId: json['optionId'] as int?,
    selected: json['selected'] as bool?,
    image: json['image'] as String?);

Map<String, dynamic> _$OptionRespsModelToJson(OptionRespsModel instance) => <String, dynamic>{
      'content': instance.content,
      'optionId': instance.optionId,
      'image': instance.image,
      'selected': instance.selected,
      'subContent': instance.subContent,
    };
