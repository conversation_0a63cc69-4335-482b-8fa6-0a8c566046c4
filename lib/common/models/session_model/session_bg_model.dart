import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
part 'session_bg_model.gx.dart';

class SessionBgModel {
  int? discount;
  int? originGems;
  int? saleGems;
  @_SessionBgModelListItemConverter()
  List<SessionBgModelListItem>? list;
  SessionBgModel({this.discount, this.list, this.originGems, this.saleGems});
  factory SessionBgModel.fromJson(Map<String, dynamic> json) => _$SessionBgModelFromJson(json);

  Map<String, dynamic> toJson() => _$SessionBgModelToJson(this);
}

class _SessionBgModelListItemConverter
    implements JsonConverter<List<SessionBgModelListItem>?, String?> {
  const _SessionBgModelListItemConverter();

  @override
  List<SessionBgModelListItem>? fromJson(String? json) {
    final List<SessionBgModelListItem> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(SessionBgModelListItem.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<SessionBgModelListItem>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class SessionBgModelListItem {
  String? bgId;
  String? id; //-1 默认图片 -2 官方默认图片
  int? originGems;
  String? paymentStatus;
  int? saleGems;
  int? discount;
  String? type;
  String? url;
  bool? selected;
  SessionBgModelListItem({
    this.bgId,
    this.id,
    this.originGems,
    this.paymentStatus,
    this.saleGems,
    this.type,
    this.url,
    this.discount,
    this.selected,
  });
  factory SessionBgModelListItem.fromJson(Map<String, dynamic> json) =>
      _$SessionBgModelListItemFromJson(json);

  Map<String, dynamic> toJson() => _$SessionBgModelListItemToJson(this);
}
