part of 'session_report_model.dart';

ReportInfoModel _$ReportInfoModelFromJson(Map<String, dynamic> json) => ReportInfoModel(
      title: json['title'] as String?,
      dataList: const _ChatGroupInfoModelConverter().fromJson(jsonEncode(json['dataList'])),
    );

Map<String, dynamic> _$ReportInfoModelToJson(ReportInfoModel instance) => <String, dynamic>{
      'title': instance.title,
      'dataList': const _ChatGroupInfoModelConverter().toJson(instance.dataList),
    };

ReportInfoModelItem _$ReportInfoModelItemFromJson(Map<String, dynamic> json) => ReportInfoModelItem(
      problemType: json['problemType'] as String?,
      id: json['id'] as int?,
      content: json['content'] as String?,
    );

Map<String, dynamic> _$ReportInfoModelItemToJson(ReportInfoModelItem instance) => <String, dynamic>{
      'problemType': instance.problemType,
      'id': instance.id,
      'content': instance.content,
    };
