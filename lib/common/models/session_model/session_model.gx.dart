// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SessionModel _$SessionModelFromJson(Map<String, dynamic> json) {
  //设置语音大概的时长
  if (json['content'] != null && json['duration'] == null) {
    json['duration'] = CommonUtil.extractEmojis((json['content'] as String)).length ~/ 9;
  }
  //旁白格式是否错误
  bool msgIgnoreErr = json['contentType'] == 'text' &&
      CommonUtil.countOccurrences(json['content'] ?? '', '*').isOdd;
  bool traMsgIgnoreErr = json['contentType'] == 'text' &&
      CommonUtil.countOccurrences(json['translateContent'] ?? '', '*').isOdd;
  return SessionModel(
    avatarUrl: json['avatarUrl'] as String?,
    msgId: json['msgId'] as String?,
    msgType: json['msgType'] as int?,
    contentType: json['contentType'] as String?,
    content: json['content'] as String?,
    duration: json['duration'] as int?,
    errorMsg: json['errorMsg'] as String?,
    contentAnalysis: json['contentAnalysis'] as String?,
    sendDate: json['sendDate'] as int?,
    status: json['status'] as int?,
    soundLimiter: json['soundLimiter'] as bool?,
    readonly: json['readonly'] as bool?,
    unlock: json['unlock'] as int?,
    requestId: json['requestId'] as String?,
    groupNo: json['groupNo'] as String?,
    translation: json['translation'] as String?,
    secondContent: json['secondContent'] as String?,
    image: json['image'] as String?,
    contentCategory: json['contentCategory'] as String?,
    imagesScale: json['imagesScale'] as String?,
    upvote: json['upvote'] as int?,
    //旁白
    msgIgnore: msgIgnoreErr == true
        ? []
        : CommonUtil.getTextBetweenStr(
                text: json['content'] ?? '', exp: [r'\*([^]*?)\*', r'\((.*?)\)'], include: 1) ??
            [],
    traMsgIgnore: traMsgIgnoreErr == true
        ? []
        : CommonUtil.getTextBetweenStr(
                text: json['translateContent'] ?? '',
                exp: [r'\*([^]*?)\*', r'\((.*?)\)'],
                include: 1) ??
            [],
    msgIgnoreErr: msgIgnoreErr,
    traMsgIgnoreErr: traMsgIgnoreErr,
    score: json['score'] as int?,
    actionTips: json['actionTips'] as List<dynamic>?,
    translateContent: json['translateContent'] as String?,
    //有翻译内容就默认展示
    showTransContent: json['translateContent'] != null,
    giftId: json['giftId'] as int?,
    giftImg: json['giftImg'] as String?,
  );
}

Map<String, dynamic> _$SessionModelToJson(SessionModel instance) => <String, dynamic>{
      'avatarUrl': instance.avatarUrl,
      'content': instance.content,
      'contentType': instance.contentType,
      'msgId': instance.msgId,
      'contentAnalysis': instance.contentAnalysis,
      'sendDate': instance.sendDate,
      'msgType': instance.msgType,
      'upvote': instance.upvote,
      'duration': instance.duration,
      'score': instance.score,
      'status': instance.status,
      'requestId': instance.requestId,
      'readonly': instance.readonly,
      'soundLimiter': instance.soundLimiter,
      'groupNo': instance.groupNo,
      'errorMsg': instance.errorMsg,
      'secondContent': instance.secondContent,
      'translation': instance.translation,
      'image': instance.image,
      'imagesScale': instance.imagesScale,
      'contentCategory': instance.contentCategory,
      'msgIgnore': instance.msgIgnore,
      'msgIgnoreErr': instance.msgIgnoreErr,
      'actionTips': instance.actionTips,
      'unlock': instance.unlock,
      'translateContent': instance.translateContent,
      'showTransContent': instance.showTransContent,
      'giftId': instance.giftId,
      'giftImg': instance.giftImg,
    };

SessionModelInfoModel _$SessionModelInfoModelFromJson(Map<String, dynamic> json) =>
    SessionModelInfoModel(
      cover: json['cover'] as String?,
      modelId: json['modelId'] as int?,
      modelName: json['modelName'] as String?,
      voice: json['voice'] as String?,
    );

Map<String, dynamic> _$SessionModelInfoModelToJson(SessionModelInfoModel instance) =>
    <String, dynamic>{
      'cover': instance.cover,
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'voice': instance.voice,
    };
