import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'session_report_model.gx.dart';

class ReportInfoModel {
  String? title;
  @_ChatGroupInfoModelConverter()
  List<ReportInfoModelItem>? dataList;
  ReportInfoModel({
    this.title,
    this.dataList,
  });
  factory ReportInfoModel.fromJson(Map<String, dynamic> json) => _$ReportInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReportInfoModelToJson(this);
}

class _ChatGroupInfoModelConverter implements JsonConverter<List<ReportInfoModelItem>?, String?> {
  const _ChatGroupInfoModelConverter();

  @override
  List<ReportInfoModelItem>? fromJson(String? json) {
    final List<ReportInfoModelItem> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(ReportInfoModelItem.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<ReportInfoModelItem>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class ReportInfoModelItem {
  String? problemType;
  int? id;
  String? content;
  ReportInfoModelItem({
    this.problemType,
    this.id,
    this.content,
  });
  factory ReportInfoModelItem.fromJson(Map<String, dynamic> json) =>
      _$ReportInfoModelItemFromJson(json);

  Map<String, dynamic> toJson() => _$ReportInfoModelItemToJson(this);
}
