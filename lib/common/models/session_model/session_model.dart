import 'dart:typed_data';

import 'package:amor_app/common/utils/utils.dart';

part 'session_model.gx.dart';

//flutter packages pub run build_runner build
// @JsonSerializable()
class SessionModel {
  String? avatarUrl;
  String? content;
  String? contentType;
  String? msgId;
  String? contentAnalysis;
  int? sendDate;
  int? msgType; //1:user
  /*
    DEFAULT(0, "默认"),
    LIKE(1, "喜欢"),
    DISLike(2, "不喜欢"),
   */
  int? upvote;
  int? duration;
  int? score;
  int? status; //-2：错误消息
  String? requestId;
  bool? readonly;
  bool? soundLimiter;
  String? groupNo;
  String? errorMsg;
  String? secondContent;
  String? translation;
  String? image;
  String? imagesScale;
  String? contentCategory;
  List<String>? msgIgnore; //旁白
  List<String>? traMsgIgnore; //旁白

  bool? msgIgnoreErr; //旁白格式错误
  bool? traMsgIgnoreErr; //旁白格式错误
  int? unlock;
  List? actionTips;
  List? actionTipsType; //0 普通消息  1 动作或旁白 用斜体展示
  Uint8List? audioBuffer;
  //翻译后的内容
  String? translateContent;
  //展示翻译的内容还是原文
  bool? showTransContent;
  String? giftImg;
  int? giftId;
  SessionModel({
    this.avatarUrl,
    this.msgId,
    this.msgType,
    this.contentType,
    this.content,
    this.duration,
    this.errorMsg,
    this.contentAnalysis,
    this.sendDate,
    this.status,
    this.msgIgnoreErr,
    this.traMsgIgnoreErr,
    this.soundLimiter,
    this.readonly,
    this.requestId,
    this.groupNo,
    this.translation,
    this.secondContent,
    this.image,
    this.contentCategory,
    this.imagesScale,
    this.upvote,
    this.msgIgnore,
    this.traMsgIgnore,
    this.score,
    //动作提示
    this.actionTips,
    this.actionTipsType,
    this.audioBuffer,
    this.unlock,
    this.translateContent,
    this.showTransContent,
    this.giftId,
    this.giftImg,
  });
  factory SessionModel.fromJson(Map<String, dynamic> json) => _$SessionModelFromJson(json);

  Map<String, dynamic> toJson() => _$SessionModelToJson(this);
}

// @JsonSerializable()
class SessionModelInfoModel {
  String? cover;
  int? modelId;
  String? modelName;
  String? voice;

  SessionModelInfoModel({
    this.cover,
    this.modelId,
    this.modelName,
    this.voice,
  });
  factory SessionModelInfoModel.fromJson(Map<String, dynamic> json) =>
      _$SessionModelInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$SessionModelInfoModelToJson(this);
}
