part of 'session_bg_model.dart';

SessionBgModel _$SessionBgModelFromJson(Map<String, dynamic> json) => SessionBgModel(
      discount: json['discount'] as int?,
      list: const _SessionBgModelListItemConverter().fromJson(jsonEncode(json['list'])),
      originGems: json['originGems'] as int?,
      saleGems: json['saleGems'] as int?,
    );

Map<String, dynamic> _$SessionBgModelToJson(SessionBgModel instance) => <String, dynamic>{
      'discount': instance.discount,
      'originGems': instance.originGems,
      'saleGems': instance.saleGems,
      'list': const _SessionBgModelListItemConverter().toJson(instance.list),
    };

SessionBgModelListItem _$SessionBgModelListItemFromJson(Map<String, dynamic> json) =>
    SessionBgModelListItem(
      bgId: json['bgId'] as String?,
      id: jsonEncode(json['id']),
      originGems: json['originGems'] as int?,
      paymentStatus: json['paymentStatus'] as String?,
      saleGems: json['saleGems'] as int?,
      discount: json['discount'] as int?,
      type: json['type'] as String?,
      url: json['url'] as String?,
      selected: json['selected'] as bool?,
    );

Map<String, dynamic> _$SessionBgModelListItemToJson(SessionBgModelListItem instance) =>
    <String, dynamic>{
      'bgId': instance.bgId,
      'id': instance.id,
      'originGems': instance.originGems,
      'paymentStatus': instance.paymentStatus,
      'saleGems': instance.saleGems,
      'type': instance.type,
      'url': instance.url,
      'selected': instance.selected,
      'discount': instance.discount,
    };
