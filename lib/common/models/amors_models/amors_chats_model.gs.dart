part of 'amors_chats_model.dart';

AmorsChatsModel _$AmorsChatsModelFromJson(Map<String, dynamic> json) {
  return AmorsChatsModel(
    avatar: json['avatar'] as String?,
    sessionType: json['sessionType'] as String?,
    favorite: json['favorite'] as bool?,
    lastDatetime: json['lastDatetime'] as int?,
    lastMsgSnapshot: json['lastMsgSnapshot'] as String?,
    sessionNo: json['sessionNo'] as String?,
    title: json['title'] as String?,
    top: json['top'] as bool?,
    topFirst: json['topFirst'] as bool?,
    topLast: json['topLast'] as bool?,
    isAlterEgo: json['isAlterEgo'] as bool?,
    voice: json['voice'] as String?,
    voiceEn: json['voiceEn'] as String?,
    unReadCount: json['unReadCount'] as int?,
    shareCode: json['shareCode'] as int?,
    modelId: json['modelId'] as int?,
    id: json['id'] as int?,
  );
}

Map<String, dynamic> _$AmorsChatsModelToJson(AmorsChatsModel instance) => <String, dynamic>{
      'avatar': instance.avatar,
      'favorite': instance.favorite,
      'lastDatetime': instance.lastDatetime,
      'lastMsgSnapshot': instance.lastMsgSnapshot,
      'sessionNo': instance.sessionNo,
      'title': instance.title,
      'voice': instance.voice,
      'voiceEn': instance.voiceEn,
      'top': instance.top,
      'topFirst': instance.topFirst,
      'topLast': instance.topLast,
      'sessionType': instance.sessionType,
      'unReadCount': instance.unReadCount,
      'isAlterEgo': instance.isAlterEgo,
      'shareCode': instance.shareCode,
      'modelId': instance.modelId,
      'id': instance.id,
    };
