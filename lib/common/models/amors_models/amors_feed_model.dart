import 'package:amor_app/common/utils/utils.dart';

part 'amors_feed_model.gx.dart';

class AmorsFeedModel {
  String? bgImg;
  String? cover;
  bool? hasLike;
  String? intro;
  int? likeCount;
  int? modelId;
  String? modelName;
  String? modelType;
  String? voiceParam;
  String? welcomeMsg;
  String? welcomeVoice;
  String? author;
  String? authorAvatar;
  String? avatar;
  String? createUser;
  int? duration;
  int? purview;
  int? useCount;
  int? gender;
  bool? owner;
  List? tags;
  String? contentCategory;
  String? scenario;
  String? visualTag;
  Map? videoChat;
  List? genImg;
  List? genVideo;
  String? gifUrl;
  List? images;
  List? clothingTypes;
  bool? clothe;
  AmorsFeedModel({
    this.bgImg,
    this.cover,
    this.hasLike,
    this.intro,
    this.likeCount,
    this.modelId,
    this.modelName,
    this.modelType,
    this.voiceParam,
    this.welcomeMsg,
    this.welcomeVoice,
    this.visualTag,
    this.author,
    this.duration,
    this.authorAvatar,
    this.purview,
    this.owner,
    this.useCount,
    this.avatar,
    this.createUser,
    this.tags,
    this.contentCategory,
    this.scenario,
    this.gender,
    this.videoChat,
    this.genImg,
    this.genVideo,
    this.gifUrl,
    this.images,
    this.clothingTypes,
    this.clothe,
  });

  factory AmorsFeedModel.fromJson(Map<String, dynamic> json) => _$AmorsFeedModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmorsFeedModelToJson(this);
}
