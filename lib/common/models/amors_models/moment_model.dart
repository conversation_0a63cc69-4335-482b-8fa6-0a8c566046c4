import 'package:json_annotation/json_annotation.dart';
part 'moment_model.g.dart';

@JsonSerializable()
class MomentModel {
  int? id;
  String? characterAvatar;
  int? characterId;
  String? characterName;
  String? cover;
  int? createTime;
  int? duration;
  bool? hideCharacter;
  bool? istop;
  String? media;
  String? mediaText;
  String? text;
  int? updateTime;

  MomentModel({
    this.cover,
    this.characterAvatar,
    this.characterId,
    this.characterName,
    this.createTime,
    this.duration,
    this.hideCharacter,
    this.id,
    this.istop,
    this.media,
    this.mediaText,
    this.text,
    this.updateTime,
  });

  factory MomentModel.fromJson(Map<String, dynamic> json) => _$MomentModelFromJson(json);

  Map<String, dynamic> toJson() => _$MomentModelToJson(this);
}
