// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'moment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MomentModel _$MomentModelFromJson(Map<String, dynamic> json) => MomentModel(
      cover: json['cover'] as String?,
      characterAvatar: json['characterAvatar'] as String?,
      characterId: (json['characterId'] as num?)?.toInt(),
      characterName: json['characterName'] as String?,
      createTime: (json['createTime'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      hideCharacter: json['hideCharacter'] as bool?,
      id: (json['id'] as num?)?.toInt(),
      istop: json['istop'] as bool?,
      media: json['media'] as String?,
      mediaText: json['mediaText'] as String?,
      text: json['text'] as String?,
      updateTime: (json['updateTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MomentModelToJson(MomentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'characterAvatar': instance.characterAvatar,
      'characterId': instance.characterId,
      'characterName': instance.characterName,
      'cover': instance.cover,
      'createTime': instance.createTime,
      'duration': instance.duration,
      'hideCharacter': instance.hideCharacter,
      'istop': instance.istop,
      'media': instance.media,
      'mediaText': instance.mediaText,
      'text': instance.text,
      'updateTime': instance.updateTime,
    };
