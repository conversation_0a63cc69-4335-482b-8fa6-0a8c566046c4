part 'amors_category_model.gx.dart';

// @JsonSerializable()
class AmorsCategoryModel {
  String? cateName;
  int? id;
  String? style;
  String? cateType;
  int? modelCount;
  String? selected;
  String? unselect;
  String? label;
  AmorsCategoryModel({
    this.cateName,
    this.id,
    this.style,
    this.cateType,
    this.modelCount,
    this.selected,
    this.unselect,
    this.label,
  });
  factory AmorsCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$AmorsCategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmorsCategoryModelToJson(this);
}
