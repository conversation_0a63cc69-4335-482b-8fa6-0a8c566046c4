// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'amors_feed_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AmorsFeedModel _$AmorsFeedModelFromJson(Map<String, dynamic> json) {
  //设置语音大概的时长
  if (json['welcomeMsg'] != null && json['welcomeVoice'] != null) {
    json['duration'] = CommonUtil.extractEmojis((json['welcomeMsg'] as String)).length ~/ 9;
    json['duration'] = json['duration'] > 0 ? json['duration'] : 1;
  }
  // print(json);
  return AmorsFeedModel(
    bgImg: json['bgImg'] as String?,
    cover: json['cover'] as String?,
    hasLike: json['hasLike'] as bool?,
    intro: json['intro'] as String?,
    likeCount: json['likeCount'] as int?,
    modelId: json['modelId'] as int?,
    duration: json['duration'] as int?,
    purview: json['purview'] as int?,
    useCount: json['useCount'] as int?,
    gender: json['gender'] as int?,
    modelName: json['modelName'] as String?,
    modelType: json['modelType'] as String?,
    voiceParam: json['voiceParam'] as String?,
    welcomeMsg: json['welcomeMsg'] as String?,
    welcomeVoice: json['welcomeVoice'] as String?,
    author: json['author'] as String?,
    authorAvatar: json['authorAvatar'] as String?,
    avatar: json['avatar'] as String?,
    createUser: json['createUser'] as String?,
    owner: json['owner'] as bool?,
    tags: json['tags'] as List?,
    contentCategory: json['contentCategory'] as String?,
    scenario: json['scenario'] as String?,
    visualTag: json['visualTag'] as String?,
    videoChat: json['videoChat'] as Map?,
    genImg: json['genImg'] as List?,
    genVideo: json['genVideo'] as List?,
    images: json['images'] as List?,
    gifUrl: json['gifUrl'] as String?,
    clothingTypes: json['clothingTypes'] as List?,
    clothe: json['clothe'] as bool?,
  );
}

Map<String, dynamic> _$AmorsFeedModelToJson(AmorsFeedModel instance) => <String, dynamic>{
      'bgImg': instance.bgImg,
      'cover': instance.cover,
      'hasLike': instance.hasLike,
      'intro': instance.intro,
      'likeCount': instance.likeCount,
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'modelType': instance.modelType,
      'voiceParam': instance.voiceParam,
      'welcomeMsg': instance.welcomeMsg,
      'welcomeVoice': instance.welcomeVoice,
      'author': instance.author,
      'authorAvatar': instance.authorAvatar,
      'purview': instance.purview,
      'owner': instance.owner,
      'useCount': instance.useCount,
      'avatar': instance.avatar,
      'createUser': instance.createUser,
      'tags': instance.tags,
      'contentCategory': instance.contentCategory,
      'scenario': instance.scenario,
      'visualTag': instance.visualTag,
      'gender': instance.gender,
      'videoChat': instance.videoChat,
      'genImg': instance.genImg,
      'genVideo': instance.genVideo,
      'gifUrl': instance.gifUrl,
      'images': instance.images,
      'clothingTypes': instance.clothingTypes,
      'clothe': instance.clothe,
    };
