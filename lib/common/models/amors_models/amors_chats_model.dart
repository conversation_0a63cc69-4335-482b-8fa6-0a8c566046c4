part 'amors_chats_model.gs.dart';

class AmorsChatsModel {
  String? avatar;
  bool? favorite;
  int? lastDatetime;
  String? lastMsgSnapshot;
  String? sessionNo;
  String? title;
  String? voice;
  String? sessionType;
  String? voiceEn;
  int? unReadCount;
  bool? top;
  bool? topFirst;
  bool? topLast;
  bool? isAlterEgo;
  int? shareCode;
  int? modelId;
  int? id;
  AmorsChatsModel({
    this.avatar,
    this.favorite,
    this.lastDatetime,
    this.lastMsgSnapshot,
    this.sessionNo,
    this.title,
    this.sessionType,
    this.top,
    this.topFirst,
    this.topLast,
    this.voice,
    this.isAlterEgo,
    this.unReadCount,
    this.voiceEn,
    this.shareCode,
    this.modelId,
    this.id,
  });

  factory AmorsChatsModel.fromJson(Map<String, dynamic> json) => _$AmorsChatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmorsChatsModelToJson(this);
}
