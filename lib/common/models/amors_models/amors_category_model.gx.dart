// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'amors_category_model.dart';

AmorsCategoryModel _$AmorsCategoryModelFromJson(Map<String, dynamic> json) => AmorsCategoryModel(
      cateName: json['cateName'] as String?,
      cateType: json['cateType'] as String?,
      id: json['id'] as int?,
      style: json['style'] as String?,
      modelCount: json['modelCount'] as int?,
      selected: json['selected'] as String?,
      unselect: json['unselect'] as String?,
      label: json['label'] as String?,
    );

Map<String, dynamic> _$AmorsCategoryModelToJson(AmorsCategoryModel instance) => <String, dynamic>{
      'cateName': instance.cateName,
      'id': instance.id,
      'style': instance.style,
      'modelCount': instance.modelCount,
      'cateType': instance.cateType,
      'selected': instance.selected,
      'unselect': instance.unselect,
      'subContent': instance.label
    };
