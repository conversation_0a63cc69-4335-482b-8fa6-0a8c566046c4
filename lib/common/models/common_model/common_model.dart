import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'common_model.gx.dart';

//flutter packages pub run build_runner build

// @JsonSerializable()
class PackageInfoModel {
  String? appName;
  String? packageName;
  String? version;
  String? buildNumber;
  PackageInfoModel({
    this.appName,
    this.packageName,
    this.version,
    this.buildNumber,
  });

  factory PackageInfoModel.fromJson(Map<String, dynamic> json) => _$PackageInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$PackageInfoModelToJson(this);
}

// @JsonSerializable()
class DeviceInfoModel {
  String? manufacturer; //厂商
  String? brand; //手机品牌加型号
  String? systemVersion; //当前系统版本
  String? platform; //系统名称
  String? uuid; //用户唯一识别码
  // String? incremental;
  DeviceInfoModel({
    this.brand,
    this.systemVersion,
    this.platform,
    this.uuid,
    this.manufacturer,
  });
  factory DeviceInfoModel.fromJson(Map<String, dynamic> json) => _$DeviceInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceInfoModelToJson(this);
}

// @JsonSerializable()
class ConfigModel {
  String? ossUrl;
  String? kfUrl;
  String? privacyPolicy;
  String? userAgreement;
  String? rechargeAgreement;
  String? avatar;
  String? appName;
  String? bucket;
  String? discordUrl;
  String? endpoint;
  String? loginMethods;
  String? voiceUrl;
  String? adId;
  String? afAppid;
  String? afKey;
  bool? fromServer; //标记是否从服务器获取  断网重连后就不用再初始化第三方
  String? chatAgreement;
  String? chatLevelConf; //会话等级信息
  String? openScreen; //首次启动进入聊天的角色ID

  ConfigModel({
    this.ossUrl,
    this.fromServer,
    this.privacyPolicy,
    this.userAgreement,
    this.avatar,
    this.appName,
    this.chatAgreement,
    this.bucket,
    this.endpoint,
    this.rechargeAgreement,
    this.loginMethods,
    this.discordUrl,
    this.voiceUrl,
    this.adId,
    this.kfUrl,
    this.afAppid,
    this.afKey,
    this.chatLevelConf,
    this.openScreen,
  });
  factory ConfigModel.fromJson(Map<String, dynamic> json) => _$ConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigModelToJson(this);
}

//签到信息
// @JsonSerializable()
class SignInitModel {
  int? vipGems;
  int? questsToEarn;
  int? countDown;
  int? generalGems;
  int? status;
  int? signInTimes;
  int? signGems;
  @_SignInitItemModelConverter()
  List<SignInitItemModel>? items;
  SignInitModel({
    this.countDown,
    this.items,
    this.questsToEarn,
    this.signInTimes,
    this.status,
    this.vipGems,
    this.generalGems,
    this.signGems,
  });
  factory SignInitModel.fromJson(Map<String, dynamic> json) => _$SignInitModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignInitModelToJson(this);
}

class _SignInitItemModelConverter implements JsonConverter<List<SignInitItemModel>?, String?> {
  const _SignInitItemModelConverter();

  @override
  List<SignInitItemModel>? fromJson(String? json) {
    final List<SignInitItemModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(SignInitItemModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<SignInitItemModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class SignInitItemModel {
  int? day;
  int? status;
  int? gems;

  SignInitItemModel({this.day, this.gems, this.status});
  factory SignInitItemModel.fromJson(Map<String, dynamic> json) =>
      _$SignInitItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignInitItemModelToJson(this);
}

class TagsItemModel {
  String? name;
  int? id;
  String? label;
  TagsItemModel({
    this.name,
    this.id,
    this.label,
  });
  factory TagsItemModel.fromJson(Map<String, dynamic> json) => _$TagsItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$TagsItemModelToJson(this);
}
