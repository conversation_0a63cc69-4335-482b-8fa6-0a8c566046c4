// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'common_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PackageInfoModel _$PackageInfoModelFromJson(Map<String, dynamic> json) => PackageInfoModel(
      appName: json['appName'] as String?,
      packageName: json['packageName'] as String?,
      version: json['version'] as String?,
      buildNumber: json['buildNumber'] as String?,
    );

Map<String, dynamic> _$PackageInfoModelToJson(PackageInfoModel instance) => <String, dynamic>{
      'appName': instance.appName,
      'packageName': instance.packageName,
      'version': instance.version,
      'buildNumber': instance.buildNumber,
    };

DeviceInfoModel _$DeviceInfoModelFromJson(Map<String, dynamic> json) => DeviceInfoModel(
      brand: json['brand'] as String?,
      systemVersion: json['systemVersion'] as String?,
      platform: json['platform'] as String?,
      uuid: json['uuid'] as String?,
      manufacturer: json['manufacturer'] as String?,
    );

Map<String, dynamic> _$DeviceInfoModelToJson(DeviceInfoModel instance) => <String, dynamic>{
      'brand': instance.brand,
      'systemVersion': instance.systemVersion,
      'platform': instance.platform,
      'uuid': instance.uuid,
      'manufacturer': instance.manufacturer,
    };

ConfigModel _$ConfigModelFromJson(Map<String, dynamic> json) => ConfigModel(
      ossUrl: json['ossUrl'] as String?,
      fromServer: json['fromServer'] as bool?,
      privacyPolicy: json['privacyPolicy'] as String?,
      userAgreement: json['userAgreement'] as String?,
      avatar: json['avatar'] as String?,
      appName: json['appName'] as String?,
      chatAgreement: json['chatAgreement'] as String?,
      bucket: json['bucket'] as String?,
      endpoint: json['endpoint'] as String?,
      rechargeAgreement: json['rechargeAgreement'] as String?,
      loginMethods: json['loginMethods'] as String?,
      discordUrl: json['discordUrl'] as String?,
      voiceUrl: json['voiceUrl'] as String?,
      adId: json['adId'] as String?,
      kfUrl: json['kfUrl'] as String?,
      afAppid: json['afAppid'] as String?,
      afKey: json['afKey'] as String?,
      chatLevelConf: json['chatLevelConf'] as String?,
      openScreen: json['openScreen'] as String?,
    );

Map<String, dynamic> _$ConfigModelToJson(ConfigModel instance) => <String, dynamic>{
      'ossUrl': instance.ossUrl,
      'kfUrl': instance.kfUrl,
      'privacyPolicy': instance.privacyPolicy,
      'userAgreement': instance.userAgreement,
      'rechargeAgreement': instance.rechargeAgreement,
      'avatar': instance.avatar,
      'appName': instance.appName,
      'bucket': instance.bucket,
      'discordUrl': instance.discordUrl,
      'endpoint': instance.endpoint,
      'loginMethods': instance.loginMethods,
      'voiceUrl': instance.voiceUrl,
      'fromServer': instance.fromServer,
      'chatAgreement': instance.chatAgreement,
      'adId': instance.adId,
      'afAppid': instance.afAppid,
      'afKey': instance.afKey,
      'chatLevelConf': instance.chatLevelConf,
      'openScreen': instance.openScreen,
    };

SignInitModel _$SignInitModelFromJson(Map<String, dynamic> json) => SignInitModel(
      countDown: json['countDown'] as int?,
      items: const _SignInitItemModelConverter().fromJson(jsonEncode(json['items'])),
      questsToEarn: json['questsToEarn'] as int?,
      signInTimes: json['signInTimes'] as int?,
      status: json['status'] as int?,
      vipGems: json['vipGems'] as int?,
      generalGems: json['generalGems'] as int?,
      signGems: json['signGems'] as int?,
    );

Map<String, dynamic> _$SignInitModelToJson(SignInitModel instance) => <String, dynamic>{
      'vipGems': instance.vipGems,
      'questsToEarn': instance.questsToEarn,
      'countDown': instance.countDown,
      'generalGems': instance.generalGems,
      'status': instance.status,
      'signInTimes': instance.signInTimes,
      'signGems': instance.signGems,
      'items': const _SignInitItemModelConverter().toJson(instance.items),
    };

SignInitItemModel _$SignInitItemModelFromJson(Map<String, dynamic> json) => SignInitItemModel(
      day: json['day'] as int?,
      gems: json['gems'] as int?,
      status: json['status'] as int?,
    );

Map<String, dynamic> _$SignInitItemModelToJson(SignInitItemModel instance) => <String, dynamic>{
      'day': instance.day,
      'status': instance.status,
      'gems': instance.gems,
    };
TagsItemModel _$TagsItemModelFromJson(Map<String, dynamic> json) => TagsItemModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      label: json['label'] as String?,
    );

Map<String, dynamic> _$TagsItemModelToJson(TagsItemModel instance) => <String, dynamic>{
      'id': instance.id,
      'status': instance.name,
      'gems': instance.label,
    };
