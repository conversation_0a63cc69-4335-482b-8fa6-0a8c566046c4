// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'step_content_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CloneStepContentModel _$CloneStepContentModelFromJson(Map<String, dynamic> json) =>
    CloneStepContentModel(
      type: json['type'] as int?,
      msgId: json['msgId'] as String?,
      content: json['content'] as String?,
      source: json['source'] as int?,
      specialText: json['specialText'] as String?,
      showRedo: json['showRedo'] as bool?,
      duration: json['duration'] as int?,
      optionsSelected: json['optionsSelected'] as int?,
      labelText: json['labelText'] as bool?,
      // labelList: json['labelList'] as List<dynamic>?,
      questionIndex: json['questionIndex'] as int?,
      complete: json['complete'] as bool?,
    );

Map<String, dynamic> _$CloneStepContentModelToJson(CloneStepContentModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'source': instance.source,
      'msgId': instance.msgId,
      'content': instance.content,
      'specialText': instance.specialText,
      'labelText': instance.labelText,
      'showRedo': instance.showRedo,
      'duration': instance.duration,
      'optionsSelected': instance.optionsSelected,
      // 'labelList': instance.labelList,
      'questionIndex': instance.questionIndex,
      'complete': instance.complete,
    };

CloneCreatModel _$CloneCreatModelFromJson(Map<String, dynamic> json) => CloneCreatModel(
      bgImg: json['bgImg'] as String?,
      cloneProcess: json['cloneProcess'] as String?,
      cover: json['cover'] as String?,
      nickname: json['nickname'] as String?,
      audit: json['audit'] as String?,
      voiceTypeEnum: json['voiceTypeEnum'] as String?,
      voiceName: json['voiceName'] as String?,
      recordOptions: json['recordOptions'] as int?,
      voiceParam: json['voiceParam'] as String?,
      gender: json['gender'] as int?,
      ftQuestions: const _CloneCreatModelConverter().fromJson(jsonEncode(json['ftQuestions'])),
      vtQuestions: const _CloneCreatModelConverter().fromJson(jsonEncode(json['vtQuestions'])),
    );

Map<String, dynamic> _$CloneCreatModelToJson(CloneCreatModel instance) => <String, dynamic>{
      'bgImg': instance.bgImg,
      'cloneProcess': instance.cloneProcess,
      'cover': instance.cover,
      'nickname': instance.nickname,
      'voiceTypeEnum': instance.voiceTypeEnum,
      'audit': instance.audit,
      'voiceName': instance.voiceName,
      'recordOptions': instance.recordOptions,
      'voiceParam': instance.voiceParam,
      'gender': instance.gender,
      'ftQuestions': const _CloneCreatModelConverter().toJson(instance.ftQuestions),
      'vtQuestions': const _CloneCreatModelConverter().toJson(instance.vtQuestions),
    };

CloneCreatQuestionModel _$CloneCreatQuestionModelFromJson(Map<String, dynamic> json) {
  return CloneCreatQuestionModel(
    day: json['day'] as String?,
    answer: json['answer'] as String?,
    auditUrl: json['auditUrl'] as String?,
    id: json['id'] as int?,
    questTypeEnum: json['questTypeEnum'] as String?,
    question: json['question'] as String?,
    complete: json['complete'] as bool?,
    styleType: json['styleType'] as String?,
    auditDuration: json['auditDuration'] as int?,
  );
}

Map<String, dynamic> _$CloneCreatQuestionModelToJson(CloneCreatQuestionModel instance) =>
    <String, dynamic>{
      'answer': instance.answer,
      'auditUrl': instance.auditUrl,
      'id': instance.id,
      'questTypeEnum': instance.questTypeEnum,
      'day': instance.day,
      'question': instance.question,
      'complete': instance.complete,
      'styleType': instance.styleType,
      'auditDuration': instance.auditDuration,
    };
