import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
part 'clone_list_model.gx.dart';

class CloneListModel {
  String? cover;
  int? earned;
  int? expireDay;
  int? modelId;
  String? greeting;
  String? greetingUrl;
  String? intro;
  int? likeCount;
  String? modelName;
  String? modelNo;
  String? modelProcess;
  int? purview;
  int? gender;
  int? refreshGems;

  CloneListModel({
    this.cover,
    this.earned,
    this.expireDay,
    this.greeting,
    this.greetingUrl,
    this.intro,
    this.likeCount,
    this.modelName,
    this.modelNo,
    this.modelProcess,
    this.purview,
    this.gender,
    this.refreshGems,
    this.modelId,
  });
  factory CloneListModel.fromJson(Map<String, dynamic> json) => _$CloneListModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneListModelToJson(this);
}

class CloneInfoModel {
  String? bgImg;
  int? modelId;
  int? purview;
  String? cover;
  String? intro;
  String? modelName;
  bool? privatePower;
  String? voiceParam;
  String? welcomeMsg;
  String? welcomeVoice;

  CloneInfoModel({
    this.cover,
    this.intro,
    this.modelName,
    this.purview,
    this.bgImg,
    this.modelId,
    this.privatePower,
    this.voiceParam,
    this.welcomeMsg,
    this.welcomeVoice,
  });
  factory CloneInfoModel.fromJson(Map<String, dynamic> json) => _$CloneInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneInfoModelToJson(this);
}

class CloneEditModel {
  String? appearances;
  String? audit;
  String? cloneModelProcess;
  String? cover;
  String? dialog;
  int? gender;
  String? greeting;
  String? greetingUrl;
  String? intro;
  String? inviteCode;
  String? modelName;
  String? modelNo;
  String? personaliities;
  String? preferences;
  bool? privatePower;
  int? purview;
  String? scenario;
  String? voiceTypeEnum;
  int? modelId;

  @_CloneEditImageModelConverter()
  List<CloneEditImageModel>? bgImage;
  Map? tag;

  CloneEditModel({
    this.cover,
    this.audit,
    this.voiceTypeEnum,
    this.appearances,
    this.bgImage,
    this.cloneModelProcess,
    this.dialog,
    this.gender,
    this.greeting,
    this.greetingUrl,
    this.intro,
    this.inviteCode,
    this.modelName,
    this.modelNo,
    this.personaliities,
    this.preferences,
    this.privatePower,
    this.purview,
    this.scenario,
    this.modelId,
    this.tag,
  });

  factory CloneEditModel.fromJson(Map<String, dynamic> json) => _$CloneEditModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneEditModelToJson(this);
}

class _CloneEditImageModelConverter implements JsonConverter<List<CloneEditImageModel>?, String?> {
  const _CloneEditImageModelConverter();

  @override
  List<CloneEditImageModel>? fromJson(String? json) {
    final List<CloneEditImageModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(CloneEditImageModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<CloneEditImageModel>? object) {
    throw UnimplementedError();
  }
}

class CloneEditImageModel {
  int? gems;
  String? id;
  String? img;

  CloneEditImageModel({
    this.gems,
    this.id,
    this.img,
  });
  factory CloneEditImageModel.fromJson(Map<String, dynamic> json) =>
      _$CloneEditImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneEditImageModelToJson(this);
}
