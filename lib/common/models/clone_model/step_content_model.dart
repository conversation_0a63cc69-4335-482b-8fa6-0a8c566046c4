import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:json_annotation/json_annotation.dart';
part 'step_content_model.gx.dart';

//flutter packages pub run build_runner build

// @JsonSerializable()
class CloneStepContentModel {
  int?
      type; // 1：普通文本内容 2：选项 3：带不同颜色的文本 4：带编辑的文本  5：文件 6:提示  7:录音  8:音频  9:按钮  10:标签  11:完成 12:loading
  int? source; //0：左侧消息  1：右侧消息
  String? msgId;
  String? content;
  Color? contentColor;
  Color? specialColor;
  String? specialText;
  bool? labelText; //type为4时 区分是否是可生成标签 的文本
  bool? showRedo; //是否展示重新生成
  int? duration; //音频时长
  int? optionsSelected; //选择组件选择了哪个index
  // List? labelList;
  int? questionIndex; //问题或答案 在问题列表中的下标
  bool? complete; //已完成
  Uint8List? audioBuffer;
  CloneStepContentModel({
    this.type,
    this.msgId,
    this.content,
    this.source,
    this.contentColor,
    this.specialColor,
    this.specialText,
    this.showRedo,
    this.duration,
    this.optionsSelected,
    this.labelText,
    // this.labelList,
    this.questionIndex,
    this.complete,
    this.audioBuffer,
  });

  factory CloneStepContentModel.fromJson(Map<String, dynamic> json) =>
      _$CloneStepContentModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneStepContentModelToJson(this);
}

// @JsonSerializable()
class CloneCreatModel {
  String? bgImg;
  String? cloneProcess;
  String? cover;
  String? nickname;
  String? voiceTypeEnum;
  String? audit;
  String? voiceName;
  int? recordOptions;
  String? voiceParam;
  int? gender;
  @_CloneCreatModelConverter()
  List<CloneCreatQuestionModel>? ftQuestions;
  @_CloneCreatModelConverter()
  List<CloneCreatQuestionModel>? vtQuestions;
  CloneCreatModel({
    this.bgImg,
    this.cloneProcess,
    this.cover,
    this.nickname,
    this.ftQuestions,
    this.audit,
    this.vtQuestions,
    this.voiceTypeEnum,
    this.voiceName,
    this.recordOptions,
    this.voiceParam,
    this.gender,
  });

  factory CloneCreatModel.fromJson(Map<String, dynamic> json) => _$CloneCreatModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneCreatModelToJson(this);
}

class _CloneCreatModelConverter implements JsonConverter<List<CloneCreatQuestionModel>?, String?> {
  const _CloneCreatModelConverter();

  @override
  List<CloneCreatQuestionModel>? fromJson(String? json) {
    final List<CloneCreatQuestionModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(CloneCreatQuestionModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<CloneCreatQuestionModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class CloneCreatQuestionModel {
  String? answer;
  String? auditUrl;
  int? id;
  String? questTypeEnum;
  String? day;
  String? question;
  bool? complete;
  String? styleType;
  int? auditDuration;

  CloneCreatQuestionModel({
    this.day,
    this.answer,
    this.auditUrl,
    this.id,
    this.questTypeEnum,
    this.question,
    this.complete,
    this.styleType,
    this.auditDuration,
  });
  factory CloneCreatQuestionModel.fromJson(Map<String, dynamic> json) =>
      _$CloneCreatQuestionModelFromJson(json);

  Map<String, dynamic> toJson() => _$CloneCreatQuestionModelToJson(this);
}
