// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clone_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CloneListModel _$CloneListModelFromJson(Map<String, dynamic> json) => CloneListModel(
      cover: json['cover'] as String?,
      earned: json['earned'] as int?,
      expireDay: json['expireDay'] as int?,
      greeting: json['greeting'] as String?,
      greetingUrl: json['greetingUrl'] as String?,
      intro: json['intro'] as String?,
      likeCount: json['likeCount'] as int?,
      modelName: json['modelName'] as String?,
      modelNo: json['modelNo'] as String?,
      modelProcess: json['modelProcess'] as String?,
      purview: json['purview'] as int?,
      gender: json['gender'] as int?,
      refreshGems: json['refreshGems'] as int?,
      modelId: json['modelId'] as int?,
    );

Map<String, dynamic> _$CloneListModelToJson(CloneListModel instance) => <String, dynamic>{
      'cover': instance.cover,
      'earned': instance.earned,
      'expireDay': instance.expireDay,
      'greeting': instance.greeting,
      'greetingUrl': instance.greetingUrl,
      'intro': instance.intro,
      'likeCount': instance.likeCount,
      'modelName': instance.modelName,
      'modelNo': instance.modelNo,
      'modelProcess': instance.modelProcess,
      'purview': instance.purview,
      'gender': instance.gender,
      'refreshGems': instance.refreshGems,
      'modelId': instance.modelId,
    };

CloneInfoModel _$CloneInfoModelFromJson(Map<String, dynamic> json) => CloneInfoModel(
      cover: json['cover'] as String?,
      intro: json['intro'] as String?,
      modelName: json['modelName'] as String?,
      purview: json['purview'] as int?,
      bgImg: json['bgImg'] as String?,
      modelId: json['modelId'] as int?,
      privatePower: json['privatePower'] as bool?,
      voiceParam: json['voiceParam'] as String?,
      welcomeMsg: json['welcomeMsg'] as String?,
      welcomeVoice: json['welcomeVoice'] as String?,
    );

Map<String, dynamic> _$CloneInfoModelToJson(CloneInfoModel instance) => <String, dynamic>{
      'bgImg': instance.bgImg,
      'modelId': instance.modelId,
      'purview': instance.purview,
      'cover': instance.cover,
      'intro': instance.intro,
      'modelName': instance.modelName,
      'privatePower': instance.privatePower,
      'voiceParam': instance.voiceParam,
      'welcomeMsg': instance.welcomeMsg,
      'welcomeVoice': instance.welcomeVoice,
    };

CloneEditModel _$CloneEditModelFromJson(Map<String, dynamic> json) => CloneEditModel(
      cover: json['cover'] as String?,
      audit: json['audit'] as String?,
      voiceTypeEnum: json['voiceTypeEnum'] as String?,
      appearances: json['appearances'] as String?,
      bgImage: const _CloneEditImageModelConverter().fromJson(jsonEncode(json['bgImage'])),
      cloneModelProcess: json['cloneModelProcess'] as String?,
      dialog: json['dialog'] as String?,
      gender: json['gender'] as int?,
      greeting: json['greeting'] as String?,
      greetingUrl: json['greetingUrl'] as String?,
      intro: json['intro'] as String?,
      inviteCode: json['inviteCode'] as String?,
      modelName: json['modelName'] as String?,
      modelNo: json['modelNo'] as String?,
      personaliities: json['personaliities'] as String?,
      preferences: json['preferences'] as String?,
      privatePower: json['privatePower'] as bool?,
      purview: json['purview'] as int?,
      scenario: json['scenario'] as String?,
      modelId: json['modelId'] as int?,
      tag: json['tag'] as Map?,
    );

Map<String, dynamic> _$CloneEditModelToJson(CloneEditModel instance) => <String, dynamic>{
      'appearances': instance.appearances,
      'audit': instance.audit,
      'cloneModelProcess': instance.cloneModelProcess,
      'cover': instance.cover,
      'dialog': instance.dialog,
      'gender': instance.gender,
      'greeting': instance.greeting,
      'greetingUrl': instance.greetingUrl,
      'intro': instance.intro,
      'inviteCode': instance.inviteCode,
      'modelName': instance.modelName,
      'modelNo': instance.modelNo,
      'personaliities': instance.personaliities,
      'preferences': instance.preferences,
      'privatePower': instance.privatePower,
      'purview': instance.purview,
      'scenario': instance.scenario,
      'voiceTypeEnum': instance.voiceTypeEnum,
      'modelId': instance.modelId,
      'tag': instance.tag,
      'bgImage': const _CloneEditImageModelConverter().toJson(instance.bgImage),
    };

CloneEditImageModel _$CloneEditImageModelFromJson(Map<String, dynamic> json) => CloneEditImageModel(
      gems: json['gems'] as int?,
      id: json['id'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$CloneEditImageModelToJson(CloneEditImageModel instance) => <String, dynamic>{
      'gems': instance.gems,
      'id': instance.id,
      'img': instance.img,
    };
