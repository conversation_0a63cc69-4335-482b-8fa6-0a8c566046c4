// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_bank_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VoiceBankModel _$VoiceBankModelFromJson(Map<String, dynamic> json) => VoiceBankModel(
      famale: const _VoiceBankItemModelConverter().from<PERSON>son(jsonEncode(json['famale'])),
      male: const _VoiceBankItemModelConverter().from<PERSON><PERSON>(jsonEncode(json['male'])),
    );

Map<String, dynamic> _$VoiceBankModelToJson(VoiceBankModel instance) => <String, dynamic>{
      'famale': const _VoiceBankItemModelConverter().toJson(instance.famale),
      'male': const _VoiceBankItemModelConverter().toJson(instance.male),
    };

VoiceBankItemModel _$VoiceBankItemModelFromJson(Map<String, dynamic> json) => VoiceBankItemModel(
      cateName: json['cateName'] as String?,
      sort: json['sort'] as int?,
      voice: const _VoiceBankItemVoiceModelConverter().fromJson(jsonEncode(json['voice'])),
    );

Map<String, dynamic> _$VoiceBankItemModelToJson(VoiceBankItemModel instance) => <String, dynamic>{
      'cateName': instance.cateName,
      'sort': instance.sort,
      'voice': const _VoiceBankItemVoiceModelConverter().toJson(instance.voice),
    };

VoiceBankItemVoiceModel _$VoiceBankItemVoiceModelFromJson(Map<String, dynamic> json) =>
    VoiceBankItemVoiceModel(
      voiceName: json['voiceName'] as String?,
      voiceUrl: json['voiceUrl'] as String?,
      voiceVal: json['voiceVal'] as String?,
    );

Map<String, dynamic> _$VoiceBankItemVoiceModelToJson(VoiceBankItemVoiceModel instance) =>
    <String, dynamic>{
      'voiceName': instance.voiceName,
      'voiceUrl': instance.voiceUrl,
      'voiceVal': instance.voiceVal,
    };
