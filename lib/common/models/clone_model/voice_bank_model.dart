import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
part 'voice_bank_model.gx.dart';

// @JsonSerializable()
class VoiceBankModel {
  @_VoiceBankItemModelConverter()
  List<VoiceBankItemModel>? famale;
  @_VoiceBankItemModelConverter()
  List<VoiceBankItemModel>? male;
  VoiceBankModel({
    this.famale,
    this.male,
  });

  factory VoiceBankModel.fromJson(Map<String, dynamic> json) => _$VoiceBankModelFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceBankModelToJson(this);
}

class _VoiceBankItemModelConverter implements JsonConverter<List<VoiceBankItemModel>?, String?> {
  const _VoiceBankItemModelConverter();

  @override
  List<VoiceBankItemModel>? fromJson(String? json) {
    final List<VoiceBankItemModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(VoiceBankItemModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<VoiceBankItemModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class VoiceBankItemModel {
  String? cateName;
  int? sort;
  @_VoiceBankItemVoiceModelConverter()
  List<VoiceBankItemVoiceModel>? voice;
  VoiceBankItemModel({
    this.cateName,
    this.sort,
    this.voice,
  });
  factory VoiceBankItemModel.fromJson(Map<String, dynamic> json) =>
      _$VoiceBankItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceBankItemModelToJson(this);
}

class _VoiceBankItemVoiceModelConverter
    implements JsonConverter<List<VoiceBankItemVoiceModel>?, String?> {
  const _VoiceBankItemVoiceModelConverter();

  @override
  List<VoiceBankItemVoiceModel>? fromJson(String? json) {
    final List<VoiceBankItemVoiceModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(VoiceBankItemVoiceModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<VoiceBankItemVoiceModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class VoiceBankItemVoiceModel {
  String? voiceName;
  String? voiceUrl;
  String? voiceVal;
  VoiceBankItemVoiceModel({
    this.voiceName,
    this.voiceUrl,
    this.voiceVal,
  });
  factory VoiceBankItemVoiceModel.fromJson(Map<String, dynamic> json) =>
      _$VoiceBankItemVoiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceBankItemVoiceModelToJson(this);
}
