// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_task.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskModel _$TaskModelFromJson(Map<String, dynamic> json) => TaskModel(
      newbie: json['newbie'] == null
          ? null
          : MissionTypeModel.fromJson(json['newbie'] as Map<String, dynamic>),
      cycle: json['cycle'] == null
          ? null
          : MissionTypeModel.fromJson(json['cycle'] as Map<String, dynamic>),
      onGoing: json['onGoing'] as int?,
      available: json['available'] as int?,
      actionable: json['actionable'] as int?,
    );

Map<String, dynamic> _$TaskModelToJson(TaskModel instance) => <String, dynamic>{
      'actionable': instance.actionable,
      'available': instance.available,
      'onGoing': instance.onGoing,
      'cycle': instance.cycle,
      'newbie': instance.newbie,
    };
MissionTypeModel _$MissionTypeModelFromJson(Map<String, dynamic> json) => MissionTypeModel(
      missionList: const _TaskListItemModelConverter().fromJson(jsonEncode(json['missionList'])),
      count: json['count'] as int?,
    );

Map<String, dynamic> _$MissionTypeModelToJson(MissionTypeModel instance) => <String, dynamic>{
      'count': instance.count,
      'missionList': const _TaskListItemModelConverter().toJson(instance.missionList),
    };

TaskListItemModel _$TaskListItemModelFromJson(Map<String, dynamic> json) => TaskListItemModel(
      content: json['content'] as String?,
      gems: json['gems'] as int?,
      icon: json['icon'] as String?,
      id: json['id'] as int?,
      state: json['state'] as String?,
      taskLabel: json['taskLabel'] as String?,
      taskType: json['taskType'] as String?,
      title: json['title'] as String?,
      canDoAfterTime: json['canDoAfterTime'] as int?,
      adWatchedCount: json['adWatchedCount'] as int?,
    );

Map<String, dynamic> _$TaskListItemModelToJson(TaskListItemModel instance) => <String, dynamic>{
      'content': instance.content,
      'gems': instance.gems,
      'icon': instance.icon,
      'id': instance.id,
      'state': instance.state,
      'taskLabel': instance.taskLabel,
      'taskType': instance.taskType,
      'title': instance.title,
      'adWatchedCount': instance.adWatchedCount,
      'canDoAfterTime': instance.canDoAfterTime,
    };
