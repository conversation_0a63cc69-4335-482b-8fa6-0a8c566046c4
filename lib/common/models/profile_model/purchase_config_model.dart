import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'purchase_config_model.gx.dart';

// @JsonSerializable()
class PurchaseConfigModel {
  VipTypeItem? ssvip;
  VipTypeItem? vip;
  VipTypeItem? gem;

  PurchaseConfigModel({
    this.vip,
    this.ssvip,
    this.gem,
  });
  factory PurchaseConfigModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseConfigModelToJson(this);
}

// @JsonSerializable()
class VipTypeItem {
  bool? discounts;
  int? discountPrice;
  @_PurchaseItemModelConverter()
  List<PurchaseItemModel>? recharges;

  VipTypeItem({
    this.recharges,
    this.discounts,
    this.discountPrice,
  });
  factory VipTypeItem.fromJson(Map<String, dynamic> json) =>
      _$VipTypeItemFromJson(json);

  Map<String, dynamic> toJson() => _$VipTypeItemToJson(this);
}

class _PurchaseItemModelConverter
    implements JsonConverter<List<PurchaseItemModel>?, String?> {
  const _PurchaseItemModelConverter();

  @override
  List<PurchaseItemModel>? fromJson(String? json) {
    final List<PurchaseItemModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(PurchaseItemModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<PurchaseItemModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class PurchaseItemModel {
  bool? commend;
  double? finalPrice;
  String? goodsNo;
  int? num;
  double? originalPrice;
  double? discountPrice;
  String? rechargeType;
  String? title;
  String? des;
  bool? showCountDown;
  int? countdown;
  String? tag;
  String? unitPrice;
  bool? selected;
  String? save;
  int? gems;
  String? subscriptionCycle;
  String? subscriptionType;
  bool? hot;

  PurchaseItemModel({
    this.title,
    this.commend,
    this.finalPrice,
    this.goodsNo,
    this.num,
    this.originalPrice,
    this.rechargeType,
    this.des,
    this.showCountDown,
    this.countdown,
    this.selected,
    this.save,
    this.tag,
    this.gems,
    this.discountPrice,
    this.unitPrice,
    this.hot,
    this.subscriptionCycle,
    this.subscriptionType,
  });
  factory PurchaseItemModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseItemModelToJson(this);
}
