import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'profile_task.gx.dart';

//flutter packages pub run build_runner build
// @JsonSerializable()
class TaskModel {
  int? actionable;
  int? available;
  int? onGoing;
  MissionTypeModel? cycle;
  MissionTypeModel? newbie;
  TaskModel({
    this.newbie,
    this.cycle,
    this.onGoing,
    this.available,
    this.actionable,
  });
  factory TaskModel.fromJson(Map<String, dynamic> json) => _$TaskModelFromJson(json);

  Map<String, dynamic> toJson() => _$TaskModelToJson(this);

  List<TaskListItemModel> get cycleList =>
      (cycle == null || cycle!.missionList == null || cycle!.missionList!.isEmpty)
          ? []
          : cycle!.missionList!;
  List<TaskListItemModel> get newbieList =>
      (newbie == null || newbie!.missionList == null || newbie!.missionList!.isEmpty)
          ? []
          : newbie!.missionList!;
}

// @JsonSerializable()
class MissionTypeModel {
  int? count;
  @_TaskListItemModelConverter()
  List<TaskListItemModel>? missionList;
  MissionTypeModel({
    this.missionList,
    this.count,
  });
  factory MissionTypeModel.fromJson(Map<String, dynamic> json) => _$MissionTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$MissionTypeModelToJson(this);
}

class _TaskListItemModelConverter implements JsonConverter<List<TaskListItemModel>?, String?> {
  const _TaskListItemModelConverter();

  @override
  List<TaskListItemModel>? fromJson(String? json) {
    final List<TaskListItemModel> models = [];
    if (json != null) {
      final value = jsonDecode(json);
      if (value is List) {
        for (final element in value) {
          if (element is Map<String, dynamic>) {
            models.add(TaskListItemModel.fromJson(element));
          }
        }
      }
    }
    return models;
  }

  @override
  String? toJson(List<TaskListItemModel>? object) {
    throw UnimplementedError();
  }
}

// @JsonSerializable()
class TaskListItemModel {
  String? content;
  int? gems;
  String? icon;
  int? id;
  String? state;
  String? taskLabel;
  String? taskType;
  String? title;
  int? canDoAfterTime;
  int? adWatchedCount;

  TaskListItemModel({
    this.content,
    this.gems,
    this.icon,
    this.id,
    this.state,
    this.taskLabel,
    this.taskType,
    this.title,
    this.adWatchedCount,
    this.canDoAfterTime,
  });
  factory TaskListItemModel.fromJson(Map<String, dynamic> json) =>
      _$TaskListItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$TaskListItemModelToJson(this);
}
