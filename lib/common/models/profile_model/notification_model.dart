part 'notification_model.gs.dart';

class NotficationModel {
  int? creatTime;
  String? icon;
  String? message;
  String? title;
  String? button;
  String? jumpUrl;
  int? gems;
  int? claimStatus;
  int? msgId;
  String? messageType;

  NotficationModel({
    this.creatTime,
    this.icon,
    this.message,
    this.title,
    this.button,
    this.claimStatus,
    this.gems,
    this.jumpUrl,
    this.messageType,
    this.msgId,
  });
  factory NotficationModel.fromJson(Map<String, dynamic> json) => _$NotficationModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotficationModelToJson(this);
}
