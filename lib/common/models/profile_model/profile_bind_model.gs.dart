part of 'profile_bind_model.dart';

BindInfoModel _$BindInfoModelFromJson(Map<String, dynamic> json) => BindInfoModel(
      avatarUrl: json['avatarUrl'] as String?,
      bindAccount: json['bindAccount'] as String?,
      bindType: json['bindType'] as String?,
      hasBind: json['hasBind'] as int?,
    );

Map<String, dynamic> _$BindInfoModelToJson(BindInfoModel instance) => <String, dynamic>{
      'avatarUrl': instance.avatarUrl,
      'bindAccount': instance.bindAccount,
      'bindType': instance.bindType,
      'hasBind': instance.hasBind,
    };
