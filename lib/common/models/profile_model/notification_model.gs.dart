part of 'notification_model.dart';

NotficationModel _$NotficationModelFromJson(Map<String, dynamic> json) => NotficationModel(
      creatTime: json['creatTime'] as int?,
      icon: json['icon'] as String?,
      message: json['message'] as String?,
      title: json['title'] as String?,
      button: json['button'] as String?,
      jumpUrl: json['jumpUrl'] as String?,
      gems: json['gems'] as int?,
      claimStatus: json['claimStatus'] as int?,
      msgId: json['msgId'] as int?,
      messageType: json['messageType'] as String?,
    );

Map<String, dynamic> _$NotficationModelToJson(NotficationModel instance) => <String, dynamic>{
      'creatTime': instance.creatTime,
      'icon': instance.icon,
      'message': instance.message,
      'title': instance.title,
      'button': instance.button,
      'jumpUrl': instance.jumpUrl,
      'gems': instance.gems,
      'claimStatus': instance.claimStatus,
      'messageType': instance.messageType,
      'msgId': instance.msgId,
    };
