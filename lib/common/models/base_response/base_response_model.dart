import 'dart:convert';

/// 分页类型
class Page {
  late int pages;
  late int pageSize;
  late int total;
  late int pageNo;

  Page({
    required this.pageSize,
    required this.total,
    required this.pages,
    required this.pageNo,
  });

  factory Page.fromJson(Map<String, dynamic> json) {
    return Page(
      pageSize: json['pageSize'] as int,
      total: json['total'] as int,
      pages: json['pages'] as int,
      pageNo: json['pageNo'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      "pageSize": pageSize,
      "total": total,
      "pages": pages,
      "pageNo": pageNo,
    };
  }
}

/// 响应体基类型
class BaseResponse {
  late int code;
  late dynamic data;
  late bool? success;
  late String? msg;
  late Page? page;

  BaseResponse({
    required this.code,
    required this.data,
    required this.msg,
    required this.page,
    required this.success,
  });

  factory BaseResponse.fromJson(Map<String, dynamic> json) {
    return BaseResponse(
      code: json['code'] as int,
      msg: json['msg'],
      data: json['data'],
      page: json['page'] == null
          ? null
          : Page.fromJson(json['page'].runtimeType == String
              ? jsonDecode(json['page'])
              : json['page'] as Map<String, dynamic>),
      success: json['success'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'code': code,
      'msg': msg,
      'page': page == null ? '' : jsonEncode(page!.toJson()),
      'data': data,
      'success': success,
    };
  }
}
