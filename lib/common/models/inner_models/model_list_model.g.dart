// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'model_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ModelListModel _$ModelListModelFromJson(Map<String, dynamic> json) => ModelListModel(
      cover: json['cover'] as String?,
      modelId: (json['modelId'] as num?)?.toInt(),
      author: json['author'] as String?,
      likeCount: (json['likeCount'] as num?)?.toInt(),
      modelName: json['modelName'] as String?,
      modelSource: json['modelSource'] as String?,
      subName: json['subName'] as String?,
      useCount: (json['useCount'] as num?)?.toInt(),
      callTypes: json['callTypes'] as List<dynamic>?,
      intro: json['intro'] as String?,
      modelNo: json['modelNo'] as String?,
      top: json['top'] as bool?,
      modelStatus: (json['modelStatus'] as num?)?.toInt(),
      modelType: json['modelType'] as String?,
      welcomeMsg: json['welcomeMsg'] as String?,
      welcomeVoice: json['welcomeVoice'] as String?,
      voicePlayStatus: (json['voicePlayStatus'] as num?)?.toInt(),
      voiceParam: json['voiceParam'] as String?,
      voiceParamEn: json['voiceParamEn'] as String?,
      isAlterEgo: json['isAlterEgo'] as bool?,
      hasLike: json['hasLike'] as bool?,
      purview: (json['purview'] as num?)?.toInt(),
      clothe: json['clothe'] as bool?,
    );

Map<String, dynamic> _$ModelListModelToJson(ModelListModel instance) => <String, dynamic>{
      'cover': instance.cover,
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'modelSource': instance.modelSource,
      'subName': instance.subName,
      'intro': instance.intro,
      'modelNo': instance.modelNo,
      'modelType': instance.modelType,
      'welcomeVoice': instance.welcomeVoice,
      'welcomeMsg': instance.welcomeMsg,
      'author': instance.author,
      'modelStatus': instance.modelStatus,
      'useCount': instance.useCount,
      'likeCount': instance.likeCount,
      'voicePlayStatus': instance.voicePlayStatus,
      'callTypes': instance.callTypes,
      'hasLike': instance.hasLike,
      'top': instance.top,
      'voiceParam': instance.voiceParam,
      'voiceParamEn': instance.voiceParamEn,
      'isAlterEgo': instance.isAlterEgo,
      'purview': instance.purview,
      'clothe': instance.clothe,
    };
