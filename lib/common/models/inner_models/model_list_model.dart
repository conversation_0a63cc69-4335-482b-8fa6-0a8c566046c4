import 'package:json_annotation/json_annotation.dart';
part 'model_list_model.g.dart';

@JsonSerializable()
class ModelListModel {
  String? cover;
  int? modelId;
  String? modelName;
  String? modelSource;
  String? subName;
  String? intro;
  String? modelNo;
  String? modelType;
  String? welcomeVoice;
  String? welcomeMsg;
  String? author;
  int? modelStatus;
  int? useCount;
  int? likeCount;
  int? voicePlayStatus; //1：正在播放
  List? callTypes;
  bool? hasLike;
  bool? top;
  //发音人
  String? voiceParam;
  String? voiceParamEn;
  bool? isAlterEgo;
  int? purview;
  bool? clothe;
  ModelListModel({
    this.cover,
    this.modelId,
    this.author,
    this.likeCount,
    this.modelName,
    this.modelSource,
    this.subName,
    this.useCount,
    this.callTypes,
    this.intro,
    this.modelNo,
    this.top,
    this.modelStatus,
    this.modelType,
    this.welcomeMsg,
    this.welcomeVoice,
    this.voicePlayStatus,
    this.voiceParam,
    this.voiceParamEn,
    this.isAlterEgo,
    this.hasLike,
    this.purview,
    this.clothe,
  });

  factory ModelListModel.fromJson(Map<String, dynamic> json) => _$ModelListModelFromJson(json);

  Map<String, dynamic> toJson() => _$ModelListModelToJson(this);
}
