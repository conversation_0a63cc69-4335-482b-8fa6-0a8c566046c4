///存储APP信息的key
const String spAppInfo = "app_package_info";

///存储设备信息的key
const String spDeviceInfo = "app_device_info";

///设备信息是否上报
const String spDeviceInfoCommit = 'device_info_commit';

///用户登录信息
const String spLoginInfo = 'login_info';

/// 设备id
const String spDeviceId = 'app_device_id';

/// 记录收到角色消息前是否点击播放音频（所有角色）
const String spOntapPlayMsgVoice = 'ontap_play_msg_voice';

/// 统计哪些session自动播放过第一条打招呼的语音
const String spMsgAutoPlay = 'msg_auto_play';

/// 第一次打开APP时自动设备登录进行
const String spNewUserDeviceLogin = 'new_user_device_login';

/// 会话低评分弹出反馈统计
const String spSessionScoreReport = 'session_score_report';

/// 首页角色 首次展示时自动播放语音
const String spAmorsAutoPlayVoice = 'amors_auto_play_voice';

/// 是否开启震动
const String spHipticsValue = 'hiptics_value';

/// 设备登录用户 充值成为会员后 提示绑定第三方账号 每日一次
const String spDeviceUserLoginTip = 'device_user_login_tip';

/// 设备登录用户 进入设置用户页 提示绑定第三方账号 每日一次
const String spDeviceUserBindTip = 'device_user_bind_tip';

/// 我的/聊天页面弹出签到  每日一次
const String spSigninDialog = 'signin_dialog';

/// 购买页优惠弹窗  每日一次
const String spPuchaseOfferDialog = 'puchase_offer_dialog';

/// 购买页记录优惠倒计时
const String spPuchaseOfferCountdown = 'puchase_offer_countdown';

/// 购买页关闭的时间
const String spPuchaseCloseTime = 'puchase_close_time';

/// 内购订单
const String spInAppPurchaseOrder = 'puchase_order';

///push_token
const String spPushToken = "push_token";

///创建、编辑克隆 第一次不展示hot图标
const String spCreatCloneShowHot = "creat_clone_show_hot";

/// 克隆页弹出收益  每日一次
const String spKloneDialog = 'klone_dialog';

/// install事件是否上报
const String spInstallEvent = 'app_install_event';

/// tb上报失败事件
const String spTbEventfail = 'app_tb_event_fail';

/// adjust id 是否上报
const String spAdjustIdEvent = 'upload_adjust_id';

/// network time
const String spNetworkStatusTime = 'network_status_time';

///记录推荐列表最后一个角色ID
const String spExposureModel = 'exposure_model_item';

///不再展示注册后的引导评分
const String spRegisterMark = 'register_mark';

///新用户进入聊天页弹出设置昵称和头像
const String spChatSetProfile = 'chat_set_profile';

///消息评分、超过发送消息数量 弹出评分
const String spChatShowScore = 'chat_show_score';

///消息发送统计  10条弹出评分
const String spChatMessageCount = 'chat_message_count';

///缓存头像和昵称
const String spCacheNicknameAvatar = "cache_nickname_avatar";

///每日首次弹出购买页
const String spDailyShowSale = "daily_show_sale";

///看广告解锁角色倒计时
const String spAdUnlockRole = "ad_unlock_role";

///用户vip信息
const String spUserVipInfo = 'user_vip_info';

///订阅成功后进入个人中心展示动画
const String spSubscriptionAnimation = 'mine_page_subscription_animation';

///通过通知唤起app，获取会话ID，进行跳转
const String spNotificationSessionNo = 'notification_session_no';

///聊天页展示undress按钮
const String spChatShowUndress = "chat_show_undress";

///首页moments是否点击过
const String spHomeMomentsOntap = "home_moments_ontap";

///首页create是否点击过
const String spHomeCreateOntap = "home_create_ontap";

///是否通过深度链接打开app
const String spFromDeepLink = "from_deep_link";

///聊天翻译提示开通自动翻译
const String spKeyMsgAutoTranslateTip = "msg_auto_translate_tip";
