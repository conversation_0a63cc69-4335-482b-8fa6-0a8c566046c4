import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/http_util/http_util.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:get/get.dart';

class CommonApis {
  //配置信息
  static Future<ConfigModel?> getInitConfig() async {
    final resp = await ApiRequest.post(api: Api.initConfig, loding: false);
    return resp == null || resp.data == null ? null : ConfigModel.fromJson(resp.data!);
  }

  //设备信息
  static Future<bool> deviceInfo() async {
    final packageModel =
        PackageInfoModel.fromJson(Get.find<SPService>().get(spAppInfo) as Map<String, dynamic>);
    final devideModel =
        DeviceInfoModel.fromJson(Get.find<SPService>().get(spDeviceInfo) as Map<String, dynamic>);
    Map<String, dynamic> params = {
      'appVersion': packageModel.version,
      'deviceBrand': devideModel.brand,
      'osVersion': devideModel.systemVersion
    };

    final resp = await ApiRequest.post(api: Api.deviceInfo, params: params, loding: false);
    return resp == null ? false : resp.success!;
  }

  //分享
  static Future<Map?> commonShare({int? modelId}) async {
    final resp = await ApiRequest.post(
        api: modelId == null ? Api.commonShare : '${Api.commonShare}?modelId=$modelId',
        loding: true);
    return (resp == null || resp.data == null) ? null : resp.data;
  }

  //图片检测
  static Future<bool> imageCheck(
      {bool loding = false, required String url, required String contentCheckSource}) async {
    Map<String, dynamic> params = {'image': url, 'contentCheckSource': contentCheckSource};
    final resp = await ApiRequest.post(api: Api.imageCheck, params: params, loding: loding);
    return resp == null ? false : resp.success!;
  }

  //签到信息
  static Future<SignInitModel?> signinInit({bool loading = true}) async {
    final resp = await ApiRequest.post(api: Api.signinInit, loding: loading);
    return resp == null || resp.data == null ? null : SignInitModel.fromJson(resp.data!);
  }

  //签到
  static Future<Map?> signin({required String signInType}) async {
    Map<String, dynamic> params = {'signInType': signInType};
    final resp = await ApiRequest.post(api: Api.signin, params: params, loding: true);
    return (resp == null || resp.data == null) ? null : resp.data;
  }

  //设备信息
  static Future<bool> submitPushToken() async {
    String? cid = Get.find<SPService>().get(spPushToken) as String?;
    if (cid == null) {
      return Future.value(false);
    }
    Map<String, dynamic> params = {'cid': cid};
    final resp = await ApiRequest.post(api: Api.registerCid, params: params, loding: false);
    return resp == null ? false : resp.success ?? false;
  }
  // //弹窗
  // static Future<Map?> getUserPop() async {
  //   final resp = await ApiRequest.get(api: Api.userPop, loding: false);
  //   return resp == null || resp.data == null ? null : resp.data;
  // }

  // //获取标签
  // static Future<List> getLabels() async {
  //   final resp = await ApiRequest.get(api: Api.getLabels, loding: false);
  //   return resp == null || resp.data == null ? null : resp.data;
  // }

  // //引导页设置
  // static Future<Map?> setGuideInfo({String? nickname, List? labels}) async {
  //   final resp = await ApiRequest.post(
  //       api: Api.sttGuide, params: {"nickname": nickname, "labels": labels ?? []}, loding: true);
  //   return resp == null || resp.data == null ? null : resp.data;
  // }
  //审核信息
  static Future<bool?> getAuditConfig() async {
    final resp = await ApiRequest.get(api: Api.initAudit, loding: false);
    return resp?.data;
  }

  //操作统计
  static operationReport({
    required String page,
    required String action,
    required String eventLogType,
    String? value,
  }) {
    Future.delayed(const Duration(milliseconds: 1000), () {
      Map<String, dynamic> params = {
        'action': action,
        'page': page,
        'value': value,
        'eventLogType': eventLogType,
      };
      HttpUtils().post(api: Api.operationReport, params: params, headers: {});
    });
  }

  //提交 adjust ID
  static Future<bool> registerAdid({required String adid, String? gpsAdid}) async {
    final resp = await ApiRequest.post(
        api: Api.registerAdid, params: {'adid': adid, 'gpsAdid': gpsAdid}, loding: false);
    return resp?.success ?? false;
  }

  //获取所有标签
  static Future<List<TagsItemModel>> tagsQuery(
      {Function(List<TagsItemModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
        api: Api.tagsQuery,
        loding: false,
        cacheCallBack: cacheCallBack == null
            ? null
            : (cacheResp) {
                cacheCallBack.call(
                  cacheResp == null || cacheResp.data == null || cacheResp.data['tags'] == null
                      ? []
                      : (cacheResp.data['tags'] as List).map((e) {
                          return TagsItemModel.fromJson(e);
                        }).toList(),
                );
              });
    return resp == null || resp.data == null || resp.data['tags'] == null
        ? []
        : (resp.data['tags'] as List).map((e) {
            return TagsItemModel.fromJson(e);
          }).toList();
  }

  static Future<List?> cacheNicknameAvatar() async {
    final resp = await ApiRequest.get(api: Api.prepareProfile, loding: false);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //举报作者
  static Future<bool> authorReportBlock({required String modelId, required int type}) async {
    final resp = await ApiRequest.post(
        api: type == 0 ? Api.authorReport(modelId) : Api.authorBlock(modelId), loding: true);
    return resp?.success ?? false;
  }

  //记录每日登录
  static Future<bool> dailyReport() async {
    final resp = await ApiRequest.get(api: Api.dailyReport, loding: false);
    return resp?.success ?? false;
  }
}
