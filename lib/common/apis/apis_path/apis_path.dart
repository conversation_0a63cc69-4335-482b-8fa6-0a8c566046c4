class Api {
  // Api._();

  // 开发环境IP地址
  // static const String service = '**************:9001';
  // static const String service = 'server.dev.net:9002';
  // static const String service = 'ls-test.cpolar.top';

  //线上环境
  static const String service = 'backend-api.amorai.net';

  //http请求接口
  static const String baseUrl = 'https://$service';
  //角色聊天socket
  static const String modelChatSocketUrl = 'wss://$service/amor/ws/chat';

  //TBA  Android
  //测试地址
  static const String tbaAndroidTestUrl = 'https://test-pipe.amorai.net/company/unwieldy';
  //正式地址
  static const String tbaAndroidRegularUrl = 'https://pipe.amorai.net/verbose/appendix';
  //cloak测试/正式地址
  static const String tbaAndroidCloakUrl = 'https://mastiff.amorai.net/ecuador/robbin';

  //TBA  iOS
  //测试地址
  static const String tbaIOSTestUrl = 'https://test-too.amorai.net/logician/kerygma';
  //正式地址
  static const String tbaIOSRegularUrl = 'https://too.amorai.net/razor/mangy';
  //cloak测试/正式地址
  static const String tbaIOSCloakUrl = 'https://incense.amorai.net/genoa/tardy';

  /// 获取配置
  static const String initConfig = '/amor/common/init-config';

  ///第三方登录
  static const String oauthLogin = '/amor/auth/login/third';

  ///发送验证码
  static const String loginEmailSend = '/amor/auth/login/send-email';

  ///验证码登录
  static const String loginEmail = '/amor/auth/login/verify-email';

  ///用户信息
  static const String userInfo = '/amor/user/info';

  ///刷新对话
  static const String chatRefresh = '/amor/chat/refresh';

  ///购买会员配置
  static const String rechargeConfigVip = '/amor/recharge/config/vip/v1';

  ///购买宝石配置
  static const String rechargeConfigGem = '/amor/recharge/config/gem';

  ///充值
  static const String rechargeCreat = '/amor/recharge/create-order';

  ///设备信息
  static const String deviceInfo = '/amor/device/save-device-info';

  ///注销账户
  static const String unsubscribe = '/amor/setting/account/unsubscribe';

  ///用户个人信息修改
  static const String profileUpdate = '/amor/setting/profile/update';

  ///退出登录
  static const String authLogout = '/amor/auth/logout';

  ///对话列表
  static String sessionList = '/amor/session/list';

  ///删除对话
  static String sessionDel = '/amor/session/del';

  ///对话置顶
  static String sessionTop = '/amor/session/top';

  ///对话取消置顶
  static String sessionCancelTop = '/amor/session/cancel-top';

  ///对话修改标题
  static String sessionUpdateTitle = '/amor/session/update-title';

  ///设置语音
  static String setMsgVoice = '/amor/chat/setMsgVoice';

  ///角色分类
  static String rolesCategorys = '/amor/roles/categorys';

  ///文字转语音
  static String textToVoice = '/amor/voice/tts-download';

  ///会话配置
  static String sessionConfig = '/amor/session/session-config';

  ///角色搜索
  static String modelSearch = '/amor/model/search';

  ///我的角色
  static String mineModelList = '/amor/model/list';

  ///我的角色-删除角色
  static String mineModelDel = '/amor/model/del';

  ///对话消息点赞-点踩
  static String chatMessageLike = '/amor/chat/like-or-dislike';

  ///AI停止生成
  static String chatAiStopGenerate = '/amor/session/stop-generate';

  ///提交推送ID
  static String registerCid = '/amor/common/register-cid';

  /// 新用户引导页 设置信息
  static String sttGuide = '/amor/setting/guide';

  /// 新用户引导页 获取标签列表
  static String getLabels = '/amor/setting/labels';

  ///会话背景列表
  static String sessionChatBgList(int modelId) => '/amor/session/bgList/$modelId';

  ///会话设置(背景图)
  static String sessionChatSet = '/amor/model/updateUserSetting';

  /// 角色详情
  static String getModelProfile(String modelId) => '/amor/model/model-profile/$modelId';

  ///聊天记录
  static String userChatRefresh = '/amor/user/chat/refresh';

  ///图片检测
  static String imageCheck = '/amor/common/checkImage';

  ///分享
  static String commonShare = '/amor/common/share';

  ///重新生成
  static String chatRegenerate = '/amor/chat/regenerate';

  ///发送短信验证码
  static String sendSms = '/amor/captcha/app/send';

  ///短信验证码登录
  static String smsCodeLogin = '/amor/auth/login/captcha';

  ///操作统计
  static String operationReport = '/amor/device/save-analysis-log';

  ///ai生成昵称
  static String genNickname(int gender) => '/amor/setting/gen-nickname?gender=$gender';

  ///注册后设置昵称和性别
  static String initUser = '/amor/setting/init-user';

  ///注册后获取问题数据
  static String questionGet = '/amor/question/get';

  ///注册后提交问题数据
  static String questionCommit = '/amor/question/commit';

  ///设备登录
  static String loginDevice = '/amor/auth/login/device';

  ///文字转语音流式返回
  static String ttsStream = '/api/stream_merge';

  ///聊过的角色列表
  static String chatted = '/amor/user/chatted';

  ///聊过的角色列表 置顶
  static String chattedTop = '/amor/user/chatted-top';

  ///通知消息列表
  static String noticeList = '/amor/notification/list';

  ///nsfw开关
  static String nsfwSwitch = '/amor/session/content-category-switch';

  ///重置会话
  static String sessionReset = '/amor/session/reset';

  ///举报配置列表
  static String reportInfo = '/amor/report/info';

  ///举报
  static String reportPromble = '/amor/report/problem';

  ///购买背景
  static String bgPurchase = '/amor/session/bgPurchase';

  ///选择背景
  static String bgActivate = '/amor/session/bgActivate';

  ///任务列表
  static String missionBoard = '/amor/task/board/v2';

  ///领取任务奖励
  static String missionClaim = '/amor/task/claim';

  ///完成评分任务
  static String missionTheFun = '/amor/task/theFun';

  ///任务跳转聊天页 获取sessionNo
  static String missionSessionNo = '/amor/session/latest';

  ///动作提示剩余的次数、倒计时（get）、看完广告后增加次数（post）
  static String chatActionTipsSet = '/amor/session/inspiration-ad-count';

  ///动作提示
  static String chatActionTips = '/amor/session/action-tips/v2';

  ///查询第三方或邮箱、手机绑定
  static String accountBindGet = '/amor/setting/account/bind/get';

  ///绑定邮箱或者手机
  static String bindPhoneOrEmail = '/amor/setting/account/bind/phoneOrEmail';

  ///绑定三方
  static String bindOauth = '/amor/setting/account/bind/oauth';

  ///验证账户是否已注册
  static String authAccountExist = '/amor/auth/account/exist';

  // new

  ///角色推荐
  static String modelFeed = '/amor/model/feed';

  ///角色跳转聊天获取sessionNo
  static String modelCreatSession = '/amor/session/create-session';

  ///角色点赞
  static String dolike = '/amor/model/dolike';

  ///我喜欢的角色
  static String likedModel = '/amor/user/likes';

  ///评分
  static String chatScore = '/amor/chat/score';

  ///角色分类类别
  static String classification = '/amor/tags/user-tags';

  ///分类数据列表
  static String categorysL2All(String l2CateId) => '/amor/tags/user-tags/$l2CateId';

  ///解锁角色
  static String unlockTight = '/amor/model/unlock-tight';

  ///签到信息
  static String signinInit = '/amor/sign-in/init';

  ///签到
  static String signin = '/amor/sign-in/do';

  ///消息领取奖励
  static String notificationClaim = '/amor/notification/claim';

  ///随机获取角色
  static String modelRecommend = '/amor/model/recommend';

  ///ios内购验证
  static String iosGemVerify = '/amor/pay/gem/apple/verify';

  ///ios订阅验证
  static String iosVipVerify = '/amor/pay/vip/apple/verify';

  ///Google内购验证
  static String googleGemVerify = '/amor/pay/gem/google/verify';

  ///Google订阅验证
  static String googleVipVerify = '/amor/pay/vip/google/verify';

  ///克隆 设置基础信息
  static String cloneInit = '/amor/clone/init';

  ///克隆 随机基础信息
  static String cloneRandomProfile = '/amor/clone/random-profile';

  ///克隆 获取基本信息和进度
  static String cloneProcess = '/amor/clone/process';

  ///克隆 声音库
  static String cloneVoiceBank = '/amor/clone/voice-bank';

  ///克隆 重新生成答案
  static String cloneRegenerateAnswer = '/amor/clone/regenerate-answer';

  ///克隆 重新生成答案
  static String cloneSaveProcess = '/amor/clone/save-process';

  ///克隆 列表
  static String cloneList = '/amor/clone/list';

  ///克隆 刷新
  static String cloneRefresh = '/amor/clone/refresh';

  ///克隆 预览
  static String cloneWrap(String modelNo) => '/amor/clone/cloneWrap/$modelNo';

  ///克隆 完成
  static String cloneDone = '/amor/clone/done';

  ///克隆 详情
  static String cloneDetail(String modelNo) => '/amor/clone/list/$modelNo';

  ///克隆 编辑
  static String cloneEdit = '/amor/clone/edit';

  ///克隆 删除
  static String cloneDel = '/amor/clone/del';

  ///克隆 查询收益
  static String cloneEarnings = '/amor/user/pop-up';

  ///克隆 领取收益
  static String cloneEarningsClaim = '/amor/clone/claim-eran';

  /// 获取审核配置
  static const String initAudit = '/amor/common/isAudit';

  /// 提交 adjust ID
  static const String registerAdid = '/amor/user/register-adid';

  /// 编辑消息
  static const String updateMsg = '/amor/chat/updateMsg';

  /// 创建订单
  static const String orderCreate = '/amor/order/create';

  /// 订单失败
  static const String orderUpdateFail = '/amor/order/update-fail';

  /// 获取所有标签
  static const String tagsQuery = '/amor/tags/query';

  ///缓存昵称和头像
  static const String prepareProfile = '/amor/setting/prepare-profile';

  ///举报作者
  static String authorReport(String modelId) => '/amor/report/model/$modelId';

  ///屏蔽作者
  static String authorBlock(String modelId) => '/amor/user/black-list/save-by-model-id/$modelId';

  /// 记录每日登录
  static const String dailyReport = '/amor/active/user/active-using';

  /// 脱衣 获取价格等配置信息
  static const String unDressConfig = '/amor/noDressHistroy/getNoDressConfig';

  /// 脱衣 上传原始图片
  static const String undressLaunchTask = '/amor/noDressHistroy/undressLaunchTask';

  /// 脱衣 获取最终的结果数据
  static String undressTaskStatus(String taskId) =>
      '/amor/noDressHistroy/undressTaskStatus?taskId=$taskId';

  /// 语音通话扣费
  static const minusGems = '/amor/user/minusGems';

  /// moments列表
  static const momentsList = '/amor/moments/getAll';

  ///查询可视频、创建视频、图片的角色
  static const searchMedia = '/amor/model/search/media';

  ///解锁角色相册图片
  static const unlockPhoto = '/amor/model/unlock-special-pic';

  ///翻译消息
  static const sessionMessageTranslate = '/amor/translate';

  ///服装礼物配置
  static const getClothingConf = '/amor/getClothingConf';

  ///玩具礼物配置
  static const getGiftConf = '/amor/getGiftConf';

  ///服装礼物赠送
  static const clothesGive = '/amor/clothes';

  ///玩具礼物赠送
  static const giftGive = '/amor/gift';
}
