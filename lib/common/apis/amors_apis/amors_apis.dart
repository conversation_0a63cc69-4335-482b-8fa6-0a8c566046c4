import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_category_model.dart';
import 'package:amor_app/common/models/amors_models/amors_chats_model.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/models/amors_models/moment_model.dart';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';

class AmorsApis {
  ///首页推荐列表
  static Future<List<AmorsFeedModel>> getFeedList(int page,
      {Function(List<AmorsFeedModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.modelFeed,
      params: {'page': page, 'pageSize': appPageSize},
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack?.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return AmorsFeedModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return AmorsFeedModel.fromJson(e);
          }).toList();
  }

  //创建获取sessionNo
  static Future<Map?> createSession(
      {required int modelId, String? inviteCode, bool loding = true}) async {
    Map<String, dynamic> params = {'modelId': modelId, 'inviteCode': inviteCode};
    final resp = await ApiRequest.post(api: Api.modelCreatSession, params: params, loding: loding);
    return resp == null || resp.data == null ? null : resp.data!;
  }

  //模型点赞
  static Future<bool> doLike({int? modelId}) async {
    final resp =
        await ApiRequest.post(api: Api.dolike, params: {"modelId": modelId}, loding: false);
    return resp?.success ?? false;
  }

  //角色分类 类别列表
  static Future<List<AmorsCategoryModel>> getCategorys(
      {Function(List<AmorsCategoryModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.classification,
      loding: false,
      cacheCallBack: cacheCallBack == null
          ? null
          : (cacheResp) {
              cacheCallBack.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return AmorsCategoryModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return AmorsCategoryModel.fromJson(e);
          }).toList();
  }

  //分类列表数据
  static Future<List<AmorsFeedModel>> getCategoryDataList(
      {required String cateId,
      required int page,
      int? modelId,
      Function(List<AmorsFeedModel> cache)? cacheCallBack}) async {
    Map<String, dynamic> params = {'page': page, 'pageSize': appPageSize};
    if (cateId == '-2' && modelId != null) {
      params['modelId'] = modelId;
    }
    final resp = await ApiRequest.post(
      api: Api.categorysL2All(cateId),
      params: params,
      loding: false,
      cacheCallBack: page != 1 || cacheCallBack == null || cateId == '-2'
          ? null
          : (cacheResp) {
              cacheCallBack.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return AmorsFeedModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            // print((e as Map<String, dynamic>).values.toList());
            return AmorsFeedModel.fromJson(e);
          }).toList();
  }

  //随机获取角色
  static Future<List<AmorsFeedModel>> getModelRecommend() async {
    final resp = await ApiRequest.post(api: Api.modelRecommend, loding: false);
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return AmorsFeedModel.fromJson(e);
          }).toList();
  }

  //chat列表
  static Future<List<AmorsChatsModel>> amorsChatList(
      {required int page,
      int pageSize = appPageSize,
      required Function(List<AmorsChatsModel> cache) cacheCallBack}) async {
    Map<String, dynamic> params = {'page': page, 'pageSize': pageSize};
    final resp = await ApiRequest.post(
      api: Api.sessionList,
      params: params,
      loding: false,
      cacheCallBack: UserService.to.isLogin == false || page != 1
          ? null
          : (cacheResp) {
              cacheCallBack.call(sessionListData(cacheResp));
            },
    );
    return sessionListData(resp);
  }

  static List<AmorsChatsModel> sessionListData(BaseResponse? resp) {
    if (resp != null && resp.success!) {
      return (resp.data as List).map((e) {
        return AmorsChatsModel.fromJson(e);
      }).toList();
    }
    return [];
  }

  //amors搜索
  static Future<List<AmorsFeedModel>> amorsSearch(String keyword, String categorySource) async {
    final resp = await ApiRequest.post(
        api: Api.modelSearch,
        params: {'keyword': keyword, 'categorySource': categorySource},
        loding: false);
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return AmorsFeedModel.fromJson(e);
          }).toList();
  }

  //解锁角色
  static Future<bool> unlockModel({required int modelId, required String unlockType}) async {
    final resp = await ApiRequest.post(
        api: Api.unlockTight, params: {"modelId": modelId, 'unlockType': unlockType}, loding: true);
    return resp?.success ?? false;
  }

  //脱衣 上传原始图片
  static Future<Map?> unDressLaunchTask({
    String? sourceImage,
    required String style,
    String? modelId,
  }) async {
    final resp = await ApiRequest.post(
        api: Api.undressLaunchTask,
        params: {
          "modelId": modelId,
          'sourceImage': sourceImage,
          // 'fileName': '',
          // 'mask': '',
          'style': style
        },
        loding: true);
    return resp?.data;
  }

  //脱衣 获取最终的结果数据
  static Future<Map?> unDressTaskStatus({required String taskId, bool showLoding = false}) async {
    final resp = await ApiRequest.get(api: Api.undressTaskStatus(taskId), loding: showLoding);
    return resp?.data;
  }

  //脱衣 获取配置信息
  static Future<Map?> getUnDressConfig() async {
    final resp = await ApiRequest.get(api: Api.unDressConfig, loding: false);
    return resp?.data;
  }

  //moments列表数据
  static Future<List<MomentModel>> getMomentsDataList({
    Function(List<MomentModel> cache)? cacheCallBack,
    required int page,
  }) async {
    Map<String, dynamic> params = {
      'page': page,
      'pageSize': appPageSize,
      // false:A面 审核模式、true:B面
      'hideCharacter': !AppService.audit,
    };
    final resp = await ApiRequest.post(
      api: Api.momentsList,
      loding: false,
      params: params,
      cacheCallBack: (cacheResp) {
        cacheCallBack?.call(
          cacheResp == null || cacheResp.data == null
              ? []
              : (cacheResp.data as List).map((e) {
                  return MomentModel.fromJson(e);
                }).toList(),
        );
      },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return MomentModel.fromJson(e);
          }).toList();
  }

  //amors搜索
  static Future<List<AmorsFeedModel>> amorsSearchMedia(
      {bool? genVideo,
      bool? genImg,
      bool? videoCall,
      bool? clothing,
      required int page,
      required Function(List<AmorsFeedModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.searchMedia,
      params: {
        'keyword': '',
        'categorySource': 'main',
        'videoCall': videoCall,
        'genImg': genImg,
        'genVideo': genVideo,
        'clothing': clothing,
        'page': page,
        'pageSize': appPageSize,
      },
      loding: false,
      cacheCallBack: page != 1 || cacheCallBack == null
          ? null
          : (cacheResp) {
              cacheCallBack.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return AmorsFeedModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            // print((e as Map<String, dynamic>).keys.toList());
            // print((e as Map<String, dynamic>).values.toList());
            return AmorsFeedModel.fromJson(e);
          }).toList();
  }
}
