import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/inner_models/model_list_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';

class InnerApis {
  ///我喜欢的模型
  static Future<List<ModelListModel>> likedModelList(int page,
      {Function(List<ModelListModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.likedModel,
      params: {'page': page, 'pageSize': appPageSize},
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack?.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return ModelListModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return ModelListModel.fromJson(e);
          }).toList();
  }

  ///我聊过的的模型
  static Future<List<ModelListModel>> chatted(int page,
      {Function(List<ModelListModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.chatted,
      params: {'page': page, 'pageSize': appPageSize},
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack?.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return ModelListModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return ModelListModel.fromJson(e);
          }).toList();
  }

  //置顶
  static Future<bool> chattedTop({required int modelId, required bool top}) async {
    final resp = await ApiRequest.post(
        api: Api.chattedTop, params: {"modelId": modelId, 'top': top}, loding: false);
    return resp?.success ?? false;
  }
}
