import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/models/session_model/session_bg_model.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/models/session_model/session_report_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';

class SessionApis {
  //对话列表
  static Future<List<SessionModel>> getSessionMsgList(
      {required int page,
      int pageSize = appPageSize,
      required String sessionNo,
      required String sessionType,
      required Function(List<SessionModel> cache) cacheCallBack}) async {
    Map<String, dynamic> params = {
      'page': page,
      'pageSize': pageSize,
      'sessionNo': sessionNo,
    };
    final resp = await ApiRequest.post(
      api: sessionType == 'USER' ? Api.userChatRefresh : Api.chatRefresh,
      params: params,
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack.call(chatListData(cacheResp));
            },
    );
    return chatListData(resp);
  }

  static List<SessionModel> chatListData(BaseResponse? resp) {
    if (resp != null && resp.data != null) {
      return (resp.data as List).map((e) {
        return SessionModel.fromJson(e);
      }).toList();
    }
    return [];
  }

  //设置语音、翻译后的消息
  static Future<bool> setMsgVoice(
      {String? voice, required String msgId, int? duration, String? translateMessage}) async {
    Map<String, dynamic> params = {'msgId': msgId};
    if (voice != null) {
      params['voice'] = voice;
    }
    if (translateMessage != null) {
      params['translateContent'] = translateMessage;
    }
    if (duration != null) {
      params['duration'] = duration;
    }
    final resp = await ApiRequest.post(api: Api.setMsgVoice, params: params, loding: false);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //会话配置
  static Future<Map<String, dynamic>?> getSessionConfig({
    required String sessionNo,
    required Function(Map<String, dynamic>? cache) cacheCallBack,
  }) async {
    Map<String, dynamic> params = {'sessionNo': sessionNo};
    final resp = await ApiRequest.post(
      api: Api.sessionConfig,
      params: params,
      loding: false,
      cacheCallBack: (cacheResp) {
        cacheCallBack.call(cacheResp?.data);
      },
    );
    if (resp != null) {
      return resp.data;
    }
    return null;
  }

  //动作提示
  static Future<List> chatActionTips(
      {required int modelId, required String contentCategory, required String sessionNo}) async {
    Map<String, dynamic> params = {
      'modelId': modelId,
      'contentCategory': contentCategory,
      'sessionNo': sessionNo
    };
    final resp = await ApiRequest.post(api: Api.chatActionTips, params: params, loding: false);
    return resp == null || resp.data == null ? [] : resp.data;
  }

  //获取动作提示剩余次数、倒计时
  static Future<Map> chatActionTipsGet() async {
    final resp = await ApiRequest.get(api: Api.chatActionTipsSet, loding: false);
    return resp?.data;
  }

  //看完广告增加提示次数
  static Future<bool> chatActionTipsSet() async {
    final resp = await ApiRequest.post(api: Api.chatActionTipsSet, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //消息重新生成
  static Future<bool> chatRegenerate({required String sessionNo, required String msgId}) async {
    Map<String, dynamic> params = {'sessionNo': sessionNo, 'msgId': msgId};
    final resp = await ApiRequest.post(api: Api.chatRegenerate, params: params, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //聊天消息重新生成
  static Future<bool> chatScore({required String msgId, required int score}) async {
    Map<String, dynamic> params = {'msgId': msgId, 'score': score};
    final resp = await ApiRequest.post(api: Api.chatScore, params: params, loding: false);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //nsfw开关
  static Future<bool> nsfwSwitch(
      {required String sessionNo, int? modelId, required String contentCategory}) async {
    Map<String, dynamic> params = {
      'sessionNo': sessionNo,
      'modelId': modelId,
      'contentCategory': contentCategory
    };
    final resp = await ApiRequest.post(api: Api.nsfwSwitch, params: params, loding: false);
    if (resp != null && resp.success == true) {
      return true;
    }
    return false;
  }

  //重置会话
  static Future<bool> sessionReset({required String sessionNo}) async {
    Map<String, dynamic> params = {'sessionNo': sessionNo};
    final resp = await ApiRequest.post(api: Api.sessionReset, params: params, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //会话背景列表
  static Future<SessionBgModel?> sessionChatBgList(
      {required int modelId, required Function(SessionBgModel? cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
      api: Api.sessionChatBgList(modelId),
      loding: false,
      cacheCallBack: (cacheResp) {
        if (cacheResp != null && cacheResp.data != null) {
          cacheCallBack.call(SessionBgModel.fromJson(cacheResp.data));
        }
      },
    );
    if (resp != null && resp.data != null) {
      return SessionBgModel.fromJson(resp.data);
    }
    return null;
  }

  //会话设置(背景图)
  static Future<bool> sessionChatSet({required Map<String, dynamic> params}) async {
    final resp = await ApiRequest.post(api: Api.sessionChatSet, params: params, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //购买背景
  static Future<Map?> bgPurchase({
    required int modelId,
    required String type,
    String? url,
    String? bgId,
  }) async {
    Map<String, dynamic> params = {'bgId': bgId, 'url': url, 'modelId': modelId, 'type': type};
    final resp = await ApiRequest.post(api: Api.bgPurchase, params: params);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //选择背景
  static Future<bool> bgActivate({required int modelId, String? id, String? msgId}) async {
    Map<String, dynamic> params = {'modelId': modelId, 'id': id, 'msgId': msgId};
    final resp = await ApiRequest.post(api: Api.bgActivate, params: params, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //举报配置列表
  static Future<List<ReportInfoModel>> reportInfo() async {
    final resp = await ApiRequest.get(api: Api.reportInfo);
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return ReportInfoModel.fromJson(e);
          }).toList();
  }

  //举报
  static Future<bool> reportPromble({
    required List ids,
    required String source,
    required String text,
    required String type,
    required String value,
  }) async {
    Map<String, dynamic> params = {
      'ids': ids,
      'source': source,
      'text': text,
      'type': type,
      'value': value,
    };
    final resp = await ApiRequest.post(api: Api.reportPromble, params: params, loding: false);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

//角色详情
  static Future<AmorsFeedModel?> getModelProfile(
      {required String modelId, required Function(AmorsFeedModel? cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
        api: Api.getModelProfile(modelId),
        cacheCallBack: (cacheResp) {
          // print((cacheResp!.data as Map).keys.toList());
          // print((cacheResp!.data as Map).values.toList());
          // print(11111111);
          // print((cacheResp!.data as Map)['genImg']);
          cacheCallBack.call(cacheResp == null || cacheResp.data == null
              ? null
              : AmorsFeedModel.fromJson(cacheResp.data));
        },
        loding: false);
    return resp == null || resp.data == null ? null : AmorsFeedModel.fromJson(resp.data);
  }

  //聊天消息重新生成
  static Future<bool> updateMsg(
      {required String msgId, required String content, required String contentType}) async {
    Map<String, dynamic> params = {'msgId': msgId, 'contentType': contentType, 'content': content};
    final resp = await ApiRequest.post(api: Api.updateMsg, params: params, loding: true);
    if (resp != null && resp.success!) {
      return true;
    }
    return false;
  }

  //语音通话扣费
  static Future<bool> voiceCallGems({required int gems}) async {
    String? signature = CommonUtil.getApiSignature();
    if (signature == null) {
      return false;
    }
    final resp = await ApiRequest.post(
        api: Api.minusGems, params: {'signature': signature, 'gems': gems}, loding: false);
    return resp?.success ?? false;
  }

  ///解锁角色相册图片
  static Future<bool> unlockPhoto({required int modelId, required String imgId}) async {
    Map<String, dynamic> params = {'modelId': modelId, 'imgId': imgId};
    final resp = await ApiRequest.post(api: Api.unlockPhoto, params: params, loding: true);
    return resp?.success ?? false;
  }

  //翻译消息
  static Future<String?> sessionMessageTranslate(
      {required String tarLang, required String souLang, required String text}) async {
    Map<String, dynamic> params = {
      'targetLanguage': tarLang,
      'sourceLanguage': souLang,
      'content': text
    };
    final resp =
        await ApiRequest.post(api: Api.sessionMessageTranslate, params: params, loding: true);
    return resp?.data;
  }

  //服装礼物配置
  static Future<List> getClothingConf({required Function(List cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
      api: Api.getClothingConf,
      loding: false,
      cacheCallBack: (cacheResp) {
        cacheCallBack.call(cacheResp == null || cacheResp.data == null ? [] : cacheResp.data);
      },
    );
    return resp?.data ?? [];
  }

  //玩具礼物配置
  static Future<List> getGiftConf({required Function(List cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
      api: Api.getGiftConf,
      loding: false,
      cacheCallBack: (cacheResp) {
        cacheCallBack.call(cacheResp == null || cacheResp.data == null ? [] : cacheResp.data);
      },
    );
    return resp?.data ?? [];
  }

  ///赠送服装
  static Future<List<SessionModel>> clothesGive(
      {required int clothingId, required int modelId, required String sessionNo}) async {
    Map<String, dynamic> params = {'id': clothingId, 'modelId': modelId, 'sessionNo': sessionNo};
    final resp = await ApiRequest.post(api: Api.clothesGive, params: params, loding: true);
    if (resp != null && resp.data != null) {
      return (resp.data as List).map((e) {
        return SessionModel.fromJson(e);
      }).toList();
    }
    return [];
  }

  ///赠送玩具
  static Future<List<SessionModel>> giftGive(
      {required int clothingId, required int modelId, required String sessionNo}) async {
    Map<String, dynamic> params = {'id': clothingId, 'modelId': modelId, 'sessionNo': sessionNo};
    final resp = await ApiRequest.post(api: Api.giftGive, params: params, loding: true);
    if (resp != null && resp.data != null) {
      return (resp.data as List).map((e) {
        return SessionModel.fromJson(e);
      }).toList();
    }
    return [];
  }
}
