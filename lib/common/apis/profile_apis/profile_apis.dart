import 'dart:io';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/notification_model.dart';
import 'package:amor_app/common/models/profile_model/profile_bind_model.dart';
import 'package:amor_app/common/models/profile_model/profile_task.dart';
import 'package:amor_app/common/models/profile_model/purchase_config_model.dart';
import 'package:amor_app/common/models/profile_model/user_info_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';

class ProfileApis {
  //用户信息
  static Future<UserInfo?> getUserInfo({Function(UserInfo? cache)? cacheCallBack}) async {
    final resp = await ApiRequest.get(
        api: Api.userInfo,
        cacheCallBack: cacheCallBack != null
            ? (cacheResp) {
                if (cacheResp != null && cacheResp.data != null) {
                  cacheCallBack.call(UserInfo.fromJson(cacheResp.data));
                }
              }
            : null,
        loding: false);
    return resp == null || resp.data == null ? null : UserInfo.fromJson(resp.data!);
  }

  ///注销账户
  static Future<bool> delAccount() async {
    final resp = await ApiRequest.post(api: Api.unsubscribe);
    return resp == null ? false : resp.success!;
  }

  ///退出登录
  static Future<bool> logout() async {
    final resp = await ApiRequest.post(api: Api.authLogout, loding: true);
    return resp == null ? false : resp.success!;
  }

  ///查询第三方或邮箱、手机绑定
  static Future<List<BindInfoModel>> accountBindGet() async {
    final resp = await ApiRequest.get(api: Api.accountBindGet, loding: false);
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return BindInfoModel.fromJson(e);
          }).toList();
  }

  ///用户个人信息修改
  static Future<bool> profileUpdate(
      {required Map<String, dynamic> params, bool loading = true}) async {
    final resp = await ApiRequest.post(
      api: Api.profileUpdate,
      params: params,
      loding: loading,
    );
    return resp == null ? false : resp.success!;
  }

  ///绑定邮箱或者手机
  static Future<bool> bindPhoneOrEmail(
      {required String account,
      required String code,
      required String sign,
      required String bindType}) async {
    final resp = await ApiRequest.post(
        api: Api.bindPhoneOrEmail,
        params: {'account': account, 'code': code, 'sign': sign, 'bindType': bindType},
        loding: true);
    if (resp != null && resp.success == true) {
      return true;
    }
    return false;
  }

  ///绑定三方
  static Future<bool> bindOauth({required String thirdToken, required String thirdType}) async {
    final resp = await ApiRequest.post(
        api: Api.bindOauth,
        params: {'thirdType': thirdType, 'thirdToken': thirdToken},
        loding: true);
    if (resp != null && resp.success == true) {
      return true;
    }
    return false;
  }

  ///消息通知列表
  static Future<List<NotficationModel>> noticeList(int page,
      {Function(List<NotficationModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.noticeList,
      params: {'page': page, 'pageSize': appPageSize},
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack?.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return NotficationModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return NotficationModel.fromJson(e);
          }).toList();
  }

  ///任务列表
  static Future<TaskModel?> taskBoard({required Function(TaskModel? cache) cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.missionBoard,
      loding: false,
      cacheCallBack: (cacheResp) {
        cacheCallBack.call(cacheResp == null || cacheResp.data == null
            ? null
            : TaskModel.fromJson(cacheResp.data));
      },
    );
    // (resp?.data as Map).forEach((key, value) {
    //   print(key);
    //   print(value);
    // });
    return (resp == null || resp.data == null) ? null : TaskModel.fromJson(resp.data);
  }

  //领取任务奖励
  static Future<Map?> taskClaim({required int id, required String claimType}) async {
    final resp = await ApiRequest.post(
        api: Api.missionClaim, params: {'id': id, 'claimType': claimType}, loding: true);
    return resp?.data;
  }

  //完成评分任务
  static Future<bool> taskTheFun() async {
    final resp = await ApiRequest.post(api: Api.missionTheFun, loding: true);
    return resp == null ? false : resp.success!;
  }

  //任务跳转聊天页 获取sessionNo
  static Future<Map?> taskSessionNo() async {
    final resp = await ApiRequest.get(api: Api.missionSessionNo);
    return resp?.data;
  }

  //领取任务奖励
  static Future<Map?> notificationClaim({required int msgId}) async {
    final resp =
        await ApiRequest.post(api: Api.notificationClaim, params: {'msgId': msgId}, loding: true);
    return resp?.data;
  }

  ///购买会员配置
  static Future<PurchaseConfigModel?> getPurchaseVipConfig(
      {required Function(PurchaseConfigModel? cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
        api: Api.rechargeConfigVip,
        cacheCallBack: (cacheResp) {
          if (cacheResp != null && cacheResp.data != null) {
            cacheCallBack.call(PurchaseConfigModel.fromJson(cacheResp.data));
          }
        },
        loding: false);
    return resp == null || resp.data == null ? null : PurchaseConfigModel.fromJson(resp.data);
  }

  ///购买宝石配置
  static Future<PurchaseConfigModel?> getPurchaseGemsConfig(
      {required Function(PurchaseConfigModel? cache) cacheCallBack}) async {
    final resp = await ApiRequest.get(
        api: Api.rechargeConfigGem,
        cacheCallBack: (cacheResp) {
          if (cacheResp != null && cacheResp.data != null) {
            cacheCallBack.call(PurchaseConfigModel.fromJson(cacheResp.data));
          }
        },
        loding: false);
    return resp == null || resp.data == null ? null : PurchaseConfigModel.fromJson(resp.data);
  }

  ///内购验证
  static Future<Map?> inAppPurchaseVerify({required Map<String, dynamic> params}) async {
    String url = '';
    if (Platform.isIOS) {
      if (params['type'] == 1) {
        //ios内购
        url = Api.iosGemVerify;
      } else {
        //ios订阅
        url = Api.iosVipVerify;
      }
    }
    if (Platform.isAndroid) {
      if (params['type'] == 1) {
        url = Api.googleGemVerify;
      } else {
        url = Api.googleVipVerify;
      }
    }
    final resp = await ApiRequest.post(api: url, params: params, loding: false);
    return (resp == null || resp.data == null) ? null : resp.data;
  }

  ///创建购买订单
  static Future<Map?> orderCreate(
      {required String goodsNo,
      required String currency,
      required String payType,
      required double payAmount}) async {
    Map<String, dynamic> params = {
      'goodsNo': goodsNo,
      'payAmount': payAmount,
      'payType': payType,
      'currency': currency
    };
    final resp = await ApiRequest.post(api: Api.orderCreate, params: params, loding: false);
    if (resp != null && resp.data != null) {
      return resp.data;
    }
    return null;
  }

  ///订单失败
  static Future<Map?> orderUpdateFail({required String orderNo, required String errMsg}) async {
    Map<String, dynamic> params = {'orderNo': orderNo, 'errMsg': errMsg};
    final resp = await ApiRequest.post(api: Api.orderUpdateFail, params: params);
    if (resp != null && resp.data != null) {
      return resp.data;
    }
    return null;
  }
}
