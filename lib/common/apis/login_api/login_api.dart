import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/login_models/register_model.dart';
import 'package:amor_app/common/utils/utils.dart';

class LoginApi {
  /// 获取邮箱登录验证码
  static Future<String?> fetchLoginCodeByEmail(
      {required String email, required String captchaUse}) async {
    final response = await ApiRequest.post(
      api: Api.loginEmailSend,
      params: {'email': email, 'captchaUse': captchaUse},
    );
    return response == null || response.data == null
        ? null
        : response.data["sign"];
  }

  /// 邮箱验证码登录
  static Future<Map<String, dynamic>?> loginByEmail(
      {required String authCode,
      required String email,
      required String sign,
      required String loginAction}) async {
    final response = await ApiRequest.post(
        api: Api.loginEmail,
        params: {
          'code': authCode,
          'email': email,
          'sign': sign,
          'loginAction': loginAction
        },
        loding: true);
    return response == null || response.data == null ? null : response.data;
  }

  //第三方登录
  static Future<Map<String, dynamic>?> oauthLogin(
      {String? thirdToken,
      required String thirdType,
      required String loginAction}) async {
    Map<String, dynamic> params = {
      'thirdToken': thirdToken,
      'thirdType': thirdType,
      'loginAction': loginAction
    };
    final resp = await ApiRequest.post(api: Api.oauthLogin, params: params);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //验证码登录 发送短信验证码
  static Future<Map<String, dynamic>?> sendSmsCode(
      {required String phone,
      required int timestamp,
      required String captchaUse}) async {
    Map<String, dynamic> params = {
      'phone': phone,
      'captchaUse': captchaUse,
      'timestamp': timestamp,
      'secret': CommonUtil.aesTimestamp(timestamp, phone)
    };
    final resp =
        await ApiRequest.post(api: Api.sendSms, params: params, loding: true);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //短信验证码登录
  static Future<Map<String, dynamic>?> smsCodeLogin(
      {required String phone,
      required String code,
      required String loginAction}) async {
    Map<String, dynamic> params = {
      'phone': phone,
      'code': code,
      'loginAction': loginAction,
    };
    final resp = await ApiRequest.post(
        api: Api.smsCodeLogin, params: params, loding: true);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //生成昵称
  static Future<Map<String, dynamic>?> genNickname(
      {required int gender}) async {
    final resp =
        await ApiRequest.post(api: Api.genNickname(gender), loding: false);
    return resp == null || resp.data == null ? null : resp.data;
  }

  ///注册后设置昵称和性别
  static Future<bool> initUser(
      {required int gender, String? nickname, String? avatar}) async {
    Map<String, dynamic> params = {
      'gender': gender,
      'nickname': nickname,
      'avatar': avatar
    };
    final resp =
        await ApiRequest.post(api: Api.initUser, params: params, loding: true);
    return resp == null ? false : resp.success ?? false;
  }

  // 获取注册需要填写的信息
  static Future<List<RegisterModel>> registerInfoGet() async {
    final resp = await ApiRequest.post(api: Api.questionGet, loding: false);
    if (resp != null && resp.data != null) {
      return (resp.data as List).map((e) {
        return RegisterModel.fromJson(e);
      }).toList();
    }
    return [];
  }

  //提交问题
  static Future<Map<String, dynamic>?> questionCommit(
      {required List questionItemReqs}) async {
    Map<String, dynamic> params = {'questionItemReqs': questionItemReqs};
    final resp = await ApiRequest.post(
        api: Api.questionCommit, params: params, loding: true);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //设备登录
  static Future<Map<String, dynamic>?> deviceLogin() async {
    final resp = await ApiRequest.post(api: Api.loginDevice, loding: false);
    return resp == null || resp.data == null ? null : resp.data;
  }

  //验证账号是否注册
  static Future<Map<String, dynamic>?> authAccountExist(
      {required String thirdToken, required String thirdType}) async {
    final resp = await ApiRequest.post(
        api: Api.authAccountExist,
        params: {'thirdType': thirdType, 'thirdToken': thirdToken},
        loding: true);
    return resp == null || resp.data == null ? null : resp.data;
  }
}
