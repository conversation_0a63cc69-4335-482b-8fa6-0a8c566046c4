import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/clone_model/clone_list_model.dart';
import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/models/clone_model/voice_bank_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';

class CloneApis {
  //设置基础信息
  static Future<bool> cloneInit({
    String? avatar,
    String? bgImg,
    required String gender,
    required String nickname,
    required String modelNo,
    required int recordOptions,
  }) async {
    Map<String, dynamic> params = {
      'gender': gender,
      'modelNo': modelNo,
      'nickname': nickname,
      'recordOptions': recordOptions
    };
    if (avatar != null) {
      params['avatar'] = avatar;
    }
    if (bgImg != null) {
      params['bgImg'] = bgImg;
    }
    final resp =
        await ApiRequest.post(api: Api.cloneInit, params: params, loding: true);
    return resp?.success ?? false;
  }

  //克隆 随机昵称
  static Future<String> cloneRandomProfile({required int gender}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneRandomProfile, params: {'gender': gender}, loding: true);
    return (resp == null || resp.data == null)
        ? ''
        : resp.data['nickname'] ?? '';
  }

  //克隆 进度
  static Future<CloneCreatModel?> cloneProcess(
      {required String modelNo}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneProcess, params: {'modelNo': modelNo}, loding: false);
    if (resp != null && resp.data != null) {
      return CloneCreatModel.fromJson(resp.data);
    }
    return null;
  }

  //克隆 声音库
  static Future<VoiceBankModel?> cloneVoiceBank() async {
    final resp = await ApiRequest.post(api: Api.cloneVoiceBank, loding: true);
    if (resp != null && resp.data != null) {
      return VoiceBankModel.fromJson(resp.data);
    }
    return null;
  }

  //克隆 重新生成答案
  static Future<Map?> cloneRegenerateAnswer(
      {required int id, required String modelNo}) async {
    Map<String, dynamic> params = {'id': id, 'modelNo': modelNo};
    final resp = await ApiRequest.post(
        api: Api.cloneRegenerateAnswer, params: params, loding: false);
    if (resp != null && resp.data != null) {
      return resp.data;
    }
    return null;
  }

  //克隆 保存进度
  static Future<bool> cloneSaveProcess({
    String? audit,
    required String cloneModelProcess,
    String? voiceTypeEnum,
    String? voiceName,
    required String modelNo,
    List? questions,
    bool showLoading = true,
  }) async {
    Map<String, dynamic> params = {
      'audit': audit,
      'cloneModelProcess': cloneModelProcess,
      'voiceTypeEnum': voiceTypeEnum,
      'modelNo': modelNo,
      'questions': questions,
      'voiceName': voiceName,
    };
    final resp = await ApiRequest.post(
        api: Api.cloneSaveProcess, params: params, loding: showLoading);
    return resp?.success ?? false;
  }

  ///我聊过的的模型
  static Future<List<CloneListModel>> cloneList(
      {required int page,
      Function(List<CloneListModel> cache)? cacheCallBack}) async {
    final resp = await ApiRequest.post(
      api: Api.cloneList,
      params: {'page': page, 'pageSize': appPageSize},
      loding: false,
      cacheCallBack: page != 1
          ? null
          : (cacheResp) {
              cacheCallBack?.call(
                cacheResp == null || cacheResp.data == null
                    ? []
                    : (cacheResp.data as List).map((e) {
                        return CloneListModel.fromJson(e);
                      }).toList(),
              );
            },
    );
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return CloneListModel.fromJson(e);
          }).toList();
  }

  //克隆 刷新
  static Future<bool> cloneRefresh({required String modelNo}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneRefresh, params: {'modelNo': modelNo}, loding: true);
    return resp?.success ?? false;
  }

  //克隆 预览信息
  static Future<CloneInfoModel?> cloneWrap({required String modelNo}) async {
    final resp =
        await ApiRequest.get(api: Api.cloneWrap(modelNo), loding: false);
    if (resp != null && resp.data != null) {
      return CloneInfoModel.fromJson(resp.data);
    }
    return null;
  }

  //克隆 完成
  static Future<Map?> cloneDone(
      {required String modelNo,
      required int purview,
      required List tags}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneDone,
        params: {'modelNo': modelNo, 'purview': purview, 'tags': tags},
        loding: true);
    if (resp != null) {
      return resp.data;
    }
    return null;
  }

  //克隆 详情
  static Future<CloneEditModel?> cloneDetail({required String modelNo}) async {
    final resp =
        await ApiRequest.get(api: Api.cloneDetail(modelNo), loding: false);
    if (resp != null && resp.data != null) {
      return CloneEditModel.fromJson(resp.data);
    }
    return null;
  }

  //克隆 删除
  static Future<bool> cloneDel({required String modelNo}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneDel, params: {'modelNo': modelNo}, loding: true);
    return resp?.success ?? false;
  }

  //克隆 编辑
  static Future<bool> cloneEdit(
      {required int purview,
      required String greeting,
      required String modelNo,
      required String dialog,
      required String scenario,
      required List bgImage,
      required List tags}) async {
    Map<String, dynamic> params = {
      'bgImage': bgImage,
      'modelNo': modelNo,
      'purview': purview,
      'greeting': greeting,
      'dialog': dialog,
      'scenario': scenario,
      'tags': tags,
    };
    final resp =
        await ApiRequest.post(api: Api.cloneEdit, params: params, loding: true);
    return resp?.success ?? false;
  }

  //克隆 查询收益
  static Future<Map?> cloneEarnings() async {
    final resp = await ApiRequest.post(api: Api.cloneEarnings, loding: false);
    return resp == null || resp.data == null ? null : resp.data!;
  }

  //克隆 领取收益
  static Future<bool> cloneEarningsClaim({required bool ad}) async {
    final resp = await ApiRequest.post(
        api: Api.cloneEarningsClaim, params: {'ad': ad}, loding: true);
    return resp?.success ?? false;
  }
}
