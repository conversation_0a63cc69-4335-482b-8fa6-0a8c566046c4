name: amor_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.3.0+18

environment:
  sdk: '>=3.0.0 <4.0.0'
  
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  # -- 持久化工具
  shared_preferences: ^2.2.2
  # -- 网络请求
  dio: ^5.4.0
  # -- 网络请求日志打印
  pretty_dio_logger: ^1.3.1
  # -- GetX
  get: ^4.6.6
  # --屏幕适配
  flutter_screenutil: ^5.9.3
  # -- 轻提示
  flutter_easyloading: ^3.0.5
  # -- 获取应用信息
  package_info_plus: ^8.1.2
  # -- 获取设备信息
  device_info_plus: ^11.0.0
  # --键盘监听
  flutter_keyboard_visibility: ^6.0.0
  # -- 创建数据模型
  json_annotation: ^4.8.1
  # -- 下拉刷新
  easy_refresh: ^3.3.4
  # -- 图片缓存
  cached_network_image: ^3.3.1
  # -- 设备权限
  permission_handler: ^11.1.0
  # -- 懒加载IndexedStack
  lazy_load_indexed_stack: ^1.1.0
  # -- 选择照片
  image_picker: ^1.0.7
  # -- 网络监听
  connectivity_plus: ^6.0.2
  # -- socket
  web_socket_channel: ^3.0.0
  # -- 列表定位
  scrollable_positioned_list: ^0.3.8
  # -- 音频播放
  just_audio: ^0.9.36
  # -- 阿里云oss上传
  flutter_oss_aliyun: ^6.4.1
  # --数据库
  sqflite: ^2.3.0
  # --本地存储路径
  path_provider: ^2.1.2
  path: ^1.8.3
  # --富文本
  easy_rich_text: ^2.1.0
  # --分享
  share_plus: ^10.0.0
  # -- uuid
  device_uuid: ^0.0.4
  # --安全存储
  flutter_keychain: ^2.4.0
  # -- 加解密
  encrypt: ^5.0.3
  # --本地格式化
  intl: ^0.19.0
  # --数字动画
  animated_flip_counter: ^0.3.4
  # -- 富文本输入框
  extended_text_field: ^16.0.0
  # -- lottie动画
  lottie: ^3.1.0
  # -- 轮播图
  # card_swiper: ^3.0.1
  # --小红点角标
  badges: ^3.1.2
  # -- 启动图
  flutter_native_splash: ^2.3.9
  # -- 画形状  （虚线）
  dotted_border: ^2.1.0
  # -- 照片裁剪
  image_cropper: ^9.0.0
  # --评分
  flutter_rating_bar: ^4.0.1
  # -- webview
  webview_flutter: ^4.4.3
  # --验证码输入框
  pinput: ^5.0.0
  # -- 倒计时动画
  slide_countdown: ^2.0.0
  # --APPLE 登录
  sign_in_with_apple: ^6.1.1
  # --App应用市场评分
  in_app_review: ^2.0.8
  # --启动URL
  url_launcher: ^6.2.3
  # -- 精度计算
  decimal: ^3.0.2
  # -- 内购
  in_app_purchase: ^3.2.3
  #firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.0
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3
  firebase_remote_config: ^5.1.3
  firebase_crashlytics: ^4.1.5
  #本地通知
  # flutter_local_notifications: ^17.0.0
  # --骨架屏
  shimmer: ^3.0.0
  # -- Widget转图片
  widgets_to_image: ^1.0.0
  custom_image_crop: ^0.0.13
  # --facebook 登录
  flutter_facebook_auth: ^7.1.1
  # --本机文件资源管理器
  file_picker: ^8.0.0+1
  # --录音
  record: 6.0.0
  # --广告
  # applovin_max: ^4.0.1
  # --google 登录
  google_sign_in: ^6.2.1
  # --adjust
  adjust_sdk: ^5.0.2
    # --可监听滚动组件
  scrollview_observer: ^1.19.1
  # --瀑布流
  flutter_staggered_grid_view: ^0.7.0
  # --渐变色边框
  gradient_borders: ^1.0.0
  # --视频播放
  video_player: ^2.9.2
  # --图片差异操作
  before_after: ^3.1.0
  # --语音转文字
  speech_to_text: ^7.0.0
  # --震动
  vibration: ^2.0.1
  # --外部跳转
  app_links: ^6.3.2


  
  
  # --获取gaid、idfa等
  advertising_id: ^2.6.0
  # --时区 ip地址等
  public_ip_address: ^1.1.0+2
  # --运营商信息(敏感权限问题，所以取消)
  # carrier_info: ^2.0.8 
  # --生成uuid
  uuid: ^4.3.2
  # --android_id
  android_id: ^0.4.0
  # --Android Play Install Referrer API 
  android_play_install_referrer: ^0.4.0
  # -- 获取user_agent
  # fk_user_agent: ^2.1.0
  # --提供对 Android 原生 PackageManager API 的访问
  # android_package_manager: ^0.7.1





  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  # -- 创建数据模型 工具
  json_serializable: ^6.7.1
  # -- 提供了一些用于生成文件的通用命令
  build_runner: ^2.4.7
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/lottie/
    - assets/fonts/
    - assets/lottie/klone_scan/
    - assets/lottie/klone_scan/images/
    - assets/lottie/amor_paid/
    - assets/lottie/amor_paid/images/
    - assets/lottie/sign_gem_animated/
    - assets/lottie/sign_gem_animated/images/
    - assets/lottie/task_gem_animated/
    - assets/lottie/task_gem_animated/images/
    - assets/lottie/role_unlock_anim/
    - assets/lottie/role_unlock_anim/images/
    - assets/lottie/subscribe_success/
    - assets/lottie/subscribe_success/image/

    
    
  fonts:
    - family: BeVietnamPro
      fonts:
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Black.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-BlackItalic.ttf
          style: italic
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Bold.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-BoldItalic.ttf
          style: italic
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-ExtraBold.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-ExtraBoldItalic.ttf
          style: italic       
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-ExtraLight.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-ExtraLightItalic.ttf
          style: italic  
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Italic.ttf
          style: italic  
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Light.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-LightItalic.ttf
          style: italic  
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Medium.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-MediumItalic.ttf
          style: italic  
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Regular.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-SemiBold.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-SemiBoldItalic.ttf
          style: italic                                          
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Thin.ttf
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-ThinItalic.ttf
          style: italic 

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_assets:
  assets_path: assets/images/
  output_path: lib/common/values/